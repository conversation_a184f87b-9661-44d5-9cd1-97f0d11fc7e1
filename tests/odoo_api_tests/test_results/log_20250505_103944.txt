2025-05-05 10:39:44,267 - INFO - Loading configuration from config/test_config.json
2025-05-05 10:39:44,267 - DEBUG - Config loaded: {'server_url': 'https://stairmaster18.odoo-sandbox.com', 'database': 'STAIRMASTER_18_24032025', 'client_id': 'ZqUAbvS6PIcOobKIjz4G4OuaKgm6pK9cpcpxBz1p', 'client_secret': 'ZDfR6WHORSfrGSl424G9zNu5yXhfle6PRMGpC69M', 'username': 'ptadmin', 'password': '++Uke52br++', 'auth_endpoint': '/api/v2/authentication/oauth2/token', 'models_to_test': ['res.partner', 'sale.order'], 'output_dir': 'test_results'}
2025-05-05 10:39:44,267 - INFO - Obtaining access token
2025-05-05 10:39:44,267 - DEBUG - Requesting access token from https://stairmaster18.odoo-sandbox.com/api/v2/authentication/oauth2/token
2025-05-05 10:39:45,175 - DEBUG - Access token obtained successfully
2025-05-05 10:39:45,175 - INFO - Testing Company Information
2025-05-05 10:39:45,175 - DEBUG - Testing GET endpoint /api/v2/company
2025-05-05 10:39:45,707 - DEBUG - Endpoint /api/v2/company succeeded with response: {
  "allowed_companies": [
    [
      2,
      "AU Company"
    ],
    [
      1,
      "Stairmaster"
    ]
  ],
  "current_company": [
    1,
    "Stairmaster"
  ],
  "current_company_id": 1
}
2025-05-05 10:39:45,707 - INFO - Testing Database
2025-05-05 10:39:45,707 - DEBUG - Testing GET endpoint /api/v2/database
2025-05-05 10:39:46,202 - DEBUG - Endpoint /api/v2/database succeeded with response: {
  "database": "STAIRMASTER_18_24032025"
}
2025-05-05 10:39:46,202 - INFO - Testing Modules
2025-05-05 10:39:46,202 - DEBUG - Testing GET endpoint /api/v2/modules
2025-05-05 10:39:46,733 - DEBUG - Endpoint /api/v2/modules succeeded with response: "{'document_classification_groq', 'uom', 'project', 'partner_autocomplete', 'hr_timesheet_attendance', 'project_stock_account', 'project_todo', 'stairbiz_detailing', 'project_mrp_account', 'stairbiz_standards', 'acs_documents_preview', 'sale_purchase_stock', 'hr_attendance', 'mail_bot_hr', 'crm_iap_mine', 'project_hr_expense', 'web_split_view', 'social_media', 'fleet', 'stairmaster_website', 'mail', 'itms_myob_integration', 'stairmaster_sale', 'sale_management', 'gamification', 'sale_async_emails', 'survey', 'base_import_module', 'mail_bot', 'account_fleet', 'barcodes', 'wk_wizard_messages', 'sale_expense', 'sign_oca', 'project_stock', 'website_crm_livechat', 'website_livechat', 'hr_expense', 'website', 'web_editor', 'stock', 'purchase', 'spreadsheet', 'auth_totp_mail', 'contact_address_google_place', 'itms_partner_firstname', 'hr_livechat', 'project_purchase', 'digest', 'sale', 'itms_general', 'spreadsheet_dashboard_stock_account', 'crm', 'sms', 'gamification_sale_crm', 'web_tour', 'calendar', 'sale_service', 'account_edi_ubl_cii', 'spreadsheet_dashboard_account', 'spreadsheet_dashboard_hr_timesheet', 'spreadsheet_dashboard_sale', 'snailmail', 'purchase_stock', 'stairmaster_sla', 'website_payment', 'google_recaptcha', 'auth_signup', 'stairmaster_contact_extened', 'itms_turn_off_bubble', 'payment', 'stairmaster_abn_lookup', 'project_sale_expense', 'auth_totp', 'web_widget_dropdown_dynamic', 'hr_hourly_cost', 'hr_org_chart', 'sale_edi_ubl', 'rating', 'calendar_sms', 'spreadsheet_dashboard_sale_timesheet', 'base_import', 'stairmaster_install_address', 'project_account', 'project_purchase_stock', 'sales_team', 'sale_crm', 'sale_purchase', 'spreadsheet_account', 'website_crm', 'stock_account', 'stairmaster_job_entry', 'odoo_cloud_storage', 'base_automation', 'eg_crm_lead_checklist', 'queue_job', 'google_gmail', 'website_project', 'product_showcase', 'project_mrp_sale', 'calculators', 'stairbiz_integration', 'base_setup', 'project_mrp', 'resource', 'sale_sms', 'base_geolocalize', 'iap_mail', 'ondao_flexible_chatter', 'sale_timesheet', 'muk_web_appsbar', 'delivery', 'utm', 'stairmaster_inspection', 'bus', 'stairbiz_quotes', 'product', 'l10n_au', 'web_unsplash', 'purchase_edi_ubl_bis3', 'hr_gamification', 'base_install_request', 'sale_purchase_project', 'iap_crm', 'sale_project_stock', 'iap', 'stairmaster_crm_stairbiz', 'portal_rating', 'website_sms', 'crm_iap_enrich', 'hr_skills_survey', 'lone_worker', 'crm_check_approve_limiter', 'base_sparse_field', 'html_editor', 'crm_livechat', 'project_hr_skills', 'odoo_forge_sale_map', 'stairmaster_measure_sheet', 'web', 'privacy_lookup', 'rt_activity_mgmt', 'stairbiz_servers', 'spreadsheet_dashboard', 'auth_totp_portal', 'spreadsheet_dashboard_hr_expense', 'hr', 'base', 'account', 'mrp', 'twilio_sms_gateway_gsc', 'onboarding', 'mrp_account', 'resource_mail', 'sale_mrp', 'quality', 'contacts', 'im_livechat', 'stock_sms', 'web_hierarchy', 'sale_project_stock_account', 'crm_sms', 'itms_contact_extended', 'analytic', 'account_payment', 'itms_abn_lookup', 'muk_rest', 'om_unique_contact', 'groq_document_processor', 'spreadsheet_dashboard_im_livechat', 'barcodes_gs1_nomenclature', 'sale_stock', 'hr_calendar', 'portal', 'hr_timesheet', 'stairmaster_stock', 'stock_delivery', 'remove_footer_copyright', 'hr_skills', 'sale_project', 'http_routing', 'purchase_mrp', 'phone_validation', 'acs_document_base', 'snailmail_account', 'muk_web_colors', 'stairmaster_callups', 'project_sms', 'website_crm_sms', 'calculators_extend', 'muk_web_chatter', 'lone_worker_travel_cost', 'website_mail'}"
2025-05-05 10:39:46,733 - INFO - Testing Session Information
2025-05-05 10:39:46,733 - DEBUG - Testing GET endpoint /api/v2/session
2025-05-05 10:39:47,300 - DEBUG - Endpoint /api/v2/session succeeded with response: {
  "active_ids_limit": 20000,
  "bundle_params": {
    "lang": "en_US"
  },
  "cache_hashes": {
    "load_menus": "877384ac9bda34344ed4cbb463e46730db97959a6ffd49a0db0830cdaae652bc",
    "translations": "48b77b554d94aef60df0789944ff970011c290cd"
  },
  "can_insert_in_spreadsheet": false,
  "chatter_position": "side",
  "currencies": {
    "1": {
      "digits": [
        69,
        2
      ],
      "position": "before",
      "symbol": "$"
    },
    "20": {
      "digits": [
        69,
        2
      ],
      "position": "before",
      "symbol": "\u20b9"
    },
    "21": {
      "digits": [
        69,
        2
      ],
      "position": "before",
      "symbol": "$"
    },
    "25": {
      "digits": [
        69,
        0
      ],
      "position": "before",
      "symbol": "\u00a5"
    },
    "35": {
      "digits": [
        69,
        2
      ],
      "position": "before",
      "symbol": "$"
    },
    "143": {
      "digits": [
        69,
        2
      ],
      "position": "before",
      "symbol": "\u00a3"
    }
  },
  "current_tour": null,
  "db": "STAIRMASTER_18_24032025",
  "display_switch_company_menu": true,
  "home_action_id": false,
  "iap_company_enrich": false,
  "is_admin": true,
  "is_internal_user": true,
  "is_public": false,
  "is_system": true,
  "max_file_upload_size": *********,
  "max_time_between_keys_in_ms": 100,
  "name": "Administrator",
  "partner_display_name": "YourCompany, Administrator",
  "partner_id": 3,
  "partner_write_date": "2025-04-29 01:22:37",
  "profile_collectors": null,
  "profile_params": null,
  "profile_session": null,
  "server_version": "18.0",
  "server_version_info": [
    18,
    0,
    0,
    "final",
    0,
    ""
  ],
  "show_effect": true,
  "storeData": {
    "Store": {
      "action_discuss_id": 125,
      "channel_types_with_seen_infos": [
        "chat",
        "group",
        "livechat"
      ],
      "hasGifPickerFeature": false,
      "hasLinkPreviewFeature": true,
      "hasMessageTranslationFeature": false,
      "has_access_livechat": true,
      "internalUserGroupId": 1,
      "mt_comment_id": 1,
      "odoobot": {
        "id": 2,
        "type": "partner"
      },
      "self": {
        "id": 3,
        "type": "partner"
      },
      "settings": {
        "channel_notifications": false,
        "id": 1,
        "is_discuss_sidebar_category_channel_open": true,
        "is_discuss_sidebar_category_chat_open": true,
        "livechat_lang_ids": [],
        "livechat_username": "Customer Staff",
        "mute_until_dt": false,
        "push_to_talk_key": false,
        "use_push_to_talk": false,
        "user_id": {
          "id": 2
        },
        "voice_active_duration": 200,
        "volumes": [
          [
            "ADD",
            []
          ]
        ]
      }
    },
    "res.partner": [
      {
        "active": false,
        "avatar_128_access_token": "b9e60bfd003934c361f104f3e42e9dcb9fa35d7b163d420db979436b07a9c693o0x683a7251",
        "email": "<EMAIL>",
        "id": 2,
        "im_status": "bot",
        "isInternalUser": true,
        "is_company": false,
        "name": "OdooBot",
        "userId": 1,
        "write_date": "2025-04-28 03:26:12"
      },
      {
        "active": true,
        "avatar_128_access_token": "24e745fd20f81e71e7ac66d996c89fa5a7c3a55b84f0b0adfe13843b628ac42bo0x683a7378",
        "id": 3,
        "isAdmin": true,
        "isInternalUser": true,
        "name": "Administrator",
        "notification_preference": "email",
        "signature": "<span data-o-mail-quote=\"1\">-- <br data-o-mail-quote=\"1\">Mitchell Admin</span>",
        "userId": 2,
        "write_date": "2025-04-29 01:22:37"
      }
    ]
  },
  "support_url": "https://www.odoo.com/buy",
  "test_mode": false,
  "tour_enabled": false,
  "uid": 2,
  "uom_ids": {
    "4": {
      "id": 4,
      "name": "Hours",
      "rounding": 0.01,
      "timesheet_widget": "float_time"
    }
  },
  "user_companies": {
    "allowed_companies": {
      "1": {
        "child_ids": [],
        "has_appsbar_image": true,
        "id": 1,
        "name": "Stairmaster",
        "parent_id": false,
        "sequence": 0,
        "timesheet_uom_factor": 1.0,
        "timesheet_uom_id": 4
      },
      "2": {
        "child_ids": [],
        "has_appsbar_image": false,
        "id": 2,
        "name": "AU Company",
        "parent_id": false,
        "sequence": 10,
        "timesheet_uom_factor": 1.0,
        "timesheet_uom_id": 4
      }
    },
    "current_company": 1,
    "disallowed_ancestor_companies": {}
  },
  "user_context": {
    "lang": "en_US",
    "tz": "Asia/Saigon",
    "uid": 2
  },
  "user_pinned_apps": [
    198,
    778,
    123,
    650,
    15,
    1
  ],
  "user_settings": {
    "channel_notifications": false,
    "id": 1,
    "is_discuss_sidebar_category_channel_open": true,
    "is_discuss_sidebar_category_chat_open": true,
    "livechat_lang_ids": [],
    "livechat_username": "Customer Staff",
    "mute_until_dt": false,
    "push_to_talk_key": false,
    "use_push_to_talk": false,
    "user_id": {
      "id": 2
    },
    "voice_active_duration": 200,
    "volumes": [
      [
        "ADD",
        []
      ]
    ]
  },
  "username": "ptadmin",
  "view_info": {
    "activity": {
      "display_name": "Activity",
      "icon": "fa fa-clock-o",
      "multi_record": true
    },
    "calendar": {
      "display_name": "Calendar",
      "icon": "fa fa-calendar",
      "multi_record": true
    },
    "form": {
      "display_name": "Form",
      "icon": "fa fa-address-card",
      "multi_record": false
    },
    "graph": {
      "display_name": "Graph",
      "icon": "fa fa-area-chart",
      "multi_record": true
    },
    "hierarchy": {
      "display_name": "Hierarchy",
      "icon": "fa fa-share-alt fa-rotate-90",
      "multi_record": true
    },
    "kanban": {
      "display_name": "Kanban",
      "icon": "oi oi-view-kanban",
      "multi_record": true
    },
    "list": {
      "display_name": "List",
      "icon": "oi oi-view-list",
      "multi_record": true
    },
    "pivot": {
      "display_name": "Pivot",
      "icon": "oi oi-view-pivot",
      "multi_record": true
    },
    "search": {
      "display_name": "Search",
      "icon": "oi oi-search",
      "multi_record": true
    },
    "split": {
      "display_name": "Split",
      "icon": "oi oi-text-wrap fa-rotate-180",
      "multi_record": true
    }
  },
  "web.base.url": "http://stairmaster18.odoo-sandbox.com",
  "websocket_worker_version": "18.0-3"
}
2025-05-05 10:39:47,300 - INFO - Testing User
2025-05-05 10:39:47,300 - DEBUG - Testing GET endpoint /api/v2/user
2025-05-05 10:39:47,833 - DEBUG - Endpoint /api/v2/user succeeded with response: {
  "name": "Administrator",
  "uid": 2
}
2025-05-05 10:39:47,833 - INFO - Testing User Information
2025-05-05 10:39:47,833 - DEBUG - Testing GET endpoint /api/v2/userinfo
2025-05-05 10:39:48,635 - DEBUG - Endpoint /api/v2/userinfo succeeded with response: {
  "address": {
    "country": "United States",
    "formatted": "YourCompany\n215 Vine St\n\nScranton PA 18503\nUnited States",
    "locality": "Scranton",
    "postal_code": "18503",
    "region": "Pennsylvania (US)",
    "street_address": "215 Vine St"
  },
  "email": "ptadmin",
  "locale": "en_US",
  "name": "Administrator",
  "phone_number": "******-555-5555",
  "picture": "data:image/png;base64,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********************************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",
  "sub": 2,
  "updated_at": "2025-04-29 01:22:37",
  "username": "ptadmin",
  "website": false,
  "zoneinfo": "Asia/Saigon"
}
2025-05-05 10:39:48,635 - INFO - Fetching fields for res.partner
2025-05-05 10:39:48,635 - DEBUG - Fetching fields for model res.partner with payload: {
  "model": "res.partner",
  "method": "fields_get",
  "args": [],
  "kwargs": {
    "attributes": [
      "string",
      "type",
      "relation",
      "required",
      "readonly",
      "store",
      "searchable",
      "sortable",
      "depends",
      "domain"
    ]
  }
}
2025-05-05 10:39:49,434 - DEBUG - HTTP status code for res.partner: 200
2025-05-05 10:39:49,434 - DEBUG - Response headers for res.partner: {'Server': 'openresty', 'Date': 'Mon, 05 May 2025 03:39:49 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '79707', 'Connection': 'keep-alive', 'X-Content-Type-Options': 'nosniff, nosniff', 'Content-Security-Policy': 'upgrade-insecure-requests;', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload', 'X-Frame-Options': 'SAMEORIGIN', 'Referrer-Policy': 'strict-origin', 'Permissions-Policy': 'geolocation=(), midi=(), microphone=*, camera=*, payment=()', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Methods': 'GET, POST, OPTIONS, PUT, DELETE', 'Access-Control-Allow-Headers': 'Origin, Content-Type, Accept, Authorization, X-CSRF-Token, Set-Cookie', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Set-Cookie'}
2025-05-05 10:39:49,434 - DEBUG - Raw API response for res.partner: {
    "abn": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "ABN",
        "type": "char"
    },
    "abn_active": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "ABN Active",
        "type": "boolean"
    },
    "acn": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "ACN",
        "type": "char"
    },
    "active": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Active",
        "type": "boolean"
    },
    "active_lang_count": {
        "depends": [
            "lang"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Active Lang Count",
        "type": "integer"
    },
    "activity_calendar_event_id": {
        "depends": [
            "activity_ids.calendar_event_id"
        ],
        "domain": [],
        "readonly": true,
        "relation": "calendar.event",
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Next Activity Calendar Event",
        "type": "many2one"
    },
    "activity_date_deadline": {
        "depends": [
            "activity_ids.date_deadline"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Next Activity Deadline",
        "type": "date"
    },
    "activity_exception_decoration": {
        "depends": [
            "activity_ids.activity_type_id.decoration_type",
            "activity_ids.activity_type_id.icon"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Activity Exception Decoration",
        "type": "selection"
    },
    "activity_exception_icon": {
        "depends": [
            "activity_ids.activity_type_id.decoration_type",
            "activity_ids.activity_type_id.icon"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Icon",
        "type": "char"
    },
    "activity_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "mail.activity",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Activities",
        "type": "one2many"
    },
    "activity_state": {
        "depends": [
            "activity_ids.state"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Activity State",
        "type": "selection"
    },
    "activity_summary": {
        "depends": [
            "activity_ids.summary"
        ],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Next Activity Summary",
        "type": "char"
    },
    "activity_type_icon": {
        "depends": [
            "activity_ids.icon"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Activity Type Icon",
        "type": "char"
    },
    "activity_type_id": {
        "depends": [
            "activity_ids.activity_type_id"
        ],
        "readonly": false,
        "relation": "mail.activity.type",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Next Activity Type",
        "type": "many2one"
    },
    "activity_user_id": {
        "depends": [
            "activity_ids.user_id"
        ],
        "domain": [],
        "readonly": true,
        "relation": "res.users",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Responsible User",
        "type": "many2one"
    },
    "additional_info": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Additional info",
        "type": "char"
    },
    "asic_number": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "ASIC Number",
        "type": "char"
    },
    "attach_count": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Documents",
        "type": "integer"
    },
    "attachment_count": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Attachment Count",
        "type": "integer"
    },
    "attachment_ids": {
        "depends": [],
        "domain": [],
        "readonly": true,
        "relation": "acs.document",
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Attachments",
        "type": "many2many"
    },
    "auto_issue": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Auto Issue",
        "type": "boolean"
    },
    "autopost_bills": {
        "depends": [],
        "readonly": false,
        "required": true,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Auto-post bills",
        "type": "selection"
    },
    "avatar_1024": {
        "depends": [
            "name",
            "user_ids.share",
            "image_1024",
            "is_company",
            "type",
            "name",
            "image_1024"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Avatar 1024",
        "type": "binary"
    },
    "avatar_128": {
        "depends": [
            "name",
            "user_ids.share",
            "image_128",
            "is_company",
            "type",
            "name",
            "image_128"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Avatar 128",
        "type": "binary"
    },
    "avatar_1920": {
        "depends": [
            "name",
            "user_ids.share",
            "image_1920",
            "is_company",
            "type",
            "name",
            "image_1920"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Avatar",
        "type": "binary"
    },
    "avatar_256": {
        "depends": [
            "name",
            "user_ids.share",
            "image_256",
            "is_company",
            "type",
            "name",
            "image_256"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Avatar 256",
        "type": "binary"
    },
    "avatar_512": {
        "depends": [
            "name",
            "user_ids.share",
            "image_512",
            "is_company",
            "type",
            "name",
            "image_512"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Avatar 512",
        "type": "binary"
    },
    "bank_account_count": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Bank",
        "type": "integer"
    },
    "bank_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "res.partner.bank",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Banks",
        "type": "one2many"
    },
    "barcode": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Barcode",
        "type": "char"
    },
    "builder_group_id": {
        "depends": [
            "builder_id"
        ],
        "domain": [],
        "readonly": true,
        "relation": "customer.group",
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Customer Group",
        "type": "many2one"
    },
    "builder_id": {
        "depends": [],
        "domain": [
            [
                "type_contact",
                "=",
                "builders"
            ]
        ],
        "readonly": false,
        "relation": "res.partner",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Builder",
        "type": "many2one"
    },
    "business_name": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Business Name",
        "type": "char"
    },
    "business_name_effective_from": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Business Name Effective From",
        "type": "date"
    },
    "buyer_id": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "res.users",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Buyer",
        "type": "many2one"
    },
    "calendar_last_notif_ack": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Last notification marked as read from base Calendar",
        "type": "datetime"
    },
    "can_publish": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Can Publish",
        "type": "boolean"
    },
    "carrier": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "res.partner",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Carrier",
        "type": "many2one"
    },
    "category_id": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "res.partner.category",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Tags",
        "type": "many2many"
    },
    "certifications_company_count": {
        "depends": [
            "is_company",
            "child_ids.certifications_count"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Company Certifications Count",
        "type": "integer"
    },
    "certifications_count": {
        "depends": [
            "is_company"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Certifications Count",
        "type": "integer"
    },
    "channel_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "discuss.channel",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Channels",
        "type": "many2many"
    },
    "child_ids": {
        "depends": [
            "child_ids.active"
        ],
        "domain": [
            [
                "active",
                "=",
                true
            ]
        ],
        "readonly": false,
        "relation": "res.partner",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Contact",
        "type": "one2many"
    },
    "city": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "City",
        "type": "char"
    },
    "classification_rule_count": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Classification Rule Count",
        "type": "integer"
    },
    "classification_rule_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "document.classification.rule",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Classification Rules",
        "type": "one2many"
    },
    "color": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Color Index",
        "type": "integer"
    },
    "combined_document_ids": {
        "depends": [
            "document_ids"
        ],
        "domain": [],
        "readonly": false,
        "relation": "groq.document",
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "All Documents",
        "type": "one2many"
    },
    "comment": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Notes",
        "type": "html"
    },
    "commercial_company_name": {
        "depends": [
            "company_name",
            "parent_id.is_company",
            "commercial_partner_id.name"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Company Name Entity",
        "type": "char"
    },
    "commercial_partner_id": {
        "depends": [
            "is_company",
            "parent_id.commercial_partner_id"
        ],
        "domain": [],
        "readonly": true,
        "relation": "res.partner",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Commercial Entity",
        "type": "many2one"
    },
    "company_id": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "res.company",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Company",
        "type": "many2one"
    },
    "company_name": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Company Name",
        "type": "char"
    },
    "company_registry": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Company ID",
        "type": "char"
    },
    "company_registry_label": {
        "depends": [
            "country_id"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Company ID Label",
        "type": "char"
    },
    "company_type": {
        "depends": [
            "is_company"
        ],
        "readonly": false,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Company Type",
        "type": "selection"
    },
    "complete_name": {
        "depends": [
            "is_company",
            "name",
            "parent_id.name",
            "type",
            "company_name",
            "commercial_company_name"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Complete Name",
        "type": "char"
    },
    "contact_address": {
        "depends": [
            "street",
            "street2",
            "zip",
            "city",
            "state_id",
            "country_id",
            "country_id",
            "company_name",
            "state_id"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Complete Address",
        "type": "char"
    },
    "contact_address_inline": {
        "depends": [
            "contact_address"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Inlined Complete Address",
        "type": "char"
    },
    "contract_ids": {
        "depends": [],
        "domain": [],
        "readonly": true,
        "relation": "account.analytic.account",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Partner Contracts",
        "type": "one2many"
    },
    "country_code": {
        "depends": [
            "country_id.code"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": false,
        "string": "Country Code",
        "type": "char"
    },
    "country_id": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "res.country",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Country",
        "type": "many2one"
    },
    "create_date": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Created on",
        "type": "datetime"
    },
    "create_uid": {
        "depends": [],
        "domain": [],
        "readonly": true,
        "relation": "res.users",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Created by",
        "type": "many2one"
    },
    "credit": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Total Receivable",
        "type": "monetary"
    },
    "credit_limit": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Credit Limit",
        "type": "float"
    },
    "credit_to_invoice": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Credit To Invoice",
        "type": "monetary"
    },
    "currency_id": {
        "depends": [],
        "domain": [],
        "readonly": true,
        "relation": "res.currency",
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Currency",
        "type": "many2one"
    },
    "customer": {
        "depends": [
            "customer_rank"
        ],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Is a Customer",
        "type": "boolean"
    },
    "customer_rank": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Customer Rank",
        "type": "integer"
    },
    "customer_type": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Customer Type",
        "type": "selection"
    },
    "customer_type_id": {
        "depends": [
            "customer",
            "supplier"
        ],
        "domain": [],
        "readonly": false,
        "relation": "res.partner.type",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Partner Type",
        "type": "many2one"
    },
    "date_localization": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Geolocation Date",
        "type": "date"
    },
    "date_registered": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Date",
        "type": "date"
    },
    "days_sales_outstanding": {
        "depends": [
            "credit"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Days Sales Outstanding (DSO)",
        "type": "float"
    },
    "debit": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Total Payable",
        "type": "monetary"
    },
    "debit_limit": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Payable Limit",
        "type": "monetary"
    },
    "deleted": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Deleted",
        "type": "boolean"
    },
    "display_invoice_edi_format": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Display Invoice Edi Format",
        "type": "boolean"
    },
    "display_invoice_template_pdf_report_id": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Display Invoice Template Pdf Report",
        "type": "boolean"
    },
    "display_name": {
        "depends": [
            "name",
            "website_id",
            "complete_name",
            "email",
            "vat",
            "state_id",
            "country_id",
            "commercial_company_name",
            "name"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Display Name",
        "type": "char"
    },
    "document_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "groq.document",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Groq Documents",
        "type": "one2many"
    },
    "document_preview_url": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Document Preview Link",
        "type": "char"
    },
    "duplicated_bank_account_partners_count": {
        "depends": [
            "bank_ids"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Duplicated Bank Account Partners Count",
        "type": "integer"
    },
    "effective_from": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Effective From",
        "type": "date"
    },
    "effective_to": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Effective To",
        "type": "date"
    },
    "email": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Email",
        "type": "char"
    },
    "email_formatted": {
        "depends": [
            "name",
            "email"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Formatted Email",
        "type": "char"
    },
    "email_normalized": {
        "depends": [
            "email"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Normalized Email",
        "type": "char"
    },
    "employee": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Employee",
        "type": "boolean"
    },
    "employee_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "hr.employee",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Employees",
        "type": "one2many"
    },
    "employees_count": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Employees Count",
        "type": "integer"
    },
    "entity_description": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Entity Description",
        "type": "char"
    },
    "entity_type": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Entity Type",
        "type": "char"
    },
    "family_name": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Family Name",
        "type": "char"
    },
    "firstname": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "First name",
        "type": "char"
    },
    "fiscal_country_codes": {
        "depends": [
            "company_id"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Fiscal Country Codes",
        "type": "char"
    },
    "function": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Job Position",
        "type": "char"
    },
    "given_name": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Given Name",
        "type": "char"
    },
    "gps_coordinates": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "GPS Coordinates",
        "type": "char"
    },
    "groups": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "customer.group",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Group",
        "type": "many2many"
    },
    "gst": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "GST",
        "type": "selection"
    },
    "gst_effective_from": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "GST Effective From",
        "type": "date"
    },
    "gst_effective_to": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "GST Effective To",
        "type": "date"
    },
    "has_message": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Has Message",
        "type": "boolean"
    },
    "house_type_ids": {
        "depends": [],
        "domain": "[('builder_id','=', id)]",
        "readonly": false,
        "relation": "design.register",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "House Types",
        "type": "one2many"
    },
    "id": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "ID",
        "type": "integer"
    },
    "ignore_abnormal_invoice_amount": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Ignore Abnormal Invoice Amount",
        "type": "boolean"
    },
    "ignore_abnormal_invoice_date": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Ignore Abnormal Invoice Date",
        "type": "boolean"
    },
    "im_status": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "IM Status",
        "type": "char"
    },
    "image_1024": {
        "depends": [
            "image_1920"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Image 1024",
        "type": "binary"
    },
    "image_128": {
        "depends": [
            "image_1920"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Image 128",
        "type": "binary"
    },
    "image_1920": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Image",
        "type": "binary"
    },
    "image_256": {
        "depends": [
            "image_1920"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Image 256",
        "type": "binary"
    },
    "image_512": {
        "depends": [
            "image_1920"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Image 512",
        "type": "binary"
    },
    "industry_id": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "res.partner.industry",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Industry",
        "type": "many2one"
    },
    "invoice_edi_format": {
        "depends": [
            "commercial_partner_id.country_code"
        ],
        "readonly": false,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "eInvoice format",
        "type": "selection"
    },
    "invoice_edi_format_store": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Invoice Edi Format Store",
        "type": "char"
    },
    "invoice_ids": {
        "depends": [],
        "domain": [],
        "readonly": true,
        "relation": "account.move",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Invoices",
        "type": "one2many"
    },
    "invoice_sending_method": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Invoice sending",
        "type": "selection"
    },
    "invoice_template_pdf_report_id": {
        "depends": [],
        "domain": "[('is_invoice_report', '=', True)]",
        "readonly": false,
        "relation": "ir.actions.report",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Invoice template",
        "type": "many2one"
    },
    "invoice_warn": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Invoice",
        "type": "selection"
    },
    "invoice_warn_msg": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Message for Invoice",
        "type": "text"
    },
    "is_blacklisted": {
        "depends": [
            "email_normalized"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Blacklist",
        "type": "boolean"
    },
    "is_coa_installed": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Is Coa Installed",
        "type": "boolean"
    },
    "is_company": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Is a Company",
        "type": "boolean"
    },
    "is_current": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Is Current",
        "type": "boolean"
    },
    "is_peppol_edi_format": {
        "depends": [
            "invoice_edi_format"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Is Peppol Edi Format",
        "type": "boolean"
    },
    "is_public": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Is Public",
        "type": "boolean"
    },
    "is_published": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Is Published",
        "type": "boolean"
    },
    "is_ubl_format": {
        "depends": [
            "invoice_edi_format"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Is Ubl Format",
        "type": "boolean"
    },
    "journal_item_count": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Journal Items",
        "type": "integer"
    },
    "lang": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Language",
        "type": "selection"
    },
    "last_updated": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Updated",
        "type": "date"
    },
    "lastname": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Last name",
        "type": "char"
    },
    "latitude": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Latitude",
        "type": "float"
    },
    "legal_entity_name": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Legal Entity Name",
        "type": "char"
    },
    "legal_name_effective_from": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Legal Name Effective From",
        "type": "date"
    },
    "legal_name_effective_to": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Legal Name Effective To",
        "type": "date"
    },
    "longitude": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Longitude",
        "type": "float"
    },
    "main_name_effective_from": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Main Name Effective From",
        "type": "date"
    },
    "map_html": {
        "depends": [
            "street",
            "partner_latitude",
            "partner_longitude"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Map HTML",
        "type": "text"
    },
    "meeting_count": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "# Meetings",
        "type": "integer"
    },
    "meeting_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "calendar.event",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Meetings",
        "type": "many2many"
    },
    "message_attachment_count": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Attachment Count",
        "type": "integer"
    },
    "message_bounce": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Bounce",
        "type": "integer"
    },
    "message_follower_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "mail.followers",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Followers",
        "type": "one2many"
    },
    "message_has_error": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Message Delivery error",
        "type": "boolean"
    },
    "message_has_error_counter": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Number of errors",
        "type": "integer"
    },
    "message_has_sms_error": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "SMS Delivery error",
        "type": "boolean"
    },
    "message_ids": {
        "depends": [],
        "domain": [
            [
                "message_type",
                "!=",
                "user_notification"
            ]
        ],
        "readonly": false,
        "relation": "mail.message",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Messages",
        "type": "one2many"
    },
    "message_is_follower": {
        "depends": [
            "message_follower_ids"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Is Follower",
        "type": "boolean"
    },
    "message_needaction": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Action Needed",
        "type": "boolean"
    },
    "message_needaction_counter": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Number of Actions",
        "type": "integer"
    },
    "message_partner_ids": {
        "depends": [
            "message_follower_ids"
        ],
        "domain": [],
        "readonly": false,
        "relation": "res.partner",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Followers (Partners)",
        "type": "many2many"
    },
    "mobile": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Mobile",
        "type": "char"
    },
    "mobile_blacklisted": {
        "depends": [
            "phone_sanitized"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Blacklisted Phone Is Mobile",
        "type": "boolean"
    },
    "my_activity_date_deadline": {
        "depends": [
            "activity_ids.date_deadline",
            "activity_ids.user_id"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "My Activity Deadline",
        "type": "date"
    },
    "myob_card_displayid": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "MYOB Card ID",
        "type": "char"
    },
    "myob_uid_id": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Myob Uid",
        "type": "char"
    },
    "name": {
        "depends": [
            "firstname",
            "lastname"
        ],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Name",
        "type": "char"
    },
    "on_time_rate": {
        "depends": [
            "purchase_line_ids"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "On-Time Delivery Rate",
        "type": "float"
    },
    "opportunity_count": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Opportunity Count",
        "type": "integer"
    },
    "opportunity_ids": {
        "depends": [
            "opportunity_ids.type"
        ],
        "domain": [
            [
                "type",
                "=",
                "opportunity"
            ]
        ],
        "readonly": false,
        "relation": "crm.lead",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Opportunities",
        "type": "one2many"
    },
    "organisation_name": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Organisation Name",
        "type": "char"
    },
    "other_given_name": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Other Given Name",
        "type": "char"
    },
    "parent_id": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "res.partner",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Related Company",
        "type": "many2one"
    },
    "parent_name": {
        "depends": [
            "parent_id.name"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": false,
        "string": "Parent name",
        "type": "char"
    },
    "partner_company_registry_placeholder": {
        "depends": [
            "country_id"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Partner Company Registry Placeholder",
        "type": "char"
    },
    "partner_gid": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Company database ID",
        "type": "integer"
    },
    "partner_latitude": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Geo Latitude",
        "type": "float"
    },
    "partner_longitude": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Geo Longitude",
        "type": "float"
    },
    "partner_share": {
        "depends": [
            "user_ids.share",
            "user_ids.active"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Share Partner",
        "type": "boolean"
    },
    "partner_vat_placeholder": {
        "depends": [
            "country_id"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Partner Vat Placeholder",
        "type": "char"
    },
    "payment_token_count": {
        "depends": [
            "payment_token_ids"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Payment Token Count",
        "type": "integer"
    },
    "payment_token_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "payment.token",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Payment Tokens",
        "type": "one2many"
    },
    "peppol_eas": {
        "depends": [
            "country_code",
            "vat",
            "company_registry"
        ],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Peppol e-address (EAS)",
        "type": "selection"
    },
    "peppol_endpoint": {
        "depends": [
            "country_code",
            "vat",
            "company_registry",
            "peppol_eas"
        ],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Peppol Endpoint",
        "type": "char"
    },
    "phone": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Phone",
        "type": "char"
    },
    "phone_blacklisted": {
        "depends": [
            "phone_sanitized"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Blacklisted Phone is Phone",
        "type": "boolean"
    },
    "phone_mobile_search": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Phone/Mobile",
        "type": "char"
    },
    "phone_sanitized": {
        "depends": [
            "mobile",
            "phone",
            "country_id"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Sanitized Number",
        "type": "char"
    },
    "phone_sanitized_blacklisted": {
        "depends": [
            "phone_sanitized"
        ],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Phone Blacklisted",
        "type": "boolean"
    },
    "physical_address_effective_from": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Physical Address Effective From",
        "type": "date"
    },
    "physical_address_effective_to": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Physical Address Effective To",
        "type": "date"
    },
    "physical_address_postcode": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Physical Address Postcode",
        "type": "char"
    },
    "physical_address_state": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Physical Address State",
        "type": "char"
    },
    "picking_warn": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Stock Picking",
        "type": "selection"
    },
    "picking_warn_msg": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Message for Stock Picking",
        "type": "text"
    },
    "plan_to_change_bike": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Plan To Change Bike",
        "type": "boolean"
    },
    "plan_to_change_car": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Plan To Change Car",
        "type": "boolean"
    },
    "project_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "project.project",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Projects",
        "type": "one2many"
    },
    "property_account_payable_id": {
        "depends": [],
        "domain": "[('account_type', '=', 'liability_payable'), ('deprecated', '=', False)]",
        "readonly": false,
        "relation": "account.account",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Account Payable",
        "type": "many2one"
    },
    "property_account_position_id": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "account.fiscal.position",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Fiscal Position",
        "type": "many2one"
    },
    "property_account_receivable_id": {
        "depends": [],
        "domain": "[('account_type', '=', 'asset_receivable'), ('deprecated', '=', False)]",
        "readonly": false,
        "relation": "account.account",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Account Receivable",
        "type": "many2one"
    },
    "property_delivery_carrier_id": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "delivery.carrier",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Delivery Method",
        "type": "many2one"
    },
    "property_inbound_payment_method_line_id": {
        "depends": [],
        "domain": [
            [
                "payment_type",
                "=",
                "inbound"
            ],
            [
                "company_id",
                "=",
                1
            ]
        ],
        "readonly": false,
        "relation": "account.payment.method.line",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Property Inbound Payment Method Line",
        "type": "many2one"
    },
    "property_outbound_payment_method_line_id": {
        "depends": [],
        "domain": [
            [
                "payment_type",
                "=",
                "outbound"
            ],
            [
                "company_id",
                "=",
                1
            ]
        ],
        "readonly": false,
        "relation": "account.payment.method.line",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Property Outbound Payment Method Line",
        "type": "many2one"
    },
    "property_payment_term_id": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "account.payment.term",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Customer Payment Terms",
        "type": "many2one"
    },
    "property_product_pricelist": {
        "depends": [
            "country_id",
            "specific_property_product_pricelist"
        ],
        "domain": [
            [
                "company_id",
                "in",
                [
                    1,
                    false
                ]
            ]
        ],
        "readonly": false,
        "relation": "product.pricelist",
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Pricelist",
        "type": "many2one"
    },
    "property_purchase_currency_id": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "res.currency",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Supplier Currency",
        "type": "many2one"
    },
    "property_stock_customer": {
        "depends": [],
        "domain": "[('company_id', 'in', [allowed_company_ids[0]] + [False])] + ['|', ('company_id', '=', False), ('company_id', '=', allowed_company_ids[0])]",
        "readonly": false,
        "relation": "stock.location",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Customer Location",
        "type": "many2one"
    },
    "property_stock_supplier": {
        "depends": [],
        "domain": "[('company_id', 'in', [allowed_company_ids[0]] + [False])] + ['|', ('company_id', '=', False), ('company_id', '=', allowed_company_ids[0])]",
        "readonly": false,
        "relation": "stock.location",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Vendor Location",
        "type": "many2one"
    },
    "property_supplier_payment_term_id": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "account.payment.term",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Vendor Payment Terms",
        "type": "many2one"
    },
    "purchase_line_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "purchase.order.line",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Purchase Lines",
        "type": "one2many"
    },
    "purchase_order_count": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Purchase Order Count",
        "type": "integer"
    },
    "purchase_warn": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Purchase Order Warning",
        "type": "selection"
    },
    "purchase_warn_msg": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Message for Purchase Order",
        "type": "text"
    },
    "rating_ids": {
        "depends": [],
        "domain": [
            [
                "res_model",
                "=",
                "res.partner"
            ]
        ],
        "readonly": false,
        "relation": "rating.rating",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Ratings",
        "type": "one2many"
    },
    "receipt_reminder_email": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Receipt Reminder",
        "type": "boolean"
    },
    "ref": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Reference",
        "type": "char"
    },
    "ref_company_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "res.company",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Companies that refers to partner",
        "type": "one2many"
    },
    "reminder_date_before_receipt": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Days Before Receipt",
        "type": "integer"
    },
    "replaced_from": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Replaced From",
        "type": "date"
    },
    "retrieved_date": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Retrieved Date",
        "type": "datetime"
    },
    "sale_order_count": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Sale Order Count",
        "type": "integer"
    },
    "sale_order_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "sale.order",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Sales Order",
        "type": "one2many"
    },
    "sale_warn": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Sales Warnings",
        "type": "selection"
    },
    "sale_warn_msg": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Message for Sales Order",
        "type": "text"
    },
    "same_company_registry_partner_id": {
        "depends": [
            "vat",
            "company_id",
            "company_registry"
        ],
        "domain": [],
        "readonly": true,
        "relation": "res.partner",
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Partner with same Company Registry",
        "type": "many2one"
    },
    "same_vat_partner_id": {
        "depends": [
            "vat",
            "company_id",
            "company_registry"
        ],
        "domain": [],
        "readonly": true,
        "relation": "res.partner",
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Partner with same Tax ID",
        "type": "many2one"
    },
    "self": {
        "depends": [],
        "domain": [],
        "readonly": true,
        "relation": "res.partner",
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Self",
        "type": "many2one"
    },
    "sh_contact_google_location": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Enter Location",
        "type": "char"
    },
    "sh_contact_place_text": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Enter location",
        "type": "char"
    },
    "sh_contact_place_text_main_string": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Enter location ",
        "type": "char"
    },
    "show_carrier": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Show Carrier",
        "type": "boolean"
    },
    "show_credit_limit": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Show Credit Limit",
        "type": "boolean"
    },
    "show_group": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Show Group",
        "type": "boolean"
    },
    "signer_count": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Signer Count",
        "type": "integer"
    },
    "signer_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "sign.oca.request.signer",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Signer",
        "type": "one2many"
    },
    "signup_type": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Signup Token Type",
        "type": "char"
    },
    "site_addresses_sale_order_ids": {
        "depends": [
            "sale_order_ids"
        ],
        "domain": [],
        "readonly": true,
        "relation": "res.partner",
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Opportunities",
        "type": "many2many"
    },
    "specific_property_product_pricelist": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "product.pricelist",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Specific Property Product Pricelist",
        "type": "many2one"
    },
    "stairmaster_entity_id": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "res.partner",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Stairmaster Entity Name",
        "type": "many2one"
    },
    "stairmaster_short_name": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Stairmaster Short Name",
        "type": "char"
    },
    "starred_message_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "mail.message",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Starred Message",
        "type": "many2many"
    },
    "state_id": {
        "depends": [],
        "domain": "[('country_id', '=?', country_id)]",
        "readonly": false,
        "relation": "res.country.state",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "State",
        "type": "many2one"
    },
    "status_code": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Status Code",
        "type": "char"
    },
    "street": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Street",
        "type": "char"
    },
    "street2": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Street2",
        "type": "char"
    },
    "supervisor_ids": {
        "depends": [
            "supervisor_ids.type_contact"
        ],
        "domain": [
            [
                "type_contact",
                "=",
                "supervisor"
            ]
        ],
        "readonly": false,
        "relation": "res.partner",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Supervisors",
        "type": "one2many"
    },
    "supplier": {
        "depends": [
            "supplier_rank"
        ],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Is a Vendor",
        "type": "boolean"
    },
    "supplier_invoice_count": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "# Vendor Bills",
        "type": "integer"
    },
    "supplier_rank": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Supplier Rank",
        "type": "integer"
    },
    "task_count": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "# Tasks",
        "type": "integer"
    },
    "task_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "project.task",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Tasks",
        "type": "one2many"
    },
    "title": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "res.partner.title",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Title",
        "type": "many2one"
    },
    "total_invoiced": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Total Invoiced",
        "type": "monetary"
    },
    "trading_name": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Trade License",
        "type": "char"
    },
    "transport_note": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Carrier Notes",
        "type": "char"
    },
    "trust": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Degree of trust you have in this debtor",
        "type": "selection"
    },
    "type": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Address Type",
        "type": "selection"
    },
    "type_abn": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Type",
        "type": "selection"
    },
    "type_contact": {
        "depends": [
            "type"
        ],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Type of Contact",
        "type": "selection"
    },
    "tz": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Timezone",
        "type": "selection"
    },
    "tz_offset": {
        "depends": [
            "tz"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Timezone offset",
        "type": "char"
    },
    "use_partner_credit_limit": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Partner Limit",
        "type": "boolean"
    },
    "user_id": {
        "depends": [
            "parent_id"
        ],
        "domain": [],
        "readonly": false,
        "relation": "res.users",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Salesperson",
        "type": "many2one"
    },
    "user_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "res.users",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Users",
        "type": "one2many"
    },
    "user_livechat_username": {
        "depends": [
            "user_ids.livechat_username"
        ],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "User Livechat Username",
        "type": "char"
    },
    "vat": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "ABN",
        "type": "char"
    },
    "vat_label": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Tax ID Label",
        "type": "char"
    },
    "view_child_in_list": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "View Child Address in List",
        "type": "boolean"
    },
    "visitor_ids": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "website.visitor",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Visitors",
        "type": "one2many"
    },
    "website": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Website Link",
        "type": "char"
    },
    "website_id": {
        "depends": [],
        "domain": [],
        "readonly": false,
        "relation": "website",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Website",
        "type": "many2one"
    },
    "website_message_ids": {
        "depends": [],
        "domain": [
            [
                "model",
                "=",
                "res.partner"
            ],
            [
                "message_type",
                "in",
                [
                    "comment",
                    "email",
                    "email_outgoing"
                ]
            ]
        ],
        "readonly": false,
        "relation": "mail.message",
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": true,
        "string": "Website Messages",
        "type": "one2many"
    },
    "website_published": {
        "depends": [
            "is_published",
            "website_id"
        ],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": false,
        "store": false,
        "string": "Visible on current website",
        "type": "boolean"
    },
    "website_url": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": false,
        "sortable": false,
        "store": false,
        "string": "Website URL",
        "type": "char"
    },
    "write_date": {
        "depends": [],
        "readonly": true,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Last Updated on",
        "type": "datetime"
    },
    "write_uid": {
        "depends": [],
        "domain": [],
        "readonly": true,
        "relation": "res.users",
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Last Updated by",
        "type": "many2one"
    },
    "x_stairbiz_id": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Stairbiz ID",
        "type": "char"
    },
    "x_stairbiz_migrated": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Migrated from Stairbiz",
        "type": "boolean"
    },
    "x_stairbiz_migration_date": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Stairbiz Migration Date",
        "type": "datetime"
    },
    "zip": {
        "depends": [],
        "readonly": false,
        "required": false,
        "searchable": true,
        "sortable": true,
        "store": true,
        "string": "Zip",
        "type": "char"
    }
}
2025-05-05 10:39:49,437 - DEBUG - Parsed API response for res.partner: {
  "abn": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "ABN",
    "type": "char"
  },
  "abn_active": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "ABN Active",
    "type": "boolean"
  },
  "acn": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "ACN",
    "type": "char"
  },
  "active": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Active",
    "type": "boolean"
  },
  "active_lang_count": {
    "depends": [
      "lang"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Active Lang Count",
    "type": "integer"
  },
  "activity_calendar_event_id": {
    "depends": [
      "activity_ids.calendar_event_id"
    ],
    "domain": [],
    "readonly": true,
    "relation": "calendar.event",
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Next Activity Calendar Event",
    "type": "many2one"
  },
  "activity_date_deadline": {
    "depends": [
      "activity_ids.date_deadline"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Next Activity Deadline",
    "type": "date"
  },
  "activity_exception_decoration": {
    "depends": [
      "activity_ids.activity_type_id.decoration_type",
      "activity_ids.activity_type_id.icon"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Activity Exception Decoration",
    "type": "selection"
  },
  "activity_exception_icon": {
    "depends": [
      "activity_ids.activity_type_id.decoration_type",
      "activity_ids.activity_type_id.icon"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Icon",
    "type": "char"
  },
  "activity_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "mail.activity",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Activities",
    "type": "one2many"
  },
  "activity_state": {
    "depends": [
      "activity_ids.state"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Activity State",
    "type": "selection"
  },
  "activity_summary": {
    "depends": [
      "activity_ids.summary"
    ],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Next Activity Summary",
    "type": "char"
  },
  "activity_type_icon": {
    "depends": [
      "activity_ids.icon"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Activity Type Icon",
    "type": "char"
  },
  "activity_type_id": {
    "depends": [
      "activity_ids.activity_type_id"
    ],
    "readonly": false,
    "relation": "mail.activity.type",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Next Activity Type",
    "type": "many2one"
  },
  "activity_user_id": {
    "depends": [
      "activity_ids.user_id"
    ],
    "domain": [],
    "readonly": true,
    "relation": "res.users",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Responsible User",
    "type": "many2one"
  },
  "additional_info": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Additional info",
    "type": "char"
  },
  "asic_number": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "ASIC Number",
    "type": "char"
  },
  "attach_count": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Documents",
    "type": "integer"
  },
  "attachment_count": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Attachment Count",
    "type": "integer"
  },
  "attachment_ids": {
    "depends": [],
    "domain": [],
    "readonly": true,
    "relation": "acs.document",
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Attachments",
    "type": "many2many"
  },
  "auto_issue": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Auto Issue",
    "type": "boolean"
  },
  "autopost_bills": {
    "depends": [],
    "readonly": false,
    "required": true,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Auto-post bills",
    "type": "selection"
  },
  "avatar_1024": {
    "depends": [
      "name",
      "user_ids.share",
      "image_1024",
      "is_company",
      "type",
      "name",
      "image_1024"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Avatar 1024",
    "type": "binary"
  },
  "avatar_128": {
    "depends": [
      "name",
      "user_ids.share",
      "image_128",
      "is_company",
      "type",
      "name",
      "image_128"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Avatar 128",
    "type": "binary"
  },
  "avatar_1920": {
    "depends": [
      "name",
      "user_ids.share",
      "image_1920",
      "is_company",
      "type",
      "name",
      "image_1920"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Avatar",
    "type": "binary"
  },
  "avatar_256": {
    "depends": [
      "name",
      "user_ids.share",
      "image_256",
      "is_company",
      "type",
      "name",
      "image_256"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Avatar 256",
    "type": "binary"
  },
  "avatar_512": {
    "depends": [
      "name",
      "user_ids.share",
      "image_512",
      "is_company",
      "type",
      "name",
      "image_512"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Avatar 512",
    "type": "binary"
  },
  "bank_account_count": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Bank",
    "type": "integer"
  },
  "bank_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "res.partner.bank",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Banks",
    "type": "one2many"
  },
  "barcode": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Barcode",
    "type": "char"
  },
  "builder_group_id": {
    "depends": [
      "builder_id"
    ],
    "domain": [],
    "readonly": true,
    "relation": "customer.group",
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Customer Group",
    "type": "many2one"
  },
  "builder_id": {
    "depends": [],
    "domain": [
      [
        "type_contact",
        "=",
        "builders"
      ]
    ],
    "readonly": false,
    "relation": "res.partner",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Builder",
    "type": "many2one"
  },
  "business_name": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Business Name",
    "type": "char"
  },
  "business_name_effective_from": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Business Name Effective From",
    "type": "date"
  },
  "buyer_id": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "res.users",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Buyer",
    "type": "many2one"
  },
  "calendar_last_notif_ack": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Last notification marked as read from base Calendar",
    "type": "datetime"
  },
  "can_publish": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Can Publish",
    "type": "boolean"
  },
  "carrier": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "res.partner",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Carrier",
    "type": "many2one"
  },
  "category_id": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "res.partner.category",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Tags",
    "type": "many2many"
  },
  "certifications_company_count": {
    "depends": [
      "is_company",
      "child_ids.certifications_count"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Company Certifications Count",
    "type": "integer"
  },
  "certifications_count": {
    "depends": [
      "is_company"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Certifications Count",
    "type": "integer"
  },
  "channel_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "discuss.channel",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Channels",
    "type": "many2many"
  },
  "child_ids": {
    "depends": [
      "child_ids.active"
    ],
    "domain": [
      [
        "active",
        "=",
        true
      ]
    ],
    "readonly": false,
    "relation": "res.partner",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Contact",
    "type": "one2many"
  },
  "city": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "City",
    "type": "char"
  },
  "classification_rule_count": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Classification Rule Count",
    "type": "integer"
  },
  "classification_rule_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "document.classification.rule",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Classification Rules",
    "type": "one2many"
  },
  "color": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Color Index",
    "type": "integer"
  },
  "combined_document_ids": {
    "depends": [
      "document_ids"
    ],
    "domain": [],
    "readonly": false,
    "relation": "groq.document",
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "All Documents",
    "type": "one2many"
  },
  "comment": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Notes",
    "type": "html"
  },
  "commercial_company_name": {
    "depends": [
      "company_name",
      "parent_id.is_company",
      "commercial_partner_id.name"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Company Name Entity",
    "type": "char"
  },
  "commercial_partner_id": {
    "depends": [
      "is_company",
      "parent_id.commercial_partner_id"
    ],
    "domain": [],
    "readonly": true,
    "relation": "res.partner",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Commercial Entity",
    "type": "many2one"
  },
  "company_id": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "res.company",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Company",
    "type": "many2one"
  },
  "company_name": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Company Name",
    "type": "char"
  },
  "company_registry": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Company ID",
    "type": "char"
  },
  "company_registry_label": {
    "depends": [
      "country_id"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Company ID Label",
    "type": "char"
  },
  "company_type": {
    "depends": [
      "is_company"
    ],
    "readonly": false,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Company Type",
    "type": "selection"
  },
  "complete_name": {
    "depends": [
      "is_company",
      "name",
      "parent_id.name",
      "type",
      "company_name",
      "commercial_company_name"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Complete Name",
    "type": "char"
  },
  "contact_address": {
    "depends": [
      "street",
      "street2",
      "zip",
      "city",
      "state_id",
      "country_id",
      "country_id",
      "company_name",
      "state_id"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Complete Address",
    "type": "char"
  },
  "contact_address_inline": {
    "depends": [
      "contact_address"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Inlined Complete Address",
    "type": "char"
  },
  "contract_ids": {
    "depends": [],
    "domain": [],
    "readonly": true,
    "relation": "account.analytic.account",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Partner Contracts",
    "type": "one2many"
  },
  "country_code": {
    "depends": [
      "country_id.code"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": false,
    "string": "Country Code",
    "type": "char"
  },
  "country_id": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "res.country",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Country",
    "type": "many2one"
  },
  "create_date": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Created on",
    "type": "datetime"
  },
  "create_uid": {
    "depends": [],
    "domain": [],
    "readonly": true,
    "relation": "res.users",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Created by",
    "type": "many2one"
  },
  "credit": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Total Receivable",
    "type": "monetary"
  },
  "credit_limit": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Credit Limit",
    "type": "float"
  },
  "credit_to_invoice": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Credit To Invoice",
    "type": "monetary"
  },
  "currency_id": {
    "depends": [],
    "domain": [],
    "readonly": true,
    "relation": "res.currency",
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Currency",
    "type": "many2one"
  },
  "customer": {
    "depends": [
      "customer_rank"
    ],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Is a Customer",
    "type": "boolean"
  },
  "customer_rank": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Customer Rank",
    "type": "integer"
  },
  "customer_type": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Customer Type",
    "type": "selection"
  },
  "customer_type_id": {
    "depends": [
      "customer",
      "supplier"
    ],
    "domain": [],
    "readonly": false,
    "relation": "res.partner.type",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Partner Type",
    "type": "many2one"
  },
  "date_localization": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Geolocation Date",
    "type": "date"
  },
  "date_registered": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Date",
    "type": "date"
  },
  "days_sales_outstanding": {
    "depends": [
      "credit"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Days Sales Outstanding (DSO)",
    "type": "float"
  },
  "debit": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Total Payable",
    "type": "monetary"
  },
  "debit_limit": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Payable Limit",
    "type": "monetary"
  },
  "deleted": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Deleted",
    "type": "boolean"
  },
  "display_invoice_edi_format": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Display Invoice Edi Format",
    "type": "boolean"
  },
  "display_invoice_template_pdf_report_id": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Display Invoice Template Pdf Report",
    "type": "boolean"
  },
  "display_name": {
    "depends": [
      "name",
      "website_id",
      "complete_name",
      "email",
      "vat",
      "state_id",
      "country_id",
      "commercial_company_name",
      "name"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Display Name",
    "type": "char"
  },
  "document_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "groq.document",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Groq Documents",
    "type": "one2many"
  },
  "document_preview_url": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Document Preview Link",
    "type": "char"
  },
  "duplicated_bank_account_partners_count": {
    "depends": [
      "bank_ids"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Duplicated Bank Account Partners Count",
    "type": "integer"
  },
  "effective_from": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Effective From",
    "type": "date"
  },
  "effective_to": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Effective To",
    "type": "date"
  },
  "email": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Email",
    "type": "char"
  },
  "email_formatted": {
    "depends": [
      "name",
      "email"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Formatted Email",
    "type": "char"
  },
  "email_normalized": {
    "depends": [
      "email"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Normalized Email",
    "type": "char"
  },
  "employee": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Employee",
    "type": "boolean"
  },
  "employee_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "hr.employee",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Employees",
    "type": "one2many"
  },
  "employees_count": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Employees Count",
    "type": "integer"
  },
  "entity_description": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Entity Description",
    "type": "char"
  },
  "entity_type": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Entity Type",
    "type": "char"
  },
  "family_name": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Family Name",
    "type": "char"
  },
  "firstname": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "First name",
    "type": "char"
  },
  "fiscal_country_codes": {
    "depends": [
      "company_id"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Fiscal Country Codes",
    "type": "char"
  },
  "function": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Job Position",
    "type": "char"
  },
  "given_name": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Given Name",
    "type": "char"
  },
  "gps_coordinates": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "GPS Coordinates",
    "type": "char"
  },
  "groups": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "customer.group",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Group",
    "type": "many2many"
  },
  "gst": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "GST",
    "type": "selection"
  },
  "gst_effective_from": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "GST Effective From",
    "type": "date"
  },
  "gst_effective_to": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "GST Effective To",
    "type": "date"
  },
  "has_message": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Has Message",
    "type": "boolean"
  },
  "house_type_ids": {
    "depends": [],
    "domain": "[('builder_id','=', id)]",
    "readonly": false,
    "relation": "design.register",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "House Types",
    "type": "one2many"
  },
  "id": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "ID",
    "type": "integer"
  },
  "ignore_abnormal_invoice_amount": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Ignore Abnormal Invoice Amount",
    "type": "boolean"
  },
  "ignore_abnormal_invoice_date": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Ignore Abnormal Invoice Date",
    "type": "boolean"
  },
  "im_status": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "IM Status",
    "type": "char"
  },
  "image_1024": {
    "depends": [
      "image_1920"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Image 1024",
    "type": "binary"
  },
  "image_128": {
    "depends": [
      "image_1920"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Image 128",
    "type": "binary"
  },
  "image_1920": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Image",
    "type": "binary"
  },
  "image_256": {
    "depends": [
      "image_1920"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Image 256",
    "type": "binary"
  },
  "image_512": {
    "depends": [
      "image_1920"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Image 512",
    "type": "binary"
  },
  "industry_id": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "res.partner.industry",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Industry",
    "type": "many2one"
  },
  "invoice_edi_format": {
    "depends": [
      "commercial_partner_id.country_code"
    ],
    "readonly": false,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "eInvoice format",
    "type": "selection"
  },
  "invoice_edi_format_store": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Invoice Edi Format Store",
    "type": "char"
  },
  "invoice_ids": {
    "depends": [],
    "domain": [],
    "readonly": true,
    "relation": "account.move",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Invoices",
    "type": "one2many"
  },
  "invoice_sending_method": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Invoice sending",
    "type": "selection"
  },
  "invoice_template_pdf_report_id": {
    "depends": [],
    "domain": "[('is_invoice_report', '=', True)]",
    "readonly": false,
    "relation": "ir.actions.report",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Invoice template",
    "type": "many2one"
  },
  "invoice_warn": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Invoice",
    "type": "selection"
  },
  "invoice_warn_msg": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Message for Invoice",
    "type": "text"
  },
  "is_blacklisted": {
    "depends": [
      "email_normalized"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Blacklist",
    "type": "boolean"
  },
  "is_coa_installed": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Is Coa Installed",
    "type": "boolean"
  },
  "is_company": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Is a Company",
    "type": "boolean"
  },
  "is_current": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Is Current",
    "type": "boolean"
  },
  "is_peppol_edi_format": {
    "depends": [
      "invoice_edi_format"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Is Peppol Edi Format",
    "type": "boolean"
  },
  "is_public": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Is Public",
    "type": "boolean"
  },
  "is_published": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Is Published",
    "type": "boolean"
  },
  "is_ubl_format": {
    "depends": [
      "invoice_edi_format"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Is Ubl Format",
    "type": "boolean"
  },
  "journal_item_count": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Journal Items",
    "type": "integer"
  },
  "lang": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Language",
    "type": "selection"
  },
  "last_updated": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Updated",
    "type": "date"
  },
  "lastname": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Last name",
    "type": "char"
  },
  "latitude": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Latitude",
    "type": "float"
  },
  "legal_entity_name": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Legal Entity Name",
    "type": "char"
  },
  "legal_name_effective_from": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Legal Name Effective From",
    "type": "date"
  },
  "legal_name_effective_to": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Legal Name Effective To",
    "type": "date"
  },
  "longitude": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Longitude",
    "type": "float"
  },
  "main_name_effective_from": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Main Name Effective From",
    "type": "date"
  },
  "map_html": {
    "depends": [
      "street",
      "partner_latitude",
      "partner_longitude"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Map HTML",
    "type": "text"
  },
  "meeting_count": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "# Meetings",
    "type": "integer"
  },
  "meeting_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "calendar.event",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Meetings",
    "type": "many2many"
  },
  "message_attachment_count": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Attachment Count",
    "type": "integer"
  },
  "message_bounce": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Bounce",
    "type": "integer"
  },
  "message_follower_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "mail.followers",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Followers",
    "type": "one2many"
  },
  "message_has_error": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Message Delivery error",
    "type": "boolean"
  },
  "message_has_error_counter": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Number of errors",
    "type": "integer"
  },
  "message_has_sms_error": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "SMS Delivery error",
    "type": "boolean"
  },
  "message_ids": {
    "depends": [],
    "domain": [
      [
        "message_type",
        "!=",
        "user_notification"
      ]
    ],
    "readonly": false,
    "relation": "mail.message",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Messages",
    "type": "one2many"
  },
  "message_is_follower": {
    "depends": [
      "message_follower_ids"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Is Follower",
    "type": "boolean"
  },
  "message_needaction": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Action Needed",
    "type": "boolean"
  },
  "message_needaction_counter": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Number of Actions",
    "type": "integer"
  },
  "message_partner_ids": {
    "depends": [
      "message_follower_ids"
    ],
    "domain": [],
    "readonly": false,
    "relation": "res.partner",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Followers (Partners)",
    "type": "many2many"
  },
  "mobile": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Mobile",
    "type": "char"
  },
  "mobile_blacklisted": {
    "depends": [
      "phone_sanitized"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Blacklisted Phone Is Mobile",
    "type": "boolean"
  },
  "my_activity_date_deadline": {
    "depends": [
      "activity_ids.date_deadline",
      "activity_ids.user_id"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "My Activity Deadline",
    "type": "date"
  },
  "myob_card_displayid": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "MYOB Card ID",
    "type": "char"
  },
  "myob_uid_id": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Myob Uid",
    "type": "char"
  },
  "name": {
    "depends": [
      "firstname",
      "lastname"
    ],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Name",
    "type": "char"
  },
  "on_time_rate": {
    "depends": [
      "purchase_line_ids"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "On-Time Delivery Rate",
    "type": "float"
  },
  "opportunity_count": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Opportunity Count",
    "type": "integer"
  },
  "opportunity_ids": {
    "depends": [
      "opportunity_ids.type"
    ],
    "domain": [
      [
        "type",
        "=",
        "opportunity"
      ]
    ],
    "readonly": false,
    "relation": "crm.lead",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Opportunities",
    "type": "one2many"
  },
  "organisation_name": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Organisation Name",
    "type": "char"
  },
  "other_given_name": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Other Given Name",
    "type": "char"
  },
  "parent_id": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "res.partner",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Related Company",
    "type": "many2one"
  },
  "parent_name": {
    "depends": [
      "parent_id.name"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": false,
    "string": "Parent name",
    "type": "char"
  },
  "partner_company_registry_placeholder": {
    "depends": [
      "country_id"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Partner Company Registry Placeholder",
    "type": "char"
  },
  "partner_gid": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Company database ID",
    "type": "integer"
  },
  "partner_latitude": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Geo Latitude",
    "type": "float"
  },
  "partner_longitude": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Geo Longitude",
    "type": "float"
  },
  "partner_share": {
    "depends": [
      "user_ids.share",
      "user_ids.active"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Share Partner",
    "type": "boolean"
  },
  "partner_vat_placeholder": {
    "depends": [
      "country_id"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Partner Vat Placeholder",
    "type": "char"
  },
  "payment_token_count": {
    "depends": [
      "payment_token_ids"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Payment Token Count",
    "type": "integer"
  },
  "payment_token_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "payment.token",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Payment Tokens",
    "type": "one2many"
  },
  "peppol_eas": {
    "depends": [
      "country_code",
      "vat",
      "company_registry"
    ],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Peppol e-address (EAS)",
    "type": "selection"
  },
  "peppol_endpoint": {
    "depends": [
      "country_code",
      "vat",
      "company_registry",
      "peppol_eas"
    ],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Peppol Endpoint",
    "type": "char"
  },
  "phone": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Phone",
    "type": "char"
  },
  "phone_blacklisted": {
    "depends": [
      "phone_sanitized"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Blacklisted Phone is Phone",
    "type": "boolean"
  },
  "phone_mobile_search": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Phone/Mobile",
    "type": "char"
  },
  "phone_sanitized": {
    "depends": [
      "mobile",
      "phone",
      "country_id"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Sanitized Number",
    "type": "char"
  },
  "phone_sanitized_blacklisted": {
    "depends": [
      "phone_sanitized"
    ],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Phone Blacklisted",
    "type": "boolean"
  },
  "physical_address_effective_from": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Physical Address Effective From",
    "type": "date"
  },
  "physical_address_effective_to": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Physical Address Effective To",
    "type": "date"
  },
  "physical_address_postcode": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Physical Address Postcode",
    "type": "char"
  },
  "physical_address_state": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Physical Address State",
    "type": "char"
  },
  "picking_warn": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Stock Picking",
    "type": "selection"
  },
  "picking_warn_msg": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Message for Stock Picking",
    "type": "text"
  },
  "plan_to_change_bike": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Plan To Change Bike",
    "type": "boolean"
  },
  "plan_to_change_car": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Plan To Change Car",
    "type": "boolean"
  },
  "project_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "project.project",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Projects",
    "type": "one2many"
  },
  "property_account_payable_id": {
    "depends": [],
    "domain": "[('account_type', '=', 'liability_payable'), ('deprecated', '=', False)]",
    "readonly": false,
    "relation": "account.account",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Account Payable",
    "type": "many2one"
  },
  "property_account_position_id": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "account.fiscal.position",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Fiscal Position",
    "type": "many2one"
  },
  "property_account_receivable_id": {
    "depends": [],
    "domain": "[('account_type', '=', 'asset_receivable'), ('deprecated', '=', False)]",
    "readonly": false,
    "relation": "account.account",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Account Receivable",
    "type": "many2one"
  },
  "property_delivery_carrier_id": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "delivery.carrier",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Delivery Method",
    "type": "many2one"
  },
  "property_inbound_payment_method_line_id": {
    "depends": [],
    "domain": [
      [
        "payment_type",
        "=",
        "inbound"
      ],
      [
        "company_id",
        "=",
        1
      ]
    ],
    "readonly": false,
    "relation": "account.payment.method.line",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Property Inbound Payment Method Line",
    "type": "many2one"
  },
  "property_outbound_payment_method_line_id": {
    "depends": [],
    "domain": [
      [
        "payment_type",
        "=",
        "outbound"
      ],
      [
        "company_id",
        "=",
        1
      ]
    ],
    "readonly": false,
    "relation": "account.payment.method.line",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Property Outbound Payment Method Line",
    "type": "many2one"
  },
  "property_payment_term_id": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "account.payment.term",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Customer Payment Terms",
    "type": "many2one"
  },
  "property_product_pricelist": {
    "depends": [
      "country_id",
      "specific_property_product_pricelist"
    ],
    "domain": [
      [
        "company_id",
        "in",
        [
          1,
          false
        ]
      ]
    ],
    "readonly": false,
    "relation": "product.pricelist",
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Pricelist",
    "type": "many2one"
  },
  "property_purchase_currency_id": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "res.currency",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Supplier Currency",
    "type": "many2one"
  },
  "property_stock_customer": {
    "depends": [],
    "domain": "[('company_id', 'in', [allowed_company_ids[0]] + [False])] + ['|', ('company_id', '=', False), ('company_id', '=', allowed_company_ids[0])]",
    "readonly": false,
    "relation": "stock.location",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Customer Location",
    "type": "many2one"
  },
  "property_stock_supplier": {
    "depends": [],
    "domain": "[('company_id', 'in', [allowed_company_ids[0]] + [False])] + ['|', ('company_id', '=', False), ('company_id', '=', allowed_company_ids[0])]",
    "readonly": false,
    "relation": "stock.location",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Vendor Location",
    "type": "many2one"
  },
  "property_supplier_payment_term_id": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "account.payment.term",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Vendor Payment Terms",
    "type": "many2one"
  },
  "purchase_line_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "purchase.order.line",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Purchase Lines",
    "type": "one2many"
  },
  "purchase_order_count": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Purchase Order Count",
    "type": "integer"
  },
  "purchase_warn": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Purchase Order Warning",
    "type": "selection"
  },
  "purchase_warn_msg": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Message for Purchase Order",
    "type": "text"
  },
  "rating_ids": {
    "depends": [],
    "domain": [
      [
        "res_model",
        "=",
        "res.partner"
      ]
    ],
    "readonly": false,
    "relation": "rating.rating",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Ratings",
    "type": "one2many"
  },
  "receipt_reminder_email": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Receipt Reminder",
    "type": "boolean"
  },
  "ref": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Reference",
    "type": "char"
  },
  "ref_company_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "res.company",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Companies that refers to partner",
    "type": "one2many"
  },
  "reminder_date_before_receipt": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Days Before Receipt",
    "type": "integer"
  },
  "replaced_from": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Replaced From",
    "type": "date"
  },
  "retrieved_date": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Retrieved Date",
    "type": "datetime"
  },
  "sale_order_count": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Sale Order Count",
    "type": "integer"
  },
  "sale_order_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "sale.order",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Sales Order",
    "type": "one2many"
  },
  "sale_warn": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Sales Warnings",
    "type": "selection"
  },
  "sale_warn_msg": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Message for Sales Order",
    "type": "text"
  },
  "same_company_registry_partner_id": {
    "depends": [
      "vat",
      "company_id",
      "company_registry"
    ],
    "domain": [],
    "readonly": true,
    "relation": "res.partner",
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Partner with same Company Registry",
    "type": "many2one"
  },
  "same_vat_partner_id": {
    "depends": [
      "vat",
      "company_id",
      "company_registry"
    ],
    "domain": [],
    "readonly": true,
    "relation": "res.partner",
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Partner with same Tax ID",
    "type": "many2one"
  },
  "self": {
    "depends": [],
    "domain": [],
    "readonly": true,
    "relation": "res.partner",
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Self",
    "type": "many2one"
  },
  "sh_contact_google_location": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Enter Location",
    "type": "char"
  },
  "sh_contact_place_text": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Enter location",
    "type": "char"
  },
  "sh_contact_place_text_main_string": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Enter location ",
    "type": "char"
  },
  "show_carrier": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Show Carrier",
    "type": "boolean"
  },
  "show_credit_limit": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Show Credit Limit",
    "type": "boolean"
  },
  "show_group": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Show Group",
    "type": "boolean"
  },
  "signer_count": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Signer Count",
    "type": "integer"
  },
  "signer_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "sign.oca.request.signer",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Signer",
    "type": "one2many"
  },
  "signup_type": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Signup Token Type",
    "type": "char"
  },
  "site_addresses_sale_order_ids": {
    "depends": [
      "sale_order_ids"
    ],
    "domain": [],
    "readonly": true,
    "relation": "res.partner",
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Opportunities",
    "type": "many2many"
  },
  "specific_property_product_pricelist": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "product.pricelist",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Specific Property Product Pricelist",
    "type": "many2one"
  },
  "stairmaster_entity_id": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "res.partner",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Stairmaster Entity Name",
    "type": "many2one"
  },
  "stairmaster_short_name": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Stairmaster Short Name",
    "type": "char"
  },
  "starred_message_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "mail.message",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Starred Message",
    "type": "many2many"
  },
  "state_id": {
    "depends": [],
    "domain": "[('country_id', '=?', country_id)]",
    "readonly": false,
    "relation": "res.country.state",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "State",
    "type": "many2one"
  },
  "status_code": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Status Code",
    "type": "char"
  },
  "street": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Street",
    "type": "char"
  },
  "street2": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Street2",
    "type": "char"
  },
  "supervisor_ids": {
    "depends": [
      "supervisor_ids.type_contact"
    ],
    "domain": [
      [
        "type_contact",
        "=",
        "supervisor"
      ]
    ],
    "readonly": false,
    "relation": "res.partner",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Supervisors",
    "type": "one2many"
  },
  "supplier": {
    "depends": [
      "supplier_rank"
    ],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Is a Vendor",
    "type": "boolean"
  },
  "supplier_invoice_count": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "# Vendor Bills",
    "type": "integer"
  },
  "supplier_rank": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Supplier Rank",
    "type": "integer"
  },
  "task_count": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "# Tasks",
    "type": "integer"
  },
  "task_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "project.task",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Tasks",
    "type": "one2many"
  },
  "title": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "res.partner.title",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Title",
    "type": "many2one"
  },
  "total_invoiced": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Total Invoiced",
    "type": "monetary"
  },
  "trading_name": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Trade License",
    "type": "char"
  },
  "transport_note": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Carrier Notes",
    "type": "char"
  },
  "trust": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Degree of trust you have in this debtor",
    "type": "selection"
  },
  "type": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Address Type",
    "type": "selection"
  },
  "type_abn": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Type",
    "type": "selection"
  },
  "type_contact": {
    "depends": [
      "type"
    ],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Type of Contact",
    "type": "selection"
  },
  "tz": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Timezone",
    "type": "selection"
  },
  "tz_offset": {
    "depends": [
      "tz"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Timezone offset",
    "type": "char"
  },
  "use_partner_credit_limit": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Partner Limit",
    "type": "boolean"
  },
  "user_id": {
    "depends": [
      "parent_id"
    ],
    "domain": [],
    "readonly": false,
    "relation": "res.users",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Salesperson",
    "type": "many2one"
  },
  "user_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "res.users",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Users",
    "type": "one2many"
  },
  "user_livechat_username": {
    "depends": [
      "user_ids.livechat_username"
    ],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "User Livechat Username",
    "type": "char"
  },
  "vat": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "ABN",
    "type": "char"
  },
  "vat_label": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Tax ID Label",
    "type": "char"
  },
  "view_child_in_list": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "View Child Address in List",
    "type": "boolean"
  },
  "visitor_ids": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "website.visitor",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Visitors",
    "type": "one2many"
  },
  "website": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Website Link",
    "type": "char"
  },
  "website_id": {
    "depends": [],
    "domain": [],
    "readonly": false,
    "relation": "website",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Website",
    "type": "many2one"
  },
  "website_message_ids": {
    "depends": [],
    "domain": [
      [
        "model",
        "=",
        "res.partner"
      ],
      [
        "message_type",
        "in",
        [
          "comment",
          "email",
          "email_outgoing"
        ]
      ]
    ],
    "readonly": false,
    "relation": "mail.message",
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": true,
    "string": "Website Messages",
    "type": "one2many"
  },
  "website_published": {
    "depends": [
      "is_published",
      "website_id"
    ],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": false,
    "store": false,
    "string": "Visible on current website",
    "type": "boolean"
  },
  "website_url": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": false,
    "sortable": false,
    "store": false,
    "string": "Website URL",
    "type": "char"
  },
  "write_date": {
    "depends": [],
    "readonly": true,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Last Updated on",
    "type": "datetime"
  },
  "write_uid": {
    "depends": [],
    "domain": [],
    "readonly": true,
    "relation": "res.users",
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Last Updated by",
    "type": "many2one"
  },
  "x_stairbiz_id": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Stairbiz ID",
    "type": "char"
  },
  "x_stairbiz_migrated": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Migrated from Stairbiz",
    "type": "boolean"
  },
  "x_stairbiz_migration_date": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Stairbiz Migration Date",
    "type": "datetime"
  },
  "zip": {
    "depends": [],
    "readonly": false,
    "required": false,
    "searchable": true,
    "sortable": true,
    "store": true,
    "string": "Zip",
    "type": "char"
  }
}
2025-05-05 10:39:49,437 - ERROR - Unexpected response format for res.partner: {'abn': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'ABN', 'type': 'char'}, 'abn_active': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'ABN Active', 'type': 'boolean'}, 'acn': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'ACN', 'type': 'char'}, 'active': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Active', 'type': 'boolean'}, 'active_lang_count': {'depends': ['lang'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Active Lang Count', 'type': 'integer'}, 'activity_calendar_event_id': {'depends': ['activity_ids.calendar_event_id'], 'domain': [], 'readonly': True, 'relation': 'calendar.event', 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Next Activity Calendar Event', 'type': 'many2one'}, 'activity_date_deadline': {'depends': ['activity_ids.date_deadline'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Next Activity Deadline', 'type': 'date'}, 'activity_exception_decoration': {'depends': ['activity_ids.activity_type_id.decoration_type', 'activity_ids.activity_type_id.icon'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Activity Exception Decoration', 'type': 'selection'}, 'activity_exception_icon': {'depends': ['activity_ids.activity_type_id.decoration_type', 'activity_ids.activity_type_id.icon'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Icon', 'type': 'char'}, 'activity_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'mail.activity', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Activities', 'type': 'one2many'}, 'activity_state': {'depends': ['activity_ids.state'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Activity State', 'type': 'selection'}, 'activity_summary': {'depends': ['activity_ids.summary'], 'readonly': False, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Next Activity Summary', 'type': 'char'}, 'activity_type_icon': {'depends': ['activity_ids.icon'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Activity Type Icon', 'type': 'char'}, 'activity_type_id': {'depends': ['activity_ids.activity_type_id'], 'readonly': False, 'relation': 'mail.activity.type', 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Next Activity Type', 'type': 'many2one'}, 'activity_user_id': {'depends': ['activity_ids.user_id'], 'domain': [], 'readonly': True, 'relation': 'res.users', 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Responsible User', 'type': 'many2one'}, 'additional_info': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Additional info', 'type': 'char'}, 'asic_number': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'ASIC Number', 'type': 'char'}, 'attach_count': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Documents', 'type': 'integer'}, 'attachment_count': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Attachment Count', 'type': 'integer'}, 'attachment_ids': {'depends': [], 'domain': [], 'readonly': True, 'relation': 'acs.document', 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Attachments', 'type': 'many2many'}, 'auto_issue': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Auto Issue', 'type': 'boolean'}, 'autopost_bills': {'depends': [], 'readonly': False, 'required': True, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Auto-post bills', 'type': 'selection'}, 'avatar_1024': {'depends': ['name', 'user_ids.share', 'image_1024', 'is_company', 'type', 'name', 'image_1024'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Avatar 1024', 'type': 'binary'}, 'avatar_128': {'depends': ['name', 'user_ids.share', 'image_128', 'is_company', 'type', 'name', 'image_128'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Avatar 128', 'type': 'binary'}, 'avatar_1920': {'depends': ['name', 'user_ids.share', 'image_1920', 'is_company', 'type', 'name', 'image_1920'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Avatar', 'type': 'binary'}, 'avatar_256': {'depends': ['name', 'user_ids.share', 'image_256', 'is_company', 'type', 'name', 'image_256'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Avatar 256', 'type': 'binary'}, 'avatar_512': {'depends': ['name', 'user_ids.share', 'image_512', 'is_company', 'type', 'name', 'image_512'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Avatar 512', 'type': 'binary'}, 'bank_account_count': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Bank', 'type': 'integer'}, 'bank_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'res.partner.bank', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Banks', 'type': 'one2many'}, 'barcode': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Barcode', 'type': 'char'}, 'builder_group_id': {'depends': ['builder_id'], 'domain': [], 'readonly': True, 'relation': 'customer.group', 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Customer Group', 'type': 'many2one'}, 'builder_id': {'depends': [], 'domain': [['type_contact', '=', 'builders']], 'readonly': False, 'relation': 'res.partner', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Builder', 'type': 'many2one'}, 'business_name': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Business Name', 'type': 'char'}, 'business_name_effective_from': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Business Name Effective From', 'type': 'date'}, 'buyer_id': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'res.users', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Buyer', 'type': 'many2one'}, 'calendar_last_notif_ack': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Last notification marked as read from base Calendar', 'type': 'datetime'}, 'can_publish': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Can Publish', 'type': 'boolean'}, 'carrier': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'res.partner', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Carrier', 'type': 'many2one'}, 'category_id': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'res.partner.category', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Tags', 'type': 'many2many'}, 'certifications_company_count': {'depends': ['is_company', 'child_ids.certifications_count'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Company Certifications Count', 'type': 'integer'}, 'certifications_count': {'depends': ['is_company'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Certifications Count', 'type': 'integer'}, 'channel_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'discuss.channel', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Channels', 'type': 'many2many'}, 'child_ids': {'depends': ['child_ids.active'], 'domain': [['active', '=', True]], 'readonly': False, 'relation': 'res.partner', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Contact', 'type': 'one2many'}, 'city': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'City', 'type': 'char'}, 'classification_rule_count': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Classification Rule Count', 'type': 'integer'}, 'classification_rule_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'document.classification.rule', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Classification Rules', 'type': 'one2many'}, 'color': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Color Index', 'type': 'integer'}, 'combined_document_ids': {'depends': ['document_ids'], 'domain': [], 'readonly': False, 'relation': 'groq.document', 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'All Documents', 'type': 'one2many'}, 'comment': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Notes', 'type': 'html'}, 'commercial_company_name': {'depends': ['company_name', 'parent_id.is_company', 'commercial_partner_id.name'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Company Name Entity', 'type': 'char'}, 'commercial_partner_id': {'depends': ['is_company', 'parent_id.commercial_partner_id'], 'domain': [], 'readonly': True, 'relation': 'res.partner', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Commercial Entity', 'type': 'many2one'}, 'company_id': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'res.company', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Company', 'type': 'many2one'}, 'company_name': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Company Name', 'type': 'char'}, 'company_registry': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Company ID', 'type': 'char'}, 'company_registry_label': {'depends': ['country_id'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Company ID Label', 'type': 'char'}, 'company_type': {'depends': ['is_company'], 'readonly': False, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Company Type', 'type': 'selection'}, 'complete_name': {'depends': ['is_company', 'name', 'parent_id.name', 'type', 'company_name', 'commercial_company_name'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Complete Name', 'type': 'char'}, 'contact_address': {'depends': ['street', 'street2', 'zip', 'city', 'state_id', 'country_id', 'country_id', 'company_name', 'state_id'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Complete Address', 'type': 'char'}, 'contact_address_inline': {'depends': ['contact_address'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Inlined Complete Address', 'type': 'char'}, 'contract_ids': {'depends': [], 'domain': [], 'readonly': True, 'relation': 'account.analytic.account', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Partner Contracts', 'type': 'one2many'}, 'country_code': {'depends': ['country_id.code'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': True, 'store': False, 'string': 'Country Code', 'type': 'char'}, 'country_id': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'res.country', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Country', 'type': 'many2one'}, 'create_date': {'depends': [], 'readonly': True, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Created on', 'type': 'datetime'}, 'create_uid': {'depends': [], 'domain': [], 'readonly': True, 'relation': 'res.users', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Created by', 'type': 'many2one'}, 'credit': {'depends': [], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Total Receivable', 'type': 'monetary'}, 'credit_limit': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Credit Limit', 'type': 'float'}, 'credit_to_invoice': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Credit To Invoice', 'type': 'monetary'}, 'currency_id': {'depends': [], 'domain': [], 'readonly': True, 'relation': 'res.currency', 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Currency', 'type': 'many2one'}, 'customer': {'depends': ['customer_rank'], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Is a Customer', 'type': 'boolean'}, 'customer_rank': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Customer Rank', 'type': 'integer'}, 'customer_type': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Customer Type', 'type': 'selection'}, 'customer_type_id': {'depends': ['customer', 'supplier'], 'domain': [], 'readonly': False, 'relation': 'res.partner.type', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Partner Type', 'type': 'many2one'}, 'date_localization': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Geolocation Date', 'type': 'date'}, 'date_registered': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Date', 'type': 'date'}, 'days_sales_outstanding': {'depends': ['credit'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Days Sales Outstanding (DSO)', 'type': 'float'}, 'debit': {'depends': [], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Total Payable', 'type': 'monetary'}, 'debit_limit': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Payable Limit', 'type': 'monetary'}, 'deleted': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Deleted', 'type': 'boolean'}, 'display_invoice_edi_format': {'depends': [], 'readonly': False, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Display Invoice Edi Format', 'type': 'boolean'}, 'display_invoice_template_pdf_report_id': {'depends': [], 'readonly': False, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Display Invoice Template Pdf Report', 'type': 'boolean'}, 'display_name': {'depends': ['name', 'website_id', 'complete_name', 'email', 'vat', 'state_id', 'country_id', 'commercial_company_name', 'name'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Display Name', 'type': 'char'}, 'document_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'groq.document', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Groq Documents', 'type': 'one2many'}, 'document_preview_url': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Document Preview Link', 'type': 'char'}, 'duplicated_bank_account_partners_count': {'depends': ['bank_ids'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Duplicated Bank Account Partners Count', 'type': 'integer'}, 'effective_from': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Effective From', 'type': 'date'}, 'effective_to': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Effective To', 'type': 'date'}, 'email': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Email', 'type': 'char'}, 'email_formatted': {'depends': ['name', 'email'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Formatted Email', 'type': 'char'}, 'email_normalized': {'depends': ['email'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Normalized Email', 'type': 'char'}, 'employee': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Employee', 'type': 'boolean'}, 'employee_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'hr.employee', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Employees', 'type': 'one2many'}, 'employees_count': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Employees Count', 'type': 'integer'}, 'entity_description': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Entity Description', 'type': 'char'}, 'entity_type': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Entity Type', 'type': 'char'}, 'family_name': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Family Name', 'type': 'char'}, 'firstname': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'First name', 'type': 'char'}, 'fiscal_country_codes': {'depends': ['company_id'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Fiscal Country Codes', 'type': 'char'}, 'function': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Job Position', 'type': 'char'}, 'given_name': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Given Name', 'type': 'char'}, 'gps_coordinates': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'GPS Coordinates', 'type': 'char'}, 'groups': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'customer.group', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Group', 'type': 'many2many'}, 'gst': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'GST', 'type': 'selection'}, 'gst_effective_from': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'GST Effective From', 'type': 'date'}, 'gst_effective_to': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'GST Effective To', 'type': 'date'}, 'has_message': {'depends': [], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Has Message', 'type': 'boolean'}, 'house_type_ids': {'depends': [], 'domain': "[('builder_id','=', id)]", 'readonly': False, 'relation': 'design.register', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'House Types', 'type': 'one2many'}, 'id': {'depends': [], 'readonly': True, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'ID', 'type': 'integer'}, 'ignore_abnormal_invoice_amount': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Ignore Abnormal Invoice Amount', 'type': 'boolean'}, 'ignore_abnormal_invoice_date': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Ignore Abnormal Invoice Date', 'type': 'boolean'}, 'im_status': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'IM Status', 'type': 'char'}, 'image_1024': {'depends': ['image_1920'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Image 1024', 'type': 'binary'}, 'image_128': {'depends': ['image_1920'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Image 128', 'type': 'binary'}, 'image_1920': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Image', 'type': 'binary'}, 'image_256': {'depends': ['image_1920'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Image 256', 'type': 'binary'}, 'image_512': {'depends': ['image_1920'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Image 512', 'type': 'binary'}, 'industry_id': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'res.partner.industry', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Industry', 'type': 'many2one'}, 'invoice_edi_format': {'depends': ['commercial_partner_id.country_code'], 'readonly': False, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'eInvoice format', 'type': 'selection'}, 'invoice_edi_format_store': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Invoice Edi Format Store', 'type': 'char'}, 'invoice_ids': {'depends': [], 'domain': [], 'readonly': True, 'relation': 'account.move', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Invoices', 'type': 'one2many'}, 'invoice_sending_method': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Invoice sending', 'type': 'selection'}, 'invoice_template_pdf_report_id': {'depends': [], 'domain': "[('is_invoice_report', '=', True)]", 'readonly': False, 'relation': 'ir.actions.report', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Invoice template', 'type': 'many2one'}, 'invoice_warn': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Invoice', 'type': 'selection'}, 'invoice_warn_msg': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Message for Invoice', 'type': 'text'}, 'is_blacklisted': {'depends': ['email_normalized'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Blacklist', 'type': 'boolean'}, 'is_coa_installed': {'depends': [], 'readonly': False, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Is Coa Installed', 'type': 'boolean'}, 'is_company': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Is a Company', 'type': 'boolean'}, 'is_current': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Is Current', 'type': 'boolean'}, 'is_peppol_edi_format': {'depends': ['invoice_edi_format'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Is Peppol Edi Format', 'type': 'boolean'}, 'is_public': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Is Public', 'type': 'boolean'}, 'is_published': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Is Published', 'type': 'boolean'}, 'is_ubl_format': {'depends': ['invoice_edi_format'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Is Ubl Format', 'type': 'boolean'}, 'journal_item_count': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Journal Items', 'type': 'integer'}, 'lang': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Language', 'type': 'selection'}, 'last_updated': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Updated', 'type': 'date'}, 'lastname': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Last name', 'type': 'char'}, 'latitude': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Latitude', 'type': 'float'}, 'legal_entity_name': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Legal Entity Name', 'type': 'char'}, 'legal_name_effective_from': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Legal Name Effective From', 'type': 'date'}, 'legal_name_effective_to': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Legal Name Effective To', 'type': 'date'}, 'longitude': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Longitude', 'type': 'float'}, 'main_name_effective_from': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Main Name Effective From', 'type': 'date'}, 'map_html': {'depends': ['street', 'partner_latitude', 'partner_longitude'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Map HTML', 'type': 'text'}, 'meeting_count': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': '# Meetings', 'type': 'integer'}, 'meeting_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'calendar.event', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Meetings', 'type': 'many2many'}, 'message_attachment_count': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Attachment Count', 'type': 'integer'}, 'message_bounce': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Bounce', 'type': 'integer'}, 'message_follower_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'mail.followers', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Followers', 'type': 'one2many'}, 'message_has_error': {'depends': [], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Message Delivery error', 'type': 'boolean'}, 'message_has_error_counter': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Number of errors', 'type': 'integer'}, 'message_has_sms_error': {'depends': [], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'SMS Delivery error', 'type': 'boolean'}, 'message_ids': {'depends': [], 'domain': [['message_type', '!=', 'user_notification']], 'readonly': False, 'relation': 'mail.message', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Messages', 'type': 'one2many'}, 'message_is_follower': {'depends': ['message_follower_ids'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Is Follower', 'type': 'boolean'}, 'message_needaction': {'depends': [], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Action Needed', 'type': 'boolean'}, 'message_needaction_counter': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Number of Actions', 'type': 'integer'}, 'message_partner_ids': {'depends': ['message_follower_ids'], 'domain': [], 'readonly': False, 'relation': 'res.partner', 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Followers (Partners)', 'type': 'many2many'}, 'mobile': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Mobile', 'type': 'char'}, 'mobile_blacklisted': {'depends': ['phone_sanitized'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Blacklisted Phone Is Mobile', 'type': 'boolean'}, 'my_activity_date_deadline': {'depends': ['activity_ids.date_deadline', 'activity_ids.user_id'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'My Activity Deadline', 'type': 'date'}, 'myob_card_displayid': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'MYOB Card ID', 'type': 'char'}, 'myob_uid_id': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Myob Uid', 'type': 'char'}, 'name': {'depends': ['firstname', 'lastname'], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Name', 'type': 'char'}, 'on_time_rate': {'depends': ['purchase_line_ids'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'On-Time Delivery Rate', 'type': 'float'}, 'opportunity_count': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Opportunity Count', 'type': 'integer'}, 'opportunity_ids': {'depends': ['opportunity_ids.type'], 'domain': [['type', '=', 'opportunity']], 'readonly': False, 'relation': 'crm.lead', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Opportunities', 'type': 'one2many'}, 'organisation_name': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Organisation Name', 'type': 'char'}, 'other_given_name': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Other Given Name', 'type': 'char'}, 'parent_id': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'res.partner', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Related Company', 'type': 'many2one'}, 'parent_name': {'depends': ['parent_id.name'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': True, 'store': False, 'string': 'Parent name', 'type': 'char'}, 'partner_company_registry_placeholder': {'depends': ['country_id'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Partner Company Registry Placeholder', 'type': 'char'}, 'partner_gid': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Company database ID', 'type': 'integer'}, 'partner_latitude': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Geo Latitude', 'type': 'float'}, 'partner_longitude': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Geo Longitude', 'type': 'float'}, 'partner_share': {'depends': ['user_ids.share', 'user_ids.active'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Share Partner', 'type': 'boolean'}, 'partner_vat_placeholder': {'depends': ['country_id'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Partner Vat Placeholder', 'type': 'char'}, 'payment_token_count': {'depends': ['payment_token_ids'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Payment Token Count', 'type': 'integer'}, 'payment_token_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'payment.token', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Payment Tokens', 'type': 'one2many'}, 'peppol_eas': {'depends': ['country_code', 'vat', 'company_registry'], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Peppol e-address (EAS)', 'type': 'selection'}, 'peppol_endpoint': {'depends': ['country_code', 'vat', 'company_registry', 'peppol_eas'], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Peppol Endpoint', 'type': 'char'}, 'phone': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Phone', 'type': 'char'}, 'phone_blacklisted': {'depends': ['phone_sanitized'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Blacklisted Phone is Phone', 'type': 'boolean'}, 'phone_mobile_search': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Phone/Mobile', 'type': 'char'}, 'phone_sanitized': {'depends': ['mobile', 'phone', 'country_id'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Sanitized Number', 'type': 'char'}, 'phone_sanitized_blacklisted': {'depends': ['phone_sanitized'], 'readonly': True, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Phone Blacklisted', 'type': 'boolean'}, 'physical_address_effective_from': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Physical Address Effective From', 'type': 'date'}, 'physical_address_effective_to': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Physical Address Effective To', 'type': 'date'}, 'physical_address_postcode': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Physical Address Postcode', 'type': 'char'}, 'physical_address_state': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Physical Address State', 'type': 'char'}, 'picking_warn': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Stock Picking', 'type': 'selection'}, 'picking_warn_msg': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Message for Stock Picking', 'type': 'text'}, 'plan_to_change_bike': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Plan To Change Bike', 'type': 'boolean'}, 'plan_to_change_car': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Plan To Change Car', 'type': 'boolean'}, 'project_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'project.project', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Projects', 'type': 'one2many'}, 'property_account_payable_id': {'depends': [], 'domain': "[('account_type', '=', 'liability_payable'), ('deprecated', '=', False)]", 'readonly': False, 'relation': 'account.account', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Account Payable', 'type': 'many2one'}, 'property_account_position_id': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'account.fiscal.position', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Fiscal Position', 'type': 'many2one'}, 'property_account_receivable_id': {'depends': [], 'domain': "[('account_type', '=', 'asset_receivable'), ('deprecated', '=', False)]", 'readonly': False, 'relation': 'account.account', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Account Receivable', 'type': 'many2one'}, 'property_delivery_carrier_id': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'delivery.carrier', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Delivery Method', 'type': 'many2one'}, 'property_inbound_payment_method_line_id': {'depends': [], 'domain': [['payment_type', '=', 'inbound'], ['company_id', '=', 1]], 'readonly': False, 'relation': 'account.payment.method.line', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Property Inbound Payment Method Line', 'type': 'many2one'}, 'property_outbound_payment_method_line_id': {'depends': [], 'domain': [['payment_type', '=', 'outbound'], ['company_id', '=', 1]], 'readonly': False, 'relation': 'account.payment.method.line', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Property Outbound Payment Method Line', 'type': 'many2one'}, 'property_payment_term_id': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'account.payment.term', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Customer Payment Terms', 'type': 'many2one'}, 'property_product_pricelist': {'depends': ['country_id', 'specific_property_product_pricelist'], 'domain': [['company_id', 'in', [1, False]]], 'readonly': False, 'relation': 'product.pricelist', 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Pricelist', 'type': 'many2one'}, 'property_purchase_currency_id': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'res.currency', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Supplier Currency', 'type': 'many2one'}, 'property_stock_customer': {'depends': [], 'domain': "[('company_id', 'in', [allowed_company_ids[0]] + [False])] + ['|', ('company_id', '=', False), ('company_id', '=', allowed_company_ids[0])]", 'readonly': False, 'relation': 'stock.location', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Customer Location', 'type': 'many2one'}, 'property_stock_supplier': {'depends': [], 'domain': "[('company_id', 'in', [allowed_company_ids[0]] + [False])] + ['|', ('company_id', '=', False), ('company_id', '=', allowed_company_ids[0])]", 'readonly': False, 'relation': 'stock.location', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Vendor Location', 'type': 'many2one'}, 'property_supplier_payment_term_id': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'account.payment.term', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Vendor Payment Terms', 'type': 'many2one'}, 'purchase_line_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'purchase.order.line', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Purchase Lines', 'type': 'one2many'}, 'purchase_order_count': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Purchase Order Count', 'type': 'integer'}, 'purchase_warn': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Purchase Order Warning', 'type': 'selection'}, 'purchase_warn_msg': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Message for Purchase Order', 'type': 'text'}, 'rating_ids': {'depends': [], 'domain': [['res_model', '=', 'res.partner']], 'readonly': False, 'relation': 'rating.rating', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Ratings', 'type': 'one2many'}, 'receipt_reminder_email': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Receipt Reminder', 'type': 'boolean'}, 'ref': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Reference', 'type': 'char'}, 'ref_company_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'res.company', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Companies that refers to partner', 'type': 'one2many'}, 'reminder_date_before_receipt': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Days Before Receipt', 'type': 'integer'}, 'replaced_from': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Replaced From', 'type': 'date'}, 'retrieved_date': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Retrieved Date', 'type': 'datetime'}, 'sale_order_count': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Sale Order Count', 'type': 'integer'}, 'sale_order_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'sale.order', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Sales Order', 'type': 'one2many'}, 'sale_warn': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Sales Warnings', 'type': 'selection'}, 'sale_warn_msg': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Message for Sales Order', 'type': 'text'}, 'same_company_registry_partner_id': {'depends': ['vat', 'company_id', 'company_registry'], 'domain': [], 'readonly': True, 'relation': 'res.partner', 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Partner with same Company Registry', 'type': 'many2one'}, 'same_vat_partner_id': {'depends': ['vat', 'company_id', 'company_registry'], 'domain': [], 'readonly': True, 'relation': 'res.partner', 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Partner with same Tax ID', 'type': 'many2one'}, 'self': {'depends': [], 'domain': [], 'readonly': True, 'relation': 'res.partner', 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Self', 'type': 'many2one'}, 'sh_contact_google_location': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Enter Location', 'type': 'char'}, 'sh_contact_place_text': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Enter location', 'type': 'char'}, 'sh_contact_place_text_main_string': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Enter location ', 'type': 'char'}, 'show_carrier': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Show Carrier', 'type': 'boolean'}, 'show_credit_limit': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Show Credit Limit', 'type': 'boolean'}, 'show_group': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Show Group', 'type': 'boolean'}, 'signer_count': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Signer Count', 'type': 'integer'}, 'signer_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'sign.oca.request.signer', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Signer', 'type': 'one2many'}, 'signup_type': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Signup Token Type', 'type': 'char'}, 'site_addresses_sale_order_ids': {'depends': ['sale_order_ids'], 'domain': [], 'readonly': True, 'relation': 'res.partner', 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Opportunities', 'type': 'many2many'}, 'specific_property_product_pricelist': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'product.pricelist', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Specific Property Product Pricelist', 'type': 'many2one'}, 'stairmaster_entity_id': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'res.partner', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Stairmaster Entity Name', 'type': 'many2one'}, 'stairmaster_short_name': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Stairmaster Short Name', 'type': 'char'}, 'starred_message_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'mail.message', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Starred Message', 'type': 'many2many'}, 'state_id': {'depends': [], 'domain': "[('country_id', '=?', country_id)]", 'readonly': False, 'relation': 'res.country.state', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'State', 'type': 'many2one'}, 'status_code': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Status Code', 'type': 'char'}, 'street': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Street', 'type': 'char'}, 'street2': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Street2', 'type': 'char'}, 'supervisor_ids': {'depends': ['supervisor_ids.type_contact'], 'domain': [['type_contact', '=', 'supervisor']], 'readonly': False, 'relation': 'res.partner', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Supervisors', 'type': 'one2many'}, 'supplier': {'depends': ['supplier_rank'], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Is a Vendor', 'type': 'boolean'}, 'supplier_invoice_count': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': '# Vendor Bills', 'type': 'integer'}, 'supplier_rank': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Supplier Rank', 'type': 'integer'}, 'task_count': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': '# Tasks', 'type': 'integer'}, 'task_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'project.task', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Tasks', 'type': 'one2many'}, 'title': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'res.partner.title', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Title', 'type': 'many2one'}, 'total_invoiced': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Total Invoiced', 'type': 'monetary'}, 'trading_name': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Trade License', 'type': 'char'}, 'transport_note': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Carrier Notes', 'type': 'char'}, 'trust': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Degree of trust you have in this debtor', 'type': 'selection'}, 'type': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Address Type', 'type': 'selection'}, 'type_abn': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Type', 'type': 'selection'}, 'type_contact': {'depends': ['type'], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Type of Contact', 'type': 'selection'}, 'tz': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Timezone', 'type': 'selection'}, 'tz_offset': {'depends': ['tz'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Timezone offset', 'type': 'char'}, 'use_partner_credit_limit': {'depends': [], 'readonly': False, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Partner Limit', 'type': 'boolean'}, 'user_id': {'depends': ['parent_id'], 'domain': [], 'readonly': False, 'relation': 'res.users', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Salesperson', 'type': 'many2one'}, 'user_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'res.users', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Users', 'type': 'one2many'}, 'user_livechat_username': {'depends': ['user_ids.livechat_username'], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'User Livechat Username', 'type': 'char'}, 'vat': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'ABN', 'type': 'char'}, 'vat_label': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Tax ID Label', 'type': 'char'}, 'view_child_in_list': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'View Child Address in List', 'type': 'boolean'}, 'visitor_ids': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'website.visitor', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Visitors', 'type': 'one2many'}, 'website': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Website Link', 'type': 'char'}, 'website_id': {'depends': [], 'domain': [], 'readonly': False, 'relation': 'website', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Website', 'type': 'many2one'}, 'website_message_ids': {'depends': [], 'domain': [['model', '=', 'res.partner'], ['message_type', 'in', ['comment', 'email', 'email_outgoing']]], 'readonly': False, 'relation': 'mail.message', 'required': False, 'searchable': True, 'sortable': False, 'store': True, 'string': 'Website Messages', 'type': 'one2many'}, 'website_published': {'depends': ['is_published', 'website_id'], 'readonly': False, 'required': False, 'searchable': True, 'sortable': False, 'store': False, 'string': 'Visible on current website', 'type': 'boolean'}, 'website_url': {'depends': [], 'readonly': True, 'required': False, 'searchable': False, 'sortable': False, 'store': False, 'string': 'Website URL', 'type': 'char'}, 'write_date': {'depends': [], 'readonly': True, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Last Updated on', 'type': 'datetime'}, 'write_uid': {'depends': [], 'domain': [], 'readonly': True, 'relation': 'res.users', 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Last Updated by', 'type': 'many2one'}, 'x_stairbiz_id': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Stairbiz ID', 'type': 'char'}, 'x_stairbiz_migrated': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Migrated from Stairbiz', 'type': 'boolean'}, 'x_stairbiz_migration_date': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Stairbiz Migration Date', 'type': 'datetime'}, 'zip': {'depends': [], 'readonly': False, 'required': False, 'searchable': True, 'sortable': True, 'store': True, 'string': 'Zip', 'type': 'char'}}
2025-05-05 10:39:49,439 - WARNING - No fields retrieved for res.partner after all attempts
2025-05-05 10:39:49,439 - DEBUG - Fields for res.partner: []
2025-05-05 10:39:49,439 - INFO - Fetching fields for sale.order
2025-05-05 10:39:49,439 - DEBUG - Fetching fields for model sale.order using access endpoint: https://stairmaster18.odoo-sandbox.com/api/v2/access/fields/sale.order?operation=read
2025-05-05 10:39:50,083 - DEBUG - HTTP status code for sale.order: 200
2025-05-05 10:39:50,083 - DEBUG - Response headers for sale.order: {'Server': 'openresty', 'Date': 'Mon, 05 May 2025 03:39:49 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '15041', 'Connection': 'keep-alive', 'X-Content-Type-Options': 'nosniff, nosniff', 'Content-Security-Policy': 'upgrade-insecure-requests;', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload', 'X-Frame-Options': 'SAMEORIGIN', 'Referrer-Policy': 'strict-origin', 'Permissions-Policy': 'geolocation=(), midi=(), microphone=*, camera=*, payment=()', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Methods': 'GET, POST, OPTIONS, PUT, DELETE', 'Access-Control-Allow-Headers': 'Origin, Content-Type, Accept, Authorization, X-CSRF-Token, Set-Cookie', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Set-Cookie'}
2025-05-05 10:39:50,083 - DEBUG - Raw API response for sale.order: {
    "access_token": "sale.order.access_token",
    "access_url": "sale.order.access_url",
    "access_warning": "sale.order.access_warning",
    "activity_calendar_event_id": "sale.order.activity_calendar_event_id",
    "activity_date_deadline": "sale.order.activity_date_deadline",
    "activity_exception_decoration": "sale.order.activity_exception_decoration",
    "activity_exception_icon": "sale.order.activity_exception_icon",
    "activity_ids": "sale.order.activity_ids",
    "activity_state": "sale.order.activity_state",
    "activity_summary": "sale.order.activity_summary",
    "activity_type_icon": "sale.order.activity_type_icon",
    "activity_type_id": "sale.order.activity_type_id",
    "activity_user_id": "sale.order.activity_user_id",
    "address_format": "sale.order.address_format",
    "address_html": "sale.order.address_html",
    "amount_invoiced": "sale.order.amount_invoiced",
    "amount_paid": "sale.order.amount_paid",
    "amount_tax": "sale.order.amount_tax",
    "amount_to_invoice": "sale.order.amount_to_invoice",
    "amount_total": "sale.order.amount_total",
    "amount_undiscounted": "sale.order.amount_undiscounted",
    "amount_untaxed": "sale.order.amount_untaxed",
    "are_walls_sheeted_html": "sale.order.are_walls_sheeted_html",
    "attachment_copy_ids": "sale.order.attachment_copy_ids",
    "attachment_count": "sale.order.attachment_count",
    "attachment_ids": "sale.order.attachment_ids",
    "attachment_po_ids": "sale.order.attachment_po_ids",
    "authorized_transaction_ids": "sale.order.authorized_transaction_ids",
    "balustrade": "sale.order.balustrade",
    "beam_height_html": "sale.order.beam_height_html",
    "bracket_colour": "sale.order.bracket_colour",
    "callups_request_date": "sale.order.callups_request_date",
    "campaign_id": "sale.order.campaign_id",
    "carrier": "sale.order.carrier",
    "carrier_id": "sale.order.carrier_id",
    "category": "sale.order.category",
    "client_email": "sale.order.client_email",
    "client_fax": "sale.order.client_fax",
    "client_name": "sale.order.client_name",
    "client_note": "sale.order.client_note",
    "client_order_ref": "sale.order.client_order_ref",
    "client_phone": "sale.order.client_phone",
    "client_state": "sale.order.client_state",
    "client_state_id": "sale.order.client_state_id",
    "client_status": "sale.order.client_status",
    "client_street": "sale.order.client_street",
    "client_suburb": "sale.order.client_suburb",
    "client_tag": "sale.order.client_tag",
    "client_zip": "sale.order.client_zip",
    "closed_task_count": "sale.order.closed_task_count",
    "combined_document_ids": "sale.order.combined_document_ids",
    "commitment_date": "sale.order.commitment_date",
    "company_id": "sale.order.company_id",
    "company_number": "sale.order.company_number",
    "company_price_include": "sale.order.company_price_include",
    "completed_task_percentage": "sale.order.completed_task_percentage",
    "contact_name": "sale.order.contact_name",
    "country_code": "sale.order.country_code",
    "create_date": "sale.order.create_date",
    "create_uid": "sale.order.create_uid",
    "critical_note": "sale.order.critical_note",
    "currency_id": "sale.order.currency_id",
    "currency_rate": "sale.order.currency_rate",
    "cust_id": "sale.order.cust_id",
    "customer_job_number": "sale.order.customer_job_number",
    "date_order": "sale.order.date_order",
    "def_contact": "sale.order.def_contact",
    "delivery_count": "sale.order.delivery_count",
    "delivery_message": "sale.order.delivery_message",
    "delivery_set": "sale.order.delivery_set",
    "delivery_status": "sale.order.delivery_status",
    "designer_id": "sale.order.designer_id",
    "detail_handrail": "sale.order.detail_handrail",
    "discount": "sale.order.discount",
    "display_name": "sale.order.display_name",
    "duplicated_order_ids": "sale.order.duplicated_order_ids",
    "effective_date": "sale.order.effective_date",
    "expected_date": "sale.order.expected_date",
    "expense_count": "sale.order.expense_count",
    "expense_ids": "sale.order.expense_ids",
    "fiscal_position_id": "sale.order.fiscal_position_id",
    "flag": "sale.order.flag",
    "floor_to_ceiling_html": "sale.order.floor_to_ceiling_html",
    "floor_to_floor_html": "sale.order.floor_to_floor_html",
    "handrail_application": "sale.order.handrail_application",
    "handrail_colour": "sale.order.handrail_colour",
    "handrail_finish": "sale.order.handrail_finish",
    "handrail_style": "sale.order.handrail_style",
    "has_active_pricelist": "sale.order.has_active_pricelist",
    "has_archived_products": "sale.order.has_archived_products",
    "has_message": "sale.order.has_message",
    "house_type_id": "sale.order.house_type_id",
    "house_type_price_ids": "sale.order.house_type_price_ids",
    "house_type_specific_id": "sale.order.house_type_specific_id",
    "id": "sale.order.id",
    "image": "sale.order.image",
    "incoterm": "sale.order.incoterm",
    "incoterm_location": "sale.order.incoterm_location",
    "invoice_count": "sale.order.invoice_count",
    "invoice_ids": "sale.order.invoice_ids",
    "invoice_status": "sale.order.invoice_status",
    "is_address_modified": "sale.order.is_address_modified",
    "is_all_service": "sale.order.is_all_service",
    "is_callups": "sale.order.is_callups",
    "is_expired": "sale.order.is_expired",
    "is_house_design": "sale.order.is_house_design",
    "is_invisible": "sale.order.is_invisible",
    "is_measure_sheet": "sale.order.is_measure_sheet",
    "is_order": "sale.order.is_order",
    "is_po_invisible": "sale.order.is_po_invisible",
    "is_product_milestone": "sale.order.is_product_milestone",
    "is_quote": "sale.order.is_quote",
    "is_sent_quote_to_customer": "sale.order.is_sent_quote_to_customer",
    "is_site_specific": "sale.order.is_site_specific",
    "is_stairbiz": "sale.order.is_stairbiz",
    "is_stairbiz_quote_team": "sale.order.is_stairbiz_quote_team",
    "job_date": "sale.order.job_date",
    "job_name": "sale.order.job_name",
    "job_number": "sale.order.job_number",
    "job_number_html": "sale.order.job_number_html",
    "job_status": "sale.order.job_status",
    "journal_id": "sale.order.journal_id",
    "json_popover": "sale.order.json_popover",
    "locked": "sale.order.locked",
    "measure_sheet_doc_id": "sale.order.measure_sheet_doc_id",
    "measure_sheet_request_data": "sale.order.measure_sheet_request_data",
    "measure_sheet_request_id": "sale.order.measure_sheet_request_id",
    "measure_sheet_request_url": "sale.order.measure_sheet_request_url",
    "measure_sheet_template_exist": "sale.order.measure_sheet_template_exist",
    "measure_sheet_template_id": "sale.order.measure_sheet_template_id",
    "measure_sheet_template_id_data": "sale.order.measure_sheet_template_id_data",
    "measure_sheet_template_item_ids": "sale.order.measure_sheet_template_item_ids",
    "measure_staff_id": "sale.order.measure_staff_id",
    "medium_id": "sale.order.medium_id",
    "message_attachment_count": "sale.order.message_attachment_count",
    "message_follower_ids": "sale.order.message_follower_ids",
    "message_has_error": "sale.order.message_has_error",
    "message_has_error_counter": "sale.order.message_has_error_counter",
    "message_has_sms_error": "sale.order.message_has_sms_error",
    "message_ids": "sale.order.message_ids",
    "message_is_follower": "sale.order.message_is_follower",
    "message_needaction": "sale.order.message_needaction",
    "message_needaction_counter": "sale.order.message_needaction_counter",
    "message_partner_ids": "sale.order.message_partner_ids",
    "milestone_count": "sale.order.milestone_count",
    "mrp_production_count": "sale.order.mrp_production_count",
    "mrp_production_ids": "sale.order.mrp_production_ids",
    "my_activity_date_deadline": "sale.order.my_activity_date_deadline",
    "name": "sale.order.name",
    "new_lead": "sale.order.new_lead",
    "nib_wall_html": "sale.order.nib_wall_html",
    "note": "sale.order.note",
    "opportunity_id": "sale.order.opportunity_id",
    "option": "sale.order.option",
    "order_line": "sale.order.order_line",
    "origin": "sale.order.origin",
    "partner_child_ids": "sale.order.partner_child_ids",
    "partner_credit_warning": "sale.order.partner_credit_warning",
    "partner_id": "sale.order.partner_id",
    "partner_invoice_id": "sale.order.partner_invoice_id",
    "partner_shipping_id": "sale.order.partner_shipping_id",
    "payment_term_id": "sale.order.payment_term_id",
    "pending_email_template_id": "sale.order.pending_email_template_id",
    "photo": "sale.order.photo",
    "photo_purchase": "sale.order.photo_purchase",
    "picking_ids": "sale.order.picking_ids",
    "picking_policy": "sale.order.picking_policy",
    "pickup_location_data": "sale.order.pickup_location_data",
    "prepayment_percent": "sale.order.prepayment_percent",
    "pricelist_id": "sale.order.pricelist_id",
    "priority": "sale.order.priority",
    "procurement_group_id": "sale.order.procurement_group_id",
    "product_house_type_id": "sale.order.product_house_type_id",
    "project_account_id": "sale.order.project_account_id",
    "project_count": "sale.order.project_count",
    "project_id": "sale.order.project_id",
    "project_ids": "sale.order.project_ids",
    "purchase_order_count": "sale.order.purchase_order_count",
    "quote_received_from_stairbiz_stage": "sale.order.quote_received_from_stairbiz_stage",
    "quote_request_type_id": "sale.order.quote_request_type_id",
    "raking_wall_html": "sale.order.raking_wall_html",
    "range_option_series": "sale.order.range_option_series",
    "rating_ids": "sale.order.rating_ids",
    "recompute_delivery_price": "sale.order.recompute_delivery_price",
    "reference": "sale.order.reference",
    "require_payment": "sale.order.require_payment",
    "require_signature": "sale.order.require_signature",
    "requote_sale_order_id": "sale.order.requote_sale_order_id",
    "sale_order_option_ids": "sale.order.sale_order_option_ids",
    "sale_order_template_id": "sale.order.sale_order_template_id",
    "sale_type": "sale.order.sale_type",
    "sent_quote_to_customer_stage": "sale.order.sent_quote_to_customer_stage",
    "sh_contact_google_location": "sale.order.sh_contact_google_location",
    "sh_contact_place_text": "sale.order.sh_contact_place_text",
    "sh_contact_place_text_main_string": "sale.order.sh_contact_place_text_main_string",
    "shipping_weight": "sale.order.shipping_weight",
    "show_colour_selections": "sale.order.show_colour_selections",
    "show_create_project_button": "sale.order.show_create_project_button",
    "show_designer": "sale.order.show_designer",
    "show_document_type": "sale.order.show_document_type",
    "show_general_information": "sale.order.show_general_information",
    "show_hours_recorded_button": "sale.order.show_hours_recorded_button",
    "show_house_type": "sale.order.show_house_type",
    "show_job_detail": "sale.order.show_job_detail",
    "show_job_type": "sale.order.show_job_type",
    "show_json_popover": "sale.order.show_json_popover",
    "show_measure_sheet": "sale.order.show_measure_sheet",
    "show_order_lines": "sale.order.show_order_lines",
    "show_project_button": "sale.order.show_project_button",
    "show_project_detail": "sale.order.show_project_detail",
    "show_requote": "sale.order.show_requote",
    "show_site_address": "sale.order.show_site_address",
    "show_site_contact": "sale.order.show_site_contact",
    "show_stairbiz_detail": "sale.order.show_stairbiz_detail",
    "show_task_button": "sale.order.show_task_button",
    "show_update_fpos": "sale.order.show_update_fpos",
    "show_update_pricelist": "sale.order.show_update_pricelist",
    "signature": "sale.order.signature",
    "signed_by": "sale.order.signed_by",
    "signed_on": "sale.order.signed_on",
    "site_address_id": "sale.order.site_address_id",
    "site_contact": "sale.order.site_contact",
    "site_country": "sale.order.site_country",
    "site_mobile": "sale.order.site_mobile",
    "site_phone": "sale.order.site_phone",
    "site_state": "sale.order.site_state",
    "site_street": "sale.order.site_street",
    "site_suburb": "sale.order.site_suburb",
    "site_zip": "sale.order.site_zip",
    "sla_deadline": "sale.order.sla_deadline",
    "sla_failed": "sale.order.sla_failed",
    "sla_remaining_hours": "sale.order.sla_remaining_hours",
    "sla_remaining_percentage": "sale.order.sla_remaining_percentage",
    "sla_status": "sale.order.sla_status",
    "sla_status_ids": "sale.order.sla_status_ids",
    "sla_success": "sale.order.sla_success",
    "source_id": "sale.order.source_id",
    "stage_id": "sale.order.stage_id",
    "stair_balustrade_html": "sale.order.stair_balustrade_html",
    "stair_calculator_id": "sale.order.stair_calculator_id",
    "stairbiz_ids": "sale.order.stairbiz_ids",
    "stairbiz_job_generated": "sale.order.stairbiz_job_generated",
    "stairbiz_quote_count": "sale.order.stairbiz_quote_count",
    "stairbiz_quotes_job_ids": "sale.order.stairbiz_quotes_job_ids",
    "state": "sale.order.state",
    "supervisor_contact_id": "sale.order.supervisor_contact_id",
    "tag_ids": "sale.order.tag_ids",
    "tasks_count": "sale.order.tasks_count",
    "tasks_ids": "sale.order.tasks_ids",
    "tax_calculation_rounding_method": "sale.order.tax_calculation_rounding_method",
    "tax_country_id": "sale.order.tax_country_id",
    "tax_totals": "sale.order.tax_totals",
    "team_id": "sale.order.team_id",
    "terms_type": "sale.order.terms_type",
    "timber_newel_post": "sale.order.timber_newel_post",
    "timber_newel_post_toppers": "sale.order.timber_newel_post_toppers",
    "timesheet_count": "sale.order.timesheet_count",
    "timesheet_encode_uom_id": "sale.order.timesheet_encode_uom_id",
    "timesheet_total_duration": "sale.order.timesheet_total_duration",
    "transaction_ids": "sale.order.transaction_ids",
    "transport_note": "sale.order.transport_note",
    "type_name": "sale.order.type_name",
    "user_id": "sale.order.user_id",
    "validity_date": "sale.order.validity_date",
    "vat": "sale.order.vat",
    "visible_project": "sale.order.visible_project",
    "warehouse_id": "sale.order.warehouse_id",
    "website_message_ids": "sale.order.website_message_ids",
    "write_date": "sale.order.write_date",
    "write_uid": "sale.order.write_uid",
    "x_sa_city": "sale.order.x_sa_city",
    "x_sa_country_id": "sale.order.x_sa_country_id",
    "x_sa_state_id": "sale.order.x_sa_state_id",
    "x_sa_street": "sale.order.x_sa_street",
    "x_sa_street2": "sale.order.x_sa_street2",
    "x_sa_zip": "sale.order.x_sa_zip",
    "x_site_contact_name": "sale.order.x_site_contact_name",
    "x_site_contact_phone": "sale.order.x_site_contact_phone",
    "x_stairbiz_id": "sale.order.x_stairbiz_id",
    "x_stairbiz_migrated": "sale.order.x_stairbiz_migrated",
    "x_stairbiz_migration_date": "sale.order.x_stairbiz_migration_date"
}
2025-05-05 10:39:50,084 - DEBUG - Parsed API response for sale.order: {
  "access_token": "sale.order.access_token",
  "access_url": "sale.order.access_url",
  "access_warning": "sale.order.access_warning",
  "activity_calendar_event_id": "sale.order.activity_calendar_event_id",
  "activity_date_deadline": "sale.order.activity_date_deadline",
  "activity_exception_decoration": "sale.order.activity_exception_decoration",
  "activity_exception_icon": "sale.order.activity_exception_icon",
  "activity_ids": "sale.order.activity_ids",
  "activity_state": "sale.order.activity_state",
  "activity_summary": "sale.order.activity_summary",
  "activity_type_icon": "sale.order.activity_type_icon",
  "activity_type_id": "sale.order.activity_type_id",
  "activity_user_id": "sale.order.activity_user_id",
  "address_format": "sale.order.address_format",
  "address_html": "sale.order.address_html",
  "amount_invoiced": "sale.order.amount_invoiced",
  "amount_paid": "sale.order.amount_paid",
  "amount_tax": "sale.order.amount_tax",
  "amount_to_invoice": "sale.order.amount_to_invoice",
  "amount_total": "sale.order.amount_total",
  "amount_undiscounted": "sale.order.amount_undiscounted",
  "amount_untaxed": "sale.order.amount_untaxed",
  "are_walls_sheeted_html": "sale.order.are_walls_sheeted_html",
  "attachment_copy_ids": "sale.order.attachment_copy_ids",
  "attachment_count": "sale.order.attachment_count",
  "attachment_ids": "sale.order.attachment_ids",
  "attachment_po_ids": "sale.order.attachment_po_ids",
  "authorized_transaction_ids": "sale.order.authorized_transaction_ids",
  "balustrade": "sale.order.balustrade",
  "beam_height_html": "sale.order.beam_height_html",
  "bracket_colour": "sale.order.bracket_colour",
  "callups_request_date": "sale.order.callups_request_date",
  "campaign_id": "sale.order.campaign_id",
  "carrier": "sale.order.carrier",
  "carrier_id": "sale.order.carrier_id",
  "category": "sale.order.category",
  "client_email": "sale.order.client_email",
  "client_fax": "sale.order.client_fax",
  "client_name": "sale.order.client_name",
  "client_note": "sale.order.client_note",
  "client_order_ref": "sale.order.client_order_ref",
  "client_phone": "sale.order.client_phone",
  "client_state": "sale.order.client_state",
  "client_state_id": "sale.order.client_state_id",
  "client_status": "sale.order.client_status",
  "client_street": "sale.order.client_street",
  "client_suburb": "sale.order.client_suburb",
  "client_tag": "sale.order.client_tag",
  "client_zip": "sale.order.client_zip",
  "closed_task_count": "sale.order.closed_task_count",
  "combined_document_ids": "sale.order.combined_document_ids",
  "commitment_date": "sale.order.commitment_date",
  "company_id": "sale.order.company_id",
  "company_number": "sale.order.company_number",
  "company_price_include": "sale.order.company_price_include",
  "completed_task_percentage": "sale.order.completed_task_percentage",
  "contact_name": "sale.order.contact_name",
  "country_code": "sale.order.country_code",
  "create_date": "sale.order.create_date",
  "create_uid": "sale.order.create_uid",
  "critical_note": "sale.order.critical_note",
  "currency_id": "sale.order.currency_id",
  "currency_rate": "sale.order.currency_rate",
  "cust_id": "sale.order.cust_id",
  "customer_job_number": "sale.order.customer_job_number",
  "date_order": "sale.order.date_order",
  "def_contact": "sale.order.def_contact",
  "delivery_count": "sale.order.delivery_count",
  "delivery_message": "sale.order.delivery_message",
  "delivery_set": "sale.order.delivery_set",
  "delivery_status": "sale.order.delivery_status",
  "designer_id": "sale.order.designer_id",
  "detail_handrail": "sale.order.detail_handrail",
  "discount": "sale.order.discount",
  "display_name": "sale.order.display_name",
  "duplicated_order_ids": "sale.order.duplicated_order_ids",
  "effective_date": "sale.order.effective_date",
  "expected_date": "sale.order.expected_date",
  "expense_count": "sale.order.expense_count",
  "expense_ids": "sale.order.expense_ids",
  "fiscal_position_id": "sale.order.fiscal_position_id",
  "flag": "sale.order.flag",
  "floor_to_ceiling_html": "sale.order.floor_to_ceiling_html",
  "floor_to_floor_html": "sale.order.floor_to_floor_html",
  "handrail_application": "sale.order.handrail_application",
  "handrail_colour": "sale.order.handrail_colour",
  "handrail_finish": "sale.order.handrail_finish",
  "handrail_style": "sale.order.handrail_style",
  "has_active_pricelist": "sale.order.has_active_pricelist",
  "has_archived_products": "sale.order.has_archived_products",
  "has_message": "sale.order.has_message",
  "house_type_id": "sale.order.house_type_id",
  "house_type_price_ids": "sale.order.house_type_price_ids",
  "house_type_specific_id": "sale.order.house_type_specific_id",
  "id": "sale.order.id",
  "image": "sale.order.image",
  "incoterm": "sale.order.incoterm",
  "incoterm_location": "sale.order.incoterm_location",
  "invoice_count": "sale.order.invoice_count",
  "invoice_ids": "sale.order.invoice_ids",
  "invoice_status": "sale.order.invoice_status",
  "is_address_modified": "sale.order.is_address_modified",
  "is_all_service": "sale.order.is_all_service",
  "is_callups": "sale.order.is_callups",
  "is_expired": "sale.order.is_expired",
  "is_house_design": "sale.order.is_house_design",
  "is_invisible": "sale.order.is_invisible",
  "is_measure_sheet": "sale.order.is_measure_sheet",
  "is_order": "sale.order.is_order",
  "is_po_invisible": "sale.order.is_po_invisible",
  "is_product_milestone": "sale.order.is_product_milestone",
  "is_quote": "sale.order.is_quote",
  "is_sent_quote_to_customer": "sale.order.is_sent_quote_to_customer",
  "is_site_specific": "sale.order.is_site_specific",
  "is_stairbiz": "sale.order.is_stairbiz",
  "is_stairbiz_quote_team": "sale.order.is_stairbiz_quote_team",
  "job_date": "sale.order.job_date",
  "job_name": "sale.order.job_name",
  "job_number": "sale.order.job_number",
  "job_number_html": "sale.order.job_number_html",
  "job_status": "sale.order.job_status",
  "journal_id": "sale.order.journal_id",
  "json_popover": "sale.order.json_popover",
  "locked": "sale.order.locked",
  "measure_sheet_doc_id": "sale.order.measure_sheet_doc_id",
  "measure_sheet_request_data": "sale.order.measure_sheet_request_data",
  "measure_sheet_request_id": "sale.order.measure_sheet_request_id",
  "measure_sheet_request_url": "sale.order.measure_sheet_request_url",
  "measure_sheet_template_exist": "sale.order.measure_sheet_template_exist",
  "measure_sheet_template_id": "sale.order.measure_sheet_template_id",
  "measure_sheet_template_id_data": "sale.order.measure_sheet_template_id_data",
  "measure_sheet_template_item_ids": "sale.order.measure_sheet_template_item_ids",
  "measure_staff_id": "sale.order.measure_staff_id",
  "medium_id": "sale.order.medium_id",
  "message_attachment_count": "sale.order.message_attachment_count",
  "message_follower_ids": "sale.order.message_follower_ids",
  "message_has_error": "sale.order.message_has_error",
  "message_has_error_counter": "sale.order.message_has_error_counter",
  "message_has_sms_error": "sale.order.message_has_sms_error",
  "message_ids": "sale.order.message_ids",
  "message_is_follower": "sale.order.message_is_follower",
  "message_needaction": "sale.order.message_needaction",
  "message_needaction_counter": "sale.order.message_needaction_counter",
  "message_partner_ids": "sale.order.message_partner_ids",
  "milestone_count": "sale.order.milestone_count",
  "mrp_production_count": "sale.order.mrp_production_count",
  "mrp_production_ids": "sale.order.mrp_production_ids",
  "my_activity_date_deadline": "sale.order.my_activity_date_deadline",
  "name": "sale.order.name",
  "new_lead": "sale.order.new_lead",
  "nib_wall_html": "sale.order.nib_wall_html",
  "note": "sale.order.note",
  "opportunity_id": "sale.order.opportunity_id",
  "option": "sale.order.option",
  "order_line": "sale.order.order_line",
  "origin": "sale.order.origin",
  "partner_child_ids": "sale.order.partner_child_ids",
  "partner_credit_warning": "sale.order.partner_credit_warning",
  "partner_id": "sale.order.partner_id",
  "partner_invoice_id": "sale.order.partner_invoice_id",
  "partner_shipping_id": "sale.order.partner_shipping_id",
  "payment_term_id": "sale.order.payment_term_id",
  "pending_email_template_id": "sale.order.pending_email_template_id",
  "photo": "sale.order.photo",
  "photo_purchase": "sale.order.photo_purchase",
  "picking_ids": "sale.order.picking_ids",
  "picking_policy": "sale.order.picking_policy",
  "pickup_location_data": "sale.order.pickup_location_data",
  "prepayment_percent": "sale.order.prepayment_percent",
  "pricelist_id": "sale.order.pricelist_id",
  "priority": "sale.order.priority",
  "procurement_group_id": "sale.order.procurement_group_id",
  "product_house_type_id": "sale.order.product_house_type_id",
  "project_account_id": "sale.order.project_account_id",
  "project_count": "sale.order.project_count",
  "project_id": "sale.order.project_id",
  "project_ids": "sale.order.project_ids",
  "purchase_order_count": "sale.order.purchase_order_count",
  "quote_received_from_stairbiz_stage": "sale.order.quote_received_from_stairbiz_stage",
  "quote_request_type_id": "sale.order.quote_request_type_id",
  "raking_wall_html": "sale.order.raking_wall_html",
  "range_option_series": "sale.order.range_option_series",
  "rating_ids": "sale.order.rating_ids",
  "recompute_delivery_price": "sale.order.recompute_delivery_price",
  "reference": "sale.order.reference",
  "require_payment": "sale.order.require_payment",
  "require_signature": "sale.order.require_signature",
  "requote_sale_order_id": "sale.order.requote_sale_order_id",
  "sale_order_option_ids": "sale.order.sale_order_option_ids",
  "sale_order_template_id": "sale.order.sale_order_template_id",
  "sale_type": "sale.order.sale_type",
  "sent_quote_to_customer_stage": "sale.order.sent_quote_to_customer_stage",
  "sh_contact_google_location": "sale.order.sh_contact_google_location",
  "sh_contact_place_text": "sale.order.sh_contact_place_text",
  "sh_contact_place_text_main_string": "sale.order.sh_contact_place_text_main_string",
  "shipping_weight": "sale.order.shipping_weight",
  "show_colour_selections": "sale.order.show_colour_selections",
  "show_create_project_button": "sale.order.show_create_project_button",
  "show_designer": "sale.order.show_designer",
  "show_document_type": "sale.order.show_document_type",
  "show_general_information": "sale.order.show_general_information",
  "show_hours_recorded_button": "sale.order.show_hours_recorded_button",
  "show_house_type": "sale.order.show_house_type",
  "show_job_detail": "sale.order.show_job_detail",
  "show_job_type": "sale.order.show_job_type",
  "show_json_popover": "sale.order.show_json_popover",
  "show_measure_sheet": "sale.order.show_measure_sheet",
  "show_order_lines": "sale.order.show_order_lines",
  "show_project_button": "sale.order.show_project_button",
  "show_project_detail": "sale.order.show_project_detail",
  "show_requote": "sale.order.show_requote",
  "show_site_address": "sale.order.show_site_address",
  "show_site_contact": "sale.order.show_site_contact",
  "show_stairbiz_detail": "sale.order.show_stairbiz_detail",
  "show_task_button": "sale.order.show_task_button",
  "show_update_fpos": "sale.order.show_update_fpos",
  "show_update_pricelist": "sale.order.show_update_pricelist",
  "signature": "sale.order.signature",
  "signed_by": "sale.order.signed_by",
  "signed_on": "sale.order.signed_on",
  "site_address_id": "sale.order.site_address_id",
  "site_contact": "sale.order.site_contact",
  "site_country": "sale.order.site_country",
  "site_mobile": "sale.order.site_mobile",
  "site_phone": "sale.order.site_phone",
  "site_state": "sale.order.site_state",
  "site_street": "sale.order.site_street",
  "site_suburb": "sale.order.site_suburb",
  "site_zip": "sale.order.site_zip",
  "sla_deadline": "sale.order.sla_deadline",
  "sla_failed": "sale.order.sla_failed",
  "sla_remaining_hours": "sale.order.sla_remaining_hours",
  "sla_remaining_percentage": "sale.order.sla_remaining_percentage",
  "sla_status": "sale.order.sla_status",
  "sla_status_ids": "sale.order.sla_status_ids",
  "sla_success": "sale.order.sla_success",
  "source_id": "sale.order.source_id",
  "stage_id": "sale.order.stage_id",
  "stair_balustrade_html": "sale.order.stair_balustrade_html",
  "stair_calculator_id": "sale.order.stair_calculator_id",
  "stairbiz_ids": "sale.order.stairbiz_ids",
  "stairbiz_job_generated": "sale.order.stairbiz_job_generated",
  "stairbiz_quote_count": "sale.order.stairbiz_quote_count",
  "stairbiz_quotes_job_ids": "sale.order.stairbiz_quotes_job_ids",
  "state": "sale.order.state",
  "supervisor_contact_id": "sale.order.supervisor_contact_id",
  "tag_ids": "sale.order.tag_ids",
  "tasks_count": "sale.order.tasks_count",
  "tasks_ids": "sale.order.tasks_ids",
  "tax_calculation_rounding_method": "sale.order.tax_calculation_rounding_method",
  "tax_country_id": "sale.order.tax_country_id",
  "tax_totals": "sale.order.tax_totals",
  "team_id": "sale.order.team_id",
  "terms_type": "sale.order.terms_type",
  "timber_newel_post": "sale.order.timber_newel_post",
  "timber_newel_post_toppers": "sale.order.timber_newel_post_toppers",
  "timesheet_count": "sale.order.timesheet_count",
  "timesheet_encode_uom_id": "sale.order.timesheet_encode_uom_id",
  "timesheet_total_duration": "sale.order.timesheet_total_duration",
  "transaction_ids": "sale.order.transaction_ids",
  "transport_note": "sale.order.transport_note",
  "type_name": "sale.order.type_name",
  "user_id": "sale.order.user_id",
  "validity_date": "sale.order.validity_date",
  "vat": "sale.order.vat",
  "visible_project": "sale.order.visible_project",
  "warehouse_id": "sale.order.warehouse_id",
  "website_message_ids": "sale.order.website_message_ids",
  "write_date": "sale.order.write_date",
  "write_uid": "sale.order.write_uid",
  "x_sa_city": "sale.order.x_sa_city",
  "x_sa_country_id": "sale.order.x_sa_country_id",
  "x_sa_state_id": "sale.order.x_sa_state_id",
  "x_sa_street": "sale.order.x_sa_street",
  "x_sa_street2": "sale.order.x_sa_street2",
  "x_sa_zip": "sale.order.x_sa_zip",
  "x_site_contact_name": "sale.order.x_site_contact_name",
  "x_site_contact_phone": "sale.order.x_site_contact_phone",
  "x_stairbiz_id": "sale.order.x_stairbiz_id",
  "x_stairbiz_migrated": "sale.order.x_stairbiz_migrated",
  "x_stairbiz_migration_date": "sale.order.x_stairbiz_migration_date"
}
2025-05-05 10:39:50,084 - INFO - Successfully fetched 281 fields for sale.order
2025-05-05 10:39:50,086 - DEBUG - Fields for sale.order: [
  {
    "name": "access_token",
    "string": "Access Token",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "access_url",
    "string": "Access Url",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "access_warning",
    "string": "Access Warning",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "activity_calendar_event_id",
    "string": "Activity Calendar Event Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "activity_date_deadline",
    "string": "Activity Date Deadline",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "activity_exception_decoration",
    "string": "Activity Exception Decoration",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "activity_exception_icon",
    "string": "Activity Exception Icon",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "activity_ids",
    "string": "Activity Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "activity_state",
    "string": "Activity State",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "activity_summary",
    "string": "Activity Summary",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "activity_type_icon",
    "string": "Activity Type Icon",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "activity_type_id",
    "string": "Activity Type Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "activity_user_id",
    "string": "Activity User Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "address_format",
    "string": "Address Format",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "address_html",
    "string": "Address Html",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "amount_invoiced",
    "string": "Amount Invoiced",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "amount_paid",
    "string": "Amount Paid",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "amount_tax",
    "string": "Amount Tax",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "amount_to_invoice",
    "string": "Amount To Invoice",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "amount_total",
    "string": "Amount Total",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "amount_undiscounted",
    "string": "Amount Undiscounted",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "amount_untaxed",
    "string": "Amount Untaxed",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "are_walls_sheeted_html",
    "string": "Are Walls Sheeted Html",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "attachment_copy_ids",
    "string": "Attachment Copy Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "attachment_count",
    "string": "Attachment Count",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "attachment_ids",
    "string": "Attachment Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "attachment_po_ids",
    "string": "Attachment Po Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "authorized_transaction_ids",
    "string": "Authorized Transaction Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "balustrade",
    "string": "Balustrade",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "beam_height_html",
    "string": "Beam Height Html",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "bracket_colour",
    "string": "Bracket Colour",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "callups_request_date",
    "string": "Callups Request Date",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "campaign_id",
    "string": "Campaign Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "carrier",
    "string": "Carrier",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "carrier_id",
    "string": "Carrier Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "category",
    "string": "Category",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "client_email",
    "string": "Client Email",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "client_fax",
    "string": "Client Fax",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "client_name",
    "string": "Client Name",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "client_note",
    "string": "Client Note",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "client_order_ref",
    "string": "Client Order Ref",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "client_phone",
    "string": "Client Phone",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "client_state",
    "string": "Client State",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "client_state_id",
    "string": "Client State Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "client_status",
    "string": "Client Status",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "client_street",
    "string": "Client Street",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "client_suburb",
    "string": "Client Suburb",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "client_tag",
    "string": "Client Tag",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "client_zip",
    "string": "Client Zip",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "closed_task_count",
    "string": "Closed Task Count",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "combined_document_ids",
    "string": "Combined Document Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "commitment_date",
    "string": "Commitment Date",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "company_id",
    "string": "Company Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "company_number",
    "string": "Company Number",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "company_price_include",
    "string": "Company Price Include",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "completed_task_percentage",
    "string": "Completed Task Percentage",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "contact_name",
    "string": "Contact Name",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "country_code",
    "string": "Country Code",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "create_date",
    "string": "Create Date",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "create_uid",
    "string": "Create Uid",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "critical_note",
    "string": "Critical Note",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "currency_id",
    "string": "Currency Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "currency_rate",
    "string": "Currency Rate",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "cust_id",
    "string": "Cust Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "customer_job_number",
    "string": "Customer Job Number",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "date_order",
    "string": "Date Order",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "def_contact",
    "string": "Def Contact",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "delivery_count",
    "string": "Delivery Count",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "delivery_message",
    "string": "Delivery Message",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "delivery_set",
    "string": "Delivery Set",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "delivery_status",
    "string": "Delivery Status",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "designer_id",
    "string": "Designer Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "detail_handrail",
    "string": "Detail Handrail",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "discount",
    "string": "Discount",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "display_name",
    "string": "Display Name",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "duplicated_order_ids",
    "string": "Duplicated Order Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "effective_date",
    "string": "Effective Date",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "expected_date",
    "string": "Expected Date",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "expense_count",
    "string": "Expense Count",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "expense_ids",
    "string": "Expense Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "fiscal_position_id",
    "string": "Fiscal Position Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "flag",
    "string": "Flag",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "floor_to_ceiling_html",
    "string": "Floor To Ceiling Html",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "floor_to_floor_html",
    "string": "Floor To Floor Html",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "handrail_application",
    "string": "Handrail Application",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "handrail_colour",
    "string": "Handrail Colour",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "handrail_finish",
    "string": "Handrail Finish",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "handrail_style",
    "string": "Handrail Style",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "has_active_pricelist",
    "string": "Has Active Pricelist",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "has_archived_products",
    "string": "Has Archived Products",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "has_message",
    "string": "Has Message",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "house_type_id",
    "string": "House Type Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "house_type_price_ids",
    "string": "House Type Price Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "house_type_specific_id",
    "string": "House Type Specific Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "id",
    "string": "Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "image",
    "string": "Image",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "incoterm",
    "string": "Incoterm",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "incoterm_location",
    "string": "Incoterm Location",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "invoice_count",
    "string": "Invoice Count",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "invoice_ids",
    "string": "Invoice Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "invoice_status",
    "string": "Invoice Status",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_address_modified",
    "string": "Is Address Modified",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_all_service",
    "string": "Is All Service",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_callups",
    "string": "Is Callups",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_expired",
    "string": "Is Expired",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_house_design",
    "string": "Is House Design",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_invisible",
    "string": "Is Invisible",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_measure_sheet",
    "string": "Is Measure Sheet",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_order",
    "string": "Is Order",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_po_invisible",
    "string": "Is Po Invisible",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_product_milestone",
    "string": "Is Product Milestone",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_quote",
    "string": "Is Quote",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_sent_quote_to_customer",
    "string": "Is Sent Quote To Customer",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_site_specific",
    "string": "Is Site Specific",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_stairbiz",
    "string": "Is Stairbiz",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "is_stairbiz_quote_team",
    "string": "Is Stairbiz Quote Team",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "job_date",
    "string": "Job Date",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "job_name",
    "string": "Job Name",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "job_number",
    "string": "Job Number",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "job_number_html",
    "string": "Job Number Html",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "job_status",
    "string": "Job Status",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "journal_id",
    "string": "Journal Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "json_popover",
    "string": "Json Popover",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "locked",
    "string": "Locked",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "measure_sheet_doc_id",
    "string": "Measure Sheet Doc Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "measure_sheet_request_data",
    "string": "Measure Sheet Request Data",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "measure_sheet_request_id",
    "string": "Measure Sheet Request Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "measure_sheet_request_url",
    "string": "Measure Sheet Request Url",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "measure_sheet_template_exist",
    "string": "Measure Sheet Template Exist",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "measure_sheet_template_id",
    "string": "Measure Sheet Template Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "measure_sheet_template_id_data",
    "string": "Measure Sheet Template Id Data",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "measure_sheet_template_item_ids",
    "string": "Measure Sheet Template Item Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "measure_staff_id",
    "string": "Measure Staff Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "medium_id",
    "string": "Medium Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "message_attachment_count",
    "string": "Message Attachment Count",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "message_follower_ids",
    "string": "Message Follower Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "message_has_error",
    "string": "Message Has Error",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "message_has_error_counter",
    "string": "Message Has Error Counter",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "message_has_sms_error",
    "string": "Message Has Sms Error",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "message_ids",
    "string": "Message Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "message_is_follower",
    "string": "Message Is Follower",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "message_needaction",
    "string": "Message Needaction",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "message_needaction_counter",
    "string": "Message Needaction Counter",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "message_partner_ids",
    "string": "Message Partner Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "milestone_count",
    "string": "Milestone Count",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "mrp_production_count",
    "string": "Mrp Production Count",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "mrp_production_ids",
    "string": "Mrp Production Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "my_activity_date_deadline",
    "string": "My Activity Date Deadline",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "name",
    "string": "Order Reference",
    "type": "char",
    "required": true,
    "readonly": false,
    "store": true,
    "searchable": true,
    "sortable": true
  },
  {
    "name": "new_lead",
    "string": "New Lead",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "nib_wall_html",
    "string": "Nib Wall Html",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "note",
    "string": "Note",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "opportunity_id",
    "string": "Opportunity Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "option",
    "string": "Option",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "order_line",
    "string": "Order Line",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "origin",
    "string": "Origin",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "partner_child_ids",
    "string": "Partner Child Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "partner_credit_warning",
    "string": "Partner Credit Warning",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "partner_id",
    "string": "Customer",
    "type": "many2one",
    "relation": "res.partner",
    "required": true,
    "readonly": false,
    "store": true,
    "searchable": true,
    "sortable": true
  },
  {
    "name": "partner_invoice_id",
    "string": "Partner Invoice Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "partner_shipping_id",
    "string": "Partner Shipping Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "payment_term_id",
    "string": "Payment Term Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "pending_email_template_id",
    "string": "Pending Email Template Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "photo",
    "string": "Photo",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "photo_purchase",
    "string": "Photo Purchase",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "picking_ids",
    "string": "Picking Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "picking_policy",
    "string": "Picking Policy",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "pickup_location_data",
    "string": "Pickup Location Data",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "prepayment_percent",
    "string": "Prepayment Percent",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "pricelist_id",
    "string": "Pricelist Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "priority",
    "string": "Priority",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "procurement_group_id",
    "string": "Procurement Group Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "product_house_type_id",
    "string": "Product House Type Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "project_account_id",
    "string": "Project Account Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "project_count",
    "string": "Project Count",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "project_id",
    "string": "Project Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "project_ids",
    "string": "Project Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "purchase_order_count",
    "string": "Purchase Order Count",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "quote_received_from_stairbiz_stage",
    "string": "Quote Received From Stairbiz Stage",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "quote_request_type_id",
    "string": "Quote Request Type Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "raking_wall_html",
    "string": "Raking Wall Html",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "range_option_series",
    "string": "Range Option Series",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "rating_ids",
    "string": "Rating Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "recompute_delivery_price",
    "string": "Recompute Delivery Price",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "reference",
    "string": "Reference",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "require_payment",
    "string": "Require Payment",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "require_signature",
    "string": "Require Signature",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "requote_sale_order_id",
    "string": "Requote Sale Order Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "sale_order_option_ids",
    "string": "Sale Order Option Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "sale_order_template_id",
    "string": "Sale Order Template Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "sale_type",
    "string": "Sale Type",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "sent_quote_to_customer_stage",
    "string": "Sent Quote To Customer Stage",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "sh_contact_google_location",
    "string": "Sh Contact Google Location",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "sh_contact_place_text",
    "string": "Sh Contact Place Text",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "sh_contact_place_text_main_string",
    "string": "Sh Contact Place Text Main String",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "shipping_weight",
    "string": "Shipping Weight",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_colour_selections",
    "string": "Show Colour Selections",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_create_project_button",
    "string": "Show Create Project Button",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_designer",
    "string": "Show Designer",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_document_type",
    "string": "Show Document Type",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_general_information",
    "string": "Show General Information",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_hours_recorded_button",
    "string": "Show Hours Recorded Button",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_house_type",
    "string": "Show House Type",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_job_detail",
    "string": "Show Job Detail",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_job_type",
    "string": "Show Job Type",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_json_popover",
    "string": "Show Json Popover",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_measure_sheet",
    "string": "Show Measure Sheet",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_order_lines",
    "string": "Show Order Lines",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_project_button",
    "string": "Show Project Button",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_project_detail",
    "string": "Show Project Detail",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_requote",
    "string": "Show Requote",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_site_address",
    "string": "Show Site Address",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_site_contact",
    "string": "Show Site Contact",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_stairbiz_detail",
    "string": "Show Stairbiz Detail",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_task_button",
    "string": "Show Task Button",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_update_fpos",
    "string": "Show Update Fpos",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "show_update_pricelist",
    "string": "Show Update Pricelist",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "signature",
    "string": "Signature",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "signed_by",
    "string": "Signed By",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "signed_on",
    "string": "Signed On",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "site_address_id",
    "string": "Site Address Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "site_contact",
    "string": "Site Contact",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "site_country",
    "string": "Site Country",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "site_mobile",
    "string": "Site Mobile",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "site_phone",
    "string": "Site Phone",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "site_state",
    "string": "Site State",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "site_street",
    "string": "Site Street",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "site_suburb",
    "string": "Site Suburb",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "site_zip",
    "string": "Site Zip",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "sla_deadline",
    "string": "Sla Deadline",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "sla_failed",
    "string": "Sla Failed",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "sla_remaining_hours",
    "string": "Sla Remaining Hours",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "sla_remaining_percentage",
    "string": "Sla Remaining Percentage",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "sla_status",
    "string": "Sla Status",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "sla_status_ids",
    "string": "Sla Status Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "sla_success",
    "string": "Sla Success",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "source_id",
    "string": "Source Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "stage_id",
    "string": "Stage Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "stair_balustrade_html",
    "string": "Stair Balustrade Html",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "stair_calculator_id",
    "string": "Stair Calculator Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "stairbiz_ids",
    "string": "Stairbiz Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "stairbiz_job_generated",
    "string": "Stairbiz Job Generated",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "stairbiz_quote_count",
    "string": "Stairbiz Quote Count",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "stairbiz_quotes_job_ids",
    "string": "Stairbiz Quotes Job Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "state",
    "string": "Status",
    "type": "selection",
    "required": false,
    "readonly": true,
    "store": true,
    "searchable": true,
    "sortable": true
  },
  {
    "name": "supervisor_contact_id",
    "string": "Supervisor Contact Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "tag_ids",
    "string": "Tag Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "tasks_count",
    "string": "Tasks Count",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "tasks_ids",
    "string": "Tasks Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "tax_calculation_rounding_method",
    "string": "Tax Calculation Rounding Method",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "tax_country_id",
    "string": "Tax Country Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "tax_totals",
    "string": "Tax Totals",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "team_id",
    "string": "Team Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "terms_type",
    "string": "Terms Type",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "timber_newel_post",
    "string": "Timber Newel Post",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "timber_newel_post_toppers",
    "string": "Timber Newel Post Toppers",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "timesheet_count",
    "string": "Timesheet Count",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "timesheet_encode_uom_id",
    "string": "Timesheet Encode Uom Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "timesheet_total_duration",
    "string": "Timesheet Total Duration",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "transaction_ids",
    "string": "Transaction Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "transport_note",
    "string": "Transport Note",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "type_name",
    "string": "Type Name",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "user_id",
    "string": "User Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "validity_date",
    "string": "Validity Date",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "vat",
    "string": "Vat",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "visible_project",
    "string": "Visible Project",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "warehouse_id",
    "string": "Warehouse Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "website_message_ids",
    "string": "Website Message Ids",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "write_date",
    "string": "Write Date",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "write_uid",
    "string": "Write Uid",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "x_sa_city",
    "string": "X Sa City",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "x_sa_country_id",
    "string": "X Sa Country Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "x_sa_state_id",
    "string": "X Sa State Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "x_sa_street",
    "string": "X Sa Street",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "x_sa_street2",
    "string": "X Sa Street2",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "x_sa_zip",
    "string": "X Sa Zip",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "x_site_contact_name",
    "string": "X Site Contact Name",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "x_site_contact_phone",
    "string": "X Site Contact Phone",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "x_stairbiz_id",
    "string": "X Stairbiz Id",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "x_stairbiz_migrated",
    "string": "X Stairbiz Migrated",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  },
  {
    "name": "x_stairbiz_migration_date",
    "string": "X Stairbiz Migration Date",
    "type": "unknown",
    "required": false,
    "readonly": false,
    "store": true,
    "searchable": false,
    "sortable": false
  }
]
2025-05-05 10:39:50,086 - INFO - Testing relationship Get sale.order name
2025-05-05 10:39:50,086 - DEBUG - Searching for record in sale.order
2025-05-05 10:39:50,086 - DEBUG - Testing GET endpoint /api/v2/search/sale.order?limit=1
2025-05-05 10:39:50,635 - DEBUG - Endpoint /api/v2/search/sale.order?limit=1 succeeded with response: [
  3895
]
2025-05-05 10:39:50,635 - DEBUG - Found record ID 3895 for sale.order
2025-05-05 10:39:50,635 - DEBUG - Testing search_read for sale.order.name
2025-05-05 10:39:50,635 - DEBUG - Testing POST endpoint /api/v2/call with payload: {
  "model": "sale.order",
  "method": "search_read",
  "args": [
    [],
    [
      "name"
    ]
  ],
  "kwargs": {
    "limit": 1
  }
}
2025-05-05 10:39:51,164 - DEBUG - Endpoint /api/v2/call succeeded with response: [
  {
    "id": 3895,
    "name": "Fwd: 19 Daintree Drive measure GJ Gardner"
  }
]
2025-05-05 10:39:51,164 - INFO - Successfully tested sale.order.name via search_read
2025-05-05 10:39:51,164 - INFO - Testing relationship Get sale.order state
2025-05-05 10:39:51,164 - DEBUG - Searching for record in sale.order
2025-05-05 10:39:51,164 - DEBUG - Testing GET endpoint /api/v2/search/sale.order?limit=1
2025-05-05 10:39:51,719 - DEBUG - Endpoint /api/v2/search/sale.order?limit=1 succeeded with response: [
  3895
]
2025-05-05 10:39:51,719 - DEBUG - Found record ID 3895 for sale.order
2025-05-05 10:39:51,719 - DEBUG - Testing search_read for sale.order.state
2025-05-05 10:39:51,719 - DEBUG - Testing POST endpoint /api/v2/call with payload: {
  "model": "sale.order",
  "method": "search_read",
  "args": [
    [],
    [
      "state"
    ]
  ],
  "kwargs": {
    "limit": 1
  }
}
2025-05-05 10:39:52,249 - DEBUG - Endpoint /api/v2/call succeeded with response: [
  {
    "id": 3895,
    "state": "draft"
  }
]
2025-05-05 10:39:52,250 - INFO - Successfully tested sale.order.state via search_read
2025-05-05 10:39:52,250 - INFO - Testing relationship Get res.partner name
2025-05-05 10:39:52,250 - DEBUG - Searching for record in res.partner
2025-05-05 10:39:52,250 - DEBUG - Testing GET endpoint /api/v2/search/res.partner?limit=1
2025-05-05 10:39:52,790 - DEBUG - Endpoint /api/v2/search/res.partner?limit=1 succeeded with response: [
  8005
]
2025-05-05 10:39:52,790 - DEBUG - Found record ID 8005 for res.partner
2025-05-05 10:39:52,790 - DEBUG - Testing search_read for res.partner.name
2025-05-05 10:39:52,791 - DEBUG - Testing POST endpoint /api/v2/call with payload: {
  "model": "res.partner",
  "method": "search_read",
  "args": [
    [],
    [
      "name"
    ]
  ],
  "kwargs": {
    "limit": 1
  }
}
2025-05-05 10:39:53,333 - DEBUG - Endpoint /api/v2/call succeeded with response: [
  {
    "id": 8005,
    "name": "API Test Contact 1746408819"
  }
]
2025-05-05 10:39:53,333 - INFO - Successfully tested res.partner.name via search_read
2025-05-05 10:39:53,336 - INFO - Saved test summary to test_results/test_summary_20250505_103953.json
2025-05-05 10:39:53,339 - INFO - Saved schema to test_results/relationship_schema_20250505_103953.json
2025-05-05 10:39:53,339 - INFO - Saved log to test_results/log_20250505_103944.txt
2025-05-05 10:39:53,339 - INFO - Test execution completed.
