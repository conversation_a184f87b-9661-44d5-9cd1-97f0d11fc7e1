import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { NavigationContainer } from '@react-navigation/native';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

// Import screens
import HomeScreen from '../features/home/<USER>/HomeScreen';
import LoginScreen from '../features/auth/screens/LoginScreen';
import ProfileScreen from '../features/profile/screens/ProfileScreen';
import EditProfileScreen from '../features/profile/screens/EditProfileScreen';
import SettingsScreen from '../features/settings/screens/SettingsScreen';

// Import feature navigators
import ContactsNavigator from '../features/contacts/ContactsNavigator';
import HelpdeskNavigator from '../features/helpdesk/navigation/HelpdeskNavigator';
import ProductsNavigator from '../features/products/ProductsNavigator';

// Import discuss screens
import DiscussScreen from '../features/discuss/screens/DiscussScreen';
import DiscussChatScreen from '../features/discuss/screens/DiscussChatScreen';

// Import types
import { RootStackParamList } from './types';

// Import auth context
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';

// Create navigators
const Stack = createStackNavigator<RootStackParamList>();
const Drawer = createDrawerNavigator();

// Drawer content component
const DrawerContent = ({ navigation }) => {
  const { logout } = useAuth();
  const { colors } = useTheme();

  const menuItems = [
    {
      id: 'home',
      title: 'Dashboard',
      icon: 'view-dashboard',
      route: 'Home',
      description: 'Main dashboard'
    },
    {
      id: 'contacts',
      title: 'Contacts',
      icon: 'account-group',
      route: 'ContactsList',
      description: 'Manage contacts'
    },
    {
      id: 'products',
      title: 'Products',
      icon: 'package-variant',
      route: 'ProductsList',
      description: 'Product catalog'
    },
    {
      id: 'chat',
      title: 'Chat',
      icon: 'chat',
      route: 'Discuss',
      description: 'Team messaging'
    },
    {
      id: 'helpdesk',
      title: 'Helpdesk',
      icon: 'ticket-account',
      route: 'HelpdeskList',
      description: 'Support tickets'
    },
    {
      id: 'profile',
      title: 'Profile',
      icon: 'account-circle',
      route: 'Profile',
      description: 'User profile'
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: 'cog',
      route: 'Settings',
      description: 'App settings'
    }
  ];

  const renderMenuItem = (item) => (
    <TouchableOpacity
      key={item.id}
      style={[styles.menuItemContainer, { borderBottomColor: colors.border }]}
      onPress={() => navigation.navigate(item.route)}
    >
      <View style={styles.menuItemContent}>
        <Icon name={item.icon} size={22} color={colors.primary} style={styles.menuIcon} />
        <View style={styles.menuTextContainer}>
          <Text style={[styles.menuItemTitle, { color: colors.text }]}>{item.title}</Text>
          <Text style={[styles.menuItemDescription, { color: colors.textSecondary }]}>{item.description}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.drawerContainer, { backgroundColor: colors.surface }]}>
      <View style={[styles.drawerHeader, { backgroundColor: colors.primary }]}>
        <Text style={[styles.drawerTitle, { color: colors.onPrimary }]}>ExoMobile</Text>
        <Text style={[styles.drawerSubtitle, { color: colors.onPrimary }]}>v1.0.0</Text>
      </View>

      <View style={styles.drawerContent}>
        {menuItems.map(renderMenuItem)}
      </View>

      <View style={[styles.drawerFooter, { borderTopColor: colors.border }]}>
        <TouchableOpacity
          style={styles.logoutButtonContainer}
          onPress={() => logout()}
        >
          <Icon name="logout" size={20} color={colors.error} style={styles.logoutIcon} />
          <Text style={[styles.logoutButton, { color: colors.error }]}>
            Logout
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

// Main drawer navigator
const MainDrawerNavigator = () => {
  return (
    <Drawer.Navigator
      drawerContent={(props) => <DrawerContent navigation={props.navigation} />}
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          width: '75%',
        },
        // Disable gesture navigation
        gestureEnabled: false,
        swipeEnabled: false,
      }}
    >
      <Drawer.Screen
        name="HomeDrawer"
        component={MainStackNavigator}
        options={{
          gestureEnabled: false,
          swipeEnabled: false,
        }}
      />
    </Drawer.Navigator>
  );
};

// Stack navigator for main app screens
const MainStackNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        // Disable gesture navigation for all screens
        gestureEnabled: false,
        animationEnabled: true,
      }}
    >
      <Stack.Screen
        name="Home"
        component={HomeScreen}
        options={{
          gestureEnabled: false,
        }}
      />

      {/* Implemented feature navigators */}
      <Stack.Screen
        name="ContactsList"
        component={ContactsNavigator}
        options={{
          gestureEnabled: false,
        }}
      />

      <Stack.Screen
        name="ProductsList"
        component={ProductsNavigator}
        options={{
          gestureEnabled: false,
        }}
      />

      {/* Discuss/Chat screens */}
      <Stack.Screen
        name="Discuss"
        component={DiscussScreen}
        options={{
          headerShown: true,
          title: 'Chat',
          gestureEnabled: false,
        }}
      />
      <Stack.Screen
        name="DiscussChat"
        component={DiscussChatScreen}
        options={{
          headerShown: true,
          gestureEnabled: false,
        }}
      />

      {/* These would be actual screens in a full implementation */}
      <Stack.Screen name="InventoryList" component={PlaceholderScreen} options={{ headerShown: true, title: 'Inventory' }} />
      <Stack.Screen name="InventoryDetail" component={PlaceholderScreen} options={{ headerShown: true, title: 'Inventory Item' }} />
      <Stack.Screen
        name="HelpdeskTickets"
        component={HelpdeskNavigator}
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
      />
      <Stack.Screen
        name="HelpdeskList"
        component={HelpdeskNavigator}
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
      />
      <Stack.Screen name="CalendarView" component={PlaceholderScreen} options={{ headerShown: true, title: 'Calendar' }} />
      <Stack.Screen name="CalendarDetail" component={PlaceholderScreen} options={{ headerShown: true, title: 'Event Details' }} />
      <Stack.Screen name="ActivityList" component={PlaceholderScreen} options={{ headerShown: true, title: 'All Activity' }} />
      <Stack.Screen name="SalesDetail" component={PlaceholderScreen} options={{ headerShown: true, title: 'Sale Details' }} />
      <Stack.Screen name="TicketDetail" component={PlaceholderScreen} options={{ headerShown: true, title: 'Ticket Details' }} />
      <Stack.Screen name="InvoiceDetail" component={PlaceholderScreen} options={{ headerShown: true, title: 'Invoice Details' }} />
      <Stack.Screen name="ReportDetail" component={PlaceholderScreen} options={{ headerShown: true, title: 'Report Details' }} />
      <Stack.Screen name="Favorites" component={PlaceholderScreen} options={{ headerShown: true, title: 'Favorites' }} />
      <Stack.Screen name="Profile" component={ProfileScreen} options={{ headerShown: true, title: 'Profile' }} />
      <Stack.Screen name="EditProfile" component={EditProfileScreen} options={{ headerShown: true, title: 'Edit Profile' }} />
      <Stack.Screen name="Settings" component={SettingsScreen} options={{ headerShown: true, title: 'Settings' }} />
      <Stack.Screen name="Notifications" component={PlaceholderScreen} options={{ headerShown: true, title: 'Notifications' }} />
    </Stack.Navigator>
  );
};

// Main app navigator component
export const AppNavigator = () => {
  const { isLoggedIn } = useAuth();

  // Create a custom theme to disable gesture navigation app-wide
  const navigationTheme = {
    colors: {
      primary: '#3B82F6',
      background: '#FFFFFF',
      card: '#FFFFFF',
      text: '#333333',
      border: '#E5E7EB',
      notification: '#EF4444',
    },
  };

  // Create custom navigation options that disable gestures
  const screenOptions = {
    gestureEnabled: false, // Disable gesture navigation app-wide
    swipeEnabled: false,   // Disable swipe gestures
    animationEnabled: true // Keep animations
  };

  return (
    <NavigationContainer theme={navigationTheme}>
      {isLoggedIn ? (
        // Logged in screens with drawer navigation
        <MainDrawerNavigator />
      ) : (
        // Auth screen
        <Stack.Navigator
          screenOptions={{
            headerShown: false,
            ...screenOptions // Apply gesture disabling options
          }}
        >
          <Stack.Screen name="Login" component={LoginScreen} />
        </Stack.Navigator>
      )}
    </NavigationContainer>
  );
};

// Placeholder screen for screens we haven't implemented yet
const PlaceholderScreen = ({ route }: { route: any }) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Screen Not Implemented</Text>
      <Text style={styles.subtitle}>This is a placeholder for:</Text>
      <Text style={styles.routeName}>{route.name}</Text>
      {route.params && (
        <Text style={styles.params}>
          Params: {JSON.stringify(route.params, null, 2)}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 5,
    color: '#666',
  },
  routeName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#3B82F6',
    marginBottom: 20,
  },
  params: {
    fontSize: 14,
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 5,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#ddd',
    alignSelf: 'stretch',
  },
  drawerContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  drawerHeader: {
    padding: 20,
    backgroundColor: '#3B82F6',
  },
  drawerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
  },
  drawerSubtitle: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 5,
  },
  drawerContent: {
    flex: 1,
    paddingTop: 10,
  },
  menuItemContainer: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuIcon: {
    marginRight: 15,
    width: 22,
  },
  menuTextContainer: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  menuItemDescription: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  drawerFooter: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  logoutButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoutIcon: {
    marginRight: 10,
  },
  logoutButton: {
    fontSize: 16,
    color: '#EF4444',
    fontWeight: '500',
  },
});

export default AppNavigator;