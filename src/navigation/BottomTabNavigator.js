import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { View, Text } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../contexts/ThemeContext';

// Import screens
import HomeScreen from '../features/home/<USER>/HomeScreen';
import ContactsNavigator from '../features/contacts/ContactsNavigator';
import HelpdeskNavigator from '../features/helpdesk/navigation/HelpdeskNavigator';
import DiscussScreen from '../features/discuss/screens/DiscussScreen';

const Tab = createBottomTabNavigator();

// Placeholder screen for Orders
const OrdersScreen = () => {
  const { colors } = useTheme();
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.background }}>
      <Icon name="cart" size={48} color={colors.textSecondary} />
      <Text style={{ color: colors.text, fontSize: 18, fontWeight: '600', marginTop: 16 }}>
        Orders
      </Text>
      <Text style={{ color: colors.textSecondary, fontSize: 14, marginTop: 8, textAlign: 'center' }}>
        Orders module coming soon
      </Text>
    </View>
  );
};

const BottomTabNavigator = () => {
  const { colors } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          switch (route.name) {
            case 'Home':
              iconName = 'view-dashboard';
              break;
            case 'Contacts':
              iconName = 'account-group';
              break;
            case 'Chat':
              iconName = 'chat';
              break;
            case 'Orders':
              iconName = 'cart';
              break;
            case 'Helpdesk':
              iconName = 'ticket-account';
              break;
            default:
              iconName = 'circle';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.border,
          borderTopWidth: 1,
          paddingBottom: 8,
          paddingTop: 8,
          height: 70,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
          marginTop: 4,
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen}
        options={{
          tabBarLabel: 'Home',
        }}
      />
      <Tab.Screen 
        name="Contacts" 
        component={ContactsNavigator}
        options={{
          tabBarLabel: 'Contacts',
        }}
      />
      <Tab.Screen 
        name="Chat" 
        component={DiscussScreen}
        options={{
          tabBarLabel: 'Chat',
        }}
      />
      <Tab.Screen 
        name="Orders" 
        component={OrdersScreen}
        options={{
          tabBarLabel: 'Orders',
        }}
      />
      <Tab.Screen 
        name="Helpdesk" 
        component={HelpdeskNavigator}
        options={{
          tabBarLabel: 'Helpdesk',
        }}
      />
    </Tab.Navigator>
  );
};

export default BottomTabNavigator;
