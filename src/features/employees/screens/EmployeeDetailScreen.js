import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  Linking,
  RefreshControl
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { hrEmployeeAPI as employeesAPI } from '../../../api/models/employeesApi';

const EmployeeDetailScreen = () => {
  const { colors } = useTheme();
  const navigation = useNavigation();
  const route = useRoute();
  const { employeeId, employeeName } = route.params;

  const [employee, setEmployee] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadEmployeeDetails();
  }, [employeeId]);

  const loadEmployeeDetails = async () => {
    try {
      setLoading(true);
      const employeeData = await employeesAPI.getEmployee(employeeId);
      setEmployee(employeeData);
    } catch (error) {
      console.error('Error loading employee details:', error);
      Alert.alert('Error', 'Failed to load employee details');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadEmployeeDetails();
    } catch (error) {
      // Error already handled in loadEmployeeDetails
    } finally {
      setRefreshing(false);
    }
  };

  const handleEdit = () => {
    navigation.navigate('EmployeeEdit', {
      employeeId: employee.id,
      isEditing: true
    });
  };

  const handleCall = (phone) => {
    if (phone) {
      Linking.openURL(`tel:${phone}`);
    }
  };

  const handleEmail = (email) => {
    if (email) {
      Linking.openURL(`mailto:${email}`);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading employee details...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!employee) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <Icon name="account-hard-hat" size={64} color={colors.textSecondary} />
          <Text style={[styles.errorTitle, { color: colors.text }]}>
            Employee Not Found
          </Text>
          <Text style={[styles.errorSubtitle, { color: colors.textSecondary }]}>
            The employee you're looking for doesn't exist or has been removed.
          </Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.primary }]}
            onPress={() => navigation.goBack()}
          >
            <Text style={[styles.backButtonText, { color: colors.onPrimary }]}>
              Go Back
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* Header Section */}
        <View style={[styles.header, { backgroundColor: colors.surface }]}>
          <View style={styles.headerContent}>
            {/* Employee Image */}
            <View style={styles.imageContainer}>
              {employee.image_128 ? (
                <Image
                  source={{ uri: `data:image/png;base64,${employee.image_128}` }}
                  style={styles.employeeImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={[styles.placeholderImage, { backgroundColor: colors.border }]}>
                  <Icon name="account-hard-hat" size={48} color={colors.textSecondary} />
                </View>
              )}

              {/* Status Badge */}
              <View style={[styles.statusBadge, { backgroundColor: employee.active ? colors.success : colors.error }]}>
                <Icon
                  name={employee.active ? 'check' : 'close'}
                  size={16}
                  color={colors.onPrimary}
                />
              </View>
            </View>

            {/* Employee Info */}
            <View style={styles.employeeInfo}>
              <Text style={[styles.employeeName, { color: colors.text }]}>
                {employee.name}
              </Text>
              {employee.job_id && (
                <Text style={[styles.jobTitle, { color: colors.textSecondary }]}>
                  {employee.job_id[1]}
                </Text>
              )}
              {employee.department_id && (
                <View style={[styles.departmentChip, { backgroundColor: colors.primary + '20' }]}>
                  <Icon name="domain" size={14} color={colors.primary} />
                  <Text style={[styles.departmentText, { color: colors.primary }]}>
                    {employee.department_id[1]}
                  </Text>
                </View>
              )}
            </View>

            {/* Edit Button */}
            <TouchableOpacity
              style={[styles.editButton, { backgroundColor: colors.primary }]}
              onPress={handleEdit}
            >
              <Icon name="pencil" size={20} color={colors.onPrimary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Contact Information */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Contact Information
          </Text>

          {employee.work_email && (
            <TouchableOpacity
              style={styles.contactItem}
              onPress={() => handleEmail(employee.work_email)}
            >
              <Icon name="email" size={20} color={colors.primary} />
              <View style={styles.contactInfo}>
                <Text style={[styles.contactLabel, { color: colors.textSecondary }]}>
                  Work Email
                </Text>
                <Text style={[styles.contactValue, { color: colors.text }]}>
                  {employee.work_email}
                </Text>
              </View>
              <Icon name="open-in-new" size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          )}

          {employee.work_phone && (
            <TouchableOpacity
              style={styles.contactItem}
              onPress={() => handleCall(employee.work_phone)}
            >
              <Icon name="phone" size={20} color={colors.success} />
              <View style={styles.contactInfo}>
                <Text style={[styles.contactLabel, { color: colors.textSecondary }]}>
                  Work Phone
                </Text>
                <Text style={[styles.contactValue, { color: colors.text }]}>
                  {employee.work_phone}
                </Text>
              </View>
              <Icon name="phone" size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          )}

          {employee.mobile_phone && (
            <TouchableOpacity
              style={styles.contactItem}
              onPress={() => handleCall(employee.mobile_phone)}
            >
              <Icon name="cellphone" size={20} color={colors.secondary} />
              <View style={styles.contactInfo}>
                <Text style={[styles.contactLabel, { color: colors.textSecondary }]}>
                  Mobile Phone
                </Text>
                <Text style={[styles.contactValue, { color: colors.text }]}>
                  {employee.mobile_phone}
                </Text>
              </View>
              <Icon name="phone" size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>

        {/* Work Information */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Work Information
          </Text>

          <View style={styles.infoItem}>
            <Icon name="briefcase" size={20} color={colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                Job Position
              </Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {employee.job_id ? employee.job_id[1] : 'Not assigned'}
              </Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <Icon name="domain" size={20} color={colors.secondary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                Department
              </Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {employee.department_id ? employee.department_id[1] : 'Not assigned'}
              </Text>
            </View>
          </View>

          {employee.parent_id && (
            <View style={styles.infoItem}>
              <Icon name="account-supervisor" size={20} color={colors.warning} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Manager
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {employee.parent_id[1]}
                </Text>
              </View>
            </View>
          )}

          {employee.coach_id && (
            <View style={styles.infoItem}>
              <Icon name="account-star" size={20} color={colors.success} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Coach
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {employee.coach_id[1]}
                </Text>
              </View>
            </View>
          )}

          {employee.work_location && (
            <View style={styles.infoItem}>
              <Icon name="map-marker" size={20} color={colors.error} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Work Location
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {employee.work_location}
                </Text>
              </View>
            </View>
          )}
        </View>

        {/* Personal Information */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Personal Information
          </Text>

          {employee.birthday && (
            <View style={styles.infoItem}>
              <Icon name="cake" size={20} color={colors.primary} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Birthday
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {formatDate(employee.birthday)}
                </Text>
              </View>
            </View>
          )}

          {employee.marital && (
            <View style={styles.infoItem}>
              <Icon name="heart" size={20} color={colors.secondary} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Marital Status
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {employee.marital}
                </Text>
              </View>
            </View>
          )}

          {employee.employee_type && (
            <View style={styles.infoItem}>
              <Icon name="account-badge" size={20} color={colors.warning} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Employee Type
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {employee.employee_type}
                </Text>
              </View>
            </View>
          )}

          <View style={styles.infoItem}>
            <Icon name="account-key" size={20} color={colors.success} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                Employee ID
              </Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                #{employee.id}
              </Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary }]}
            onPress={handleEdit}
          >
            <Icon name="pencil" size={20} color={colors.onPrimary} />
            <Text style={[styles.actionButtonText, { color: colors.onPrimary }]}>
              Edit Employee
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.success }]}
            onPress={() => Alert.alert('Attendance', 'Attendance management coming soon')}
          >
            <Icon name="clock-check" size={20} color={colors.onPrimary} />
            <Text style={[styles.actionButtonText, { color: colors.onPrimary }]}>
              View Attendance
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.warning }]}
            onPress={() => Alert.alert('Contracts', 'Contract management coming soon')}
          >
            <Icon name="file-document" size={20} color={colors.onPrimary} />
            <Text style={[styles.actionButtonText, { color: colors.onPrimary }]}>
              View Contracts
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    gap: 16,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
  },
  errorSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  backButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },

  // Header Styles
  header: {
    padding: 20,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  imageContainer: {
    position: 'relative',
  },
  employeeImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  placeholderImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  employeeInfo: {
    flex: 1,
    gap: 4,
  },
  employeeName: {
    fontSize: 20,
    fontWeight: '700',
  },
  jobTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  departmentChip: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  departmentText: {
    fontSize: 12,
    fontWeight: '600',
  },
  editButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Section Styles
  section: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },

  // Contact Item Styles
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  contactInfo: {
    flex: 1,
  },
  contactLabel: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  contactValue: {
    fontSize: 16,
    marginTop: 2,
  },

  // Info Item Styles
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  infoValue: {
    fontSize: 16,
    marginTop: 2,
  },

  // Action Buttons
  actionsContainer: {
    padding: 16,
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 14,
    borderRadius: 8,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default EmployeeDetailScreen;