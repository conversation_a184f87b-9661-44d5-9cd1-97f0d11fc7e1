import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
  SafeAreaView,
  TextInput,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme } from '../../../contexts/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import BottomSheet, { BottomSheetView, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { hrEmployeeAPI as employeesAPI } from '../../../api/models/employeesApi';
import EmployeeCard from '../components/EmployeeCard';

const { width: screenWidth } = Dimensions.get('window');

const EmployeesListScreen = () => {
  const navigation = useNavigation();
  const { colors } = useTheme();

  // Bottom Sheet refs
  const bottomSheetRef = useRef(null);
  const actionBottomSheetRef = useRef(null);

  // Bottom Sheet snap points
  const snapPoints = useMemo(() => ['25%', '50%', '90%'], []);
  const actionSnapPoints = useMemo(() => ['30%', '50%'], []);

  // State management
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  // View state
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [selectedEmployees, setSelectedEmployees] = useState([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Reload when screen focuses
  useFocusEffect(
    useCallback(() => {
      if (employees.length === 0) {
        loadInitialData();
      }
    }, [])
  );

  // Search effect
  useEffect(() => {
    if (searchQuery.length > 2 || searchQuery.length === 0) {
      searchEmployees();
    }
  }, [searchQuery]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      await loadEmployees(true);
    } catch (error) {
      console.error('Error loading initial data:', error);
      Alert.alert('Error', 'Failed to load employees. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadEmployees = async (reset = false) => {
    try {
      if (reset) {
        setCurrentPage(0);
        setHasMore(true);
      }

      const offset = reset ? 0 : currentPage * 20;

      const employeesData = await employeesAPI.getEmployees({
        limit: 20,
        offset,
        order: 'name asc'
      });

      if (employeesData && employeesData.length > 0) {
        if (reset) {
          setEmployees(employeesData);
        } else {
          setEmployees(prev => [...prev, ...employeesData]);
        }

        setHasMore(employeesData.length === 20);
        setCurrentPage(prev => reset ? 1 : prev + 1);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error loading employees:', error);
      throw error;
    }
  };

  const searchEmployees = async () => {
    try {
      setLoading(true);
      let employeesData;

      if (searchQuery.trim()) {
        employeesData = await employeesAPI.searchEmployees(searchQuery, {
          limit: 50
        });
      } else {
        employeesData = await employeesAPI.getEmployees({
          limit: 20,
          order: 'name asc'
        });
      }

      setEmployees(employeesData || []);
      setCurrentPage(1);
      setHasMore(searchQuery.trim() ? false : true);
    } catch (error) {
      console.error('Error searching employees:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadEmployees(true);
    } catch (error) {
      // Error already handled in loadEmployees
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = async () => {
    if (!hasMore || loadingMore || searchQuery.trim()) return;

    try {
      setLoadingMore(true);
      await loadEmployees(false);
    } catch (error) {
      // Error already handled in loadEmployees
    } finally {
      setLoadingMore(false);
    }
  };

  const handleEmployeePress = (employee) => {
    if (isSelectionMode) {
      toggleEmployeeSelection(employee);
    } else {
      navigation.navigate('EmployeeDetail', {
        employeeId: employee.id,
        employeeName: employee.name
      });
    }
  };

  const handleEmployeeLongPress = (employee) => {
    if (!isSelectionMode) {
      setIsSelectionMode(true);
      setSelectedEmployees([employee.id]);
    }
  };

  const toggleEmployeeSelection = (employee) => {
    setSelectedEmployees(prev => {
      if (prev.includes(employee.id)) {
        const newSelection = prev.filter(id => id !== employee.id);
        if (newSelection.length === 0) {
          setIsSelectionMode(false);
        }
        return newSelection;
      } else {
        return [...prev, employee.id];
      }
    });
  };

  const clearSelection = () => {
    setSelectedEmployees([]);
    setIsSelectionMode(false);
  };

  const showEmployeeDetails = (employee) => {
    setSelectedEmployee(employee);
    bottomSheetRef.current?.expand();
  };

  const renderEmployeeItem = ({ item, index }) => (
    <EmployeeCard
      employee={item}
      viewMode={viewMode}
      onPress={() => handleEmployeePress(item)}
      onLongPress={() => handleEmployeeLongPress(item)}
      onDetailsPress={() => showEmployeeDetails(item)}
      isSelected={selectedEmployees.includes(item.id)}
      isSelectionMode={isSelectionMode}
      index={index}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <Icon name="magnify" size={20} color={colors.textSecondary} style={styles.searchIcon} />
        <TextInput
          style={[styles.searchInput, { color: colors.text }]}
          placeholder="Search employees..."
          placeholderTextColor={colors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
          returnKeyType="search"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Icon name="close-circle" size={18} color={colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>

      {/* View Controls */}
      <View style={[styles.viewControls, { backgroundColor: colors.surface }]}>
        <View style={styles.viewModeToggle}>
          <TouchableOpacity
            style={[
              styles.viewModeButton,
              viewMode === 'grid' && { backgroundColor: colors.primary }
            ]}
            onPress={() => setViewMode('grid')}
          >
            <Icon
              name="view-grid"
              size={20}
              color={viewMode === 'grid' ? colors.onPrimary : colors.text}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.viewModeButton,
              viewMode === 'list' && { backgroundColor: colors.primary }
            ]}
            onPress={() => setViewMode('list')}
          >
            <Icon
              name="view-list"
              size={20}
              color={viewMode === 'list' ? colors.onPrimary : colors.text}
            />
          </TouchableOpacity>
        </View>

        {/* Results Count */}
        <Text style={[styles.resultsCount, { color: colors.textSecondary }]}>
          {employees.length} employee{employees.length !== 1 ? 's' : ''}
        </Text>
      </View>

      {/* Selection Mode Header */}
      {isSelectionMode && (
        <View style={[styles.selectionHeader, { backgroundColor: colors.primary }]}>
          <View style={styles.selectionInfo}>
            <TouchableOpacity onPress={clearSelection}>
              <Icon name="close" size={24} color={colors.onPrimary} />
            </TouchableOpacity>
            <Text style={[styles.selectionText, { color: colors.onPrimary }]}>
              {selectedEmployees.length} selected
            </Text>
          </View>
          <View style={styles.selectionActions}>
            <TouchableOpacity
              style={styles.selectionAction}
              onPress={() => Alert.alert('Export', 'Export functionality coming soon')}
            >
              <Icon name="export" size={20} color={colors.onPrimary} />
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );

  const renderFooter = () => {
    if (!loadingMore) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading more employees...
        </Text>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="account-hard-hat" size={64} color={colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        {searchQuery ? 'No employees found' : 'No employees available'}
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        {searchQuery
          ? `Try adjusting your search`
          : 'Employees will appear here once they are added'
        }
      </Text>
      {searchQuery && (
        <TouchableOpacity
          style={[styles.clearSearchButton, { backgroundColor: colors.primary }]}
          onPress={() => setSearchQuery('')}
        >
          <Text style={[styles.clearSearchText, { color: colors.onPrimary }]}>
            Clear Search
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (loading && employees.length === 0) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading employees...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <FlatList
        data={employees}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderEmployeeItem}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.3}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode} // Force re-render when view mode changes
        contentContainerStyle={employees.length === 0 ? styles.emptyContainer : undefined}
        showsVerticalScrollIndicator={false}
      />

      {/* Floating Action Button */}
      <TouchableOpacity
        style={[styles.fab, { backgroundColor: colors.primary }]}
        onPress={() => navigation.navigate('EmployeeEdit', { isEditing: false })}
      >
        <Icon name="plus" size={28} color={colors.onPrimary} />
      </TouchableOpacity>

      {/* Employee Details Bottom Sheet */}
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: colors.surface }}
        handleIndicatorStyle={{ backgroundColor: colors.border }}
      >
        <BottomSheetScrollView contentContainerStyle={styles.bottomSheetContent}>
          {selectedEmployee && (
            <EmployeeDetails
              employee={selectedEmployee}
              onClose={() => bottomSheetRef.current?.close()}
              onEdit={() => {
                bottomSheetRef.current?.close();
                navigation.navigate('EmployeeEdit', {
                  employeeId: selectedEmployee.id,
                  isEditing: true
                });
              }}
              onAttendance={() => {
                bottomSheetRef.current?.close();
                // Handle attendance check-in/out
                Alert.alert('Attendance', 'Attendance functionality coming soon');
              }}
            />
          )}
        </BottomSheetScrollView>
      </BottomSheet>
    </SafeAreaView>
  );
};

// Employee Details Component for Bottom Sheet
const EmployeeDetails = ({ employee, onClose, onEdit, onAttendance }) => {
  const { colors } = useTheme();

  return (
    <View style={styles.employeeDetails}>
      <View style={styles.detailsHeader}>
        <Text style={[styles.detailsTitle, { color: colors.text }]}>
          Employee Details
        </Text>
        <TouchableOpacity onPress={onClose}>
          <Icon name="close" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <Text style={[styles.detailsEmployeeName, { color: colors.text }]}>
        {employee.name}
      </Text>

      {employee.job_id && (
        <Text style={[styles.detailsJobTitle, { color: colors.textSecondary }]}>
          {employee.job_id[1]}
        </Text>
      )}

      {employee.department_id && (
        <View style={styles.detailsRow}>
          <Text style={[styles.detailsLabel, { color: colors.textSecondary }]}>
            Department:
          </Text>
          <Text style={[styles.detailsValue, { color: colors.text }]}>
            {employee.department_id[1]}
          </Text>
        </View>
      )}

      {employee.work_email && (
        <View style={styles.detailsRow}>
          <Text style={[styles.detailsLabel, { color: colors.textSecondary }]}>
            Email:
          </Text>
          <Text style={[styles.detailsValue, { color: colors.text }]}>
            {employee.work_email}
          </Text>
        </View>
      )}

      {employee.work_phone && (
        <View style={styles.detailsRow}>
          <Text style={[styles.detailsLabel, { color: colors.textSecondary }]}>
            Phone:
          </Text>
          <Text style={[styles.detailsValue, { color: colors.text }]}>
            {employee.work_phone}
          </Text>
        </View>
      )}

      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.primary }]}
          onPress={onEdit}
        >
          <Icon name="pencil" size={20} color={colors.onPrimary} />
          <Text style={[styles.actionButtonText, { color: colors.onPrimary }]}>
            Edit
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.success }]}
          onPress={onAttendance}
        >
          <Icon name="clock-check" size={20} color={colors.onPrimary} />
          <Text style={[styles.actionButtonText, { color: colors.onPrimary }]}>
            Attendance
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    paddingBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginHorizontal: 16,
    marginVertical: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '400',
    paddingVertical: 0,
  },
  viewControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 8,
  },
  viewModeToggle: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderRadius: 6,
    padding: 2,
  },
  viewModeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  resultsCount: {
    fontSize: 14,
    fontWeight: '500',
  },
  selectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 8,
  },
  selectionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  selectionText: {
    fontSize: 16,
    fontWeight: '600',
  },
  selectionActions: {
    flexDirection: 'row',
    gap: 16,
  },
  selectionAction: {
    padding: 4,
  },
  footerLoader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    gap: 8,
  },
  emptyContainer: {
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 24,
  },
  clearSearchButton: {
    marginTop: 24,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  clearSearchText: {
    fontSize: 16,
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  bottomSheetContent: {
    padding: 16,
  },
  employeeDetails: {
    gap: 16,
  },
  detailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailsTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  detailsEmployeeName: {
    fontSize: 18,
    fontWeight: '600',
  },
  detailsJobTitle: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailsLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  detailsValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default EmployeesListScreen;
