import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import EmployeesListScreen from './screens/EmployeesListScreen';
import EmployeeDetailScreen from './screens/EmployeeDetailScreen';
import EmployeeEditScreen from './screens/EmployeeEditScreen';
import { useTheme } from '../../contexts/ThemeContext';

const Stack = createStackNavigator();

const EmployeesNavigator = () => {
  const { colors } = useTheme();

  return (
    <Stack.Navigator
      initialRouteName="EmployeesList"
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: colors.onPrimary,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        headerBackTitleVisible: false,
      }}
    >
      <Stack.Screen
        name="EmployeesList"
        component={EmployeesListScreen}
        options={{
          title: 'Employees',
          headerLargeTitle: true,
        }}
      />
      <Stack.Screen
        name="EmployeeDetail"
        component={EmployeeDetailScreen}
        options={({ route }) => ({
          title: route.params?.employeeName || 'Employee Details',
        })}
      />
      <Stack.Screen
        name="EmployeeEdit"
        component={EmployeeEditScreen}
        options={({ route }) => ({
          title: route.params?.isEditing ? 'Edit Employee' : 'New Employee',
        })}
      />
    </Stack.Navigator>
  );
};

export default EmployeesNavigator;
