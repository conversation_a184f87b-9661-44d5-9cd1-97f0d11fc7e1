import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const { width: screenWidth } = Dimensions.get('window');
const cardWidth = (screenWidth - 48) / 2; // Account for padding and gap

const EmployeeCard = ({
  employee,
  viewMode = 'grid',
  onPress,
  onLongPress,
  onDetailsPress,
  isSelected = false,
  isSelectionMode = false,
  index
}) => {
  const { colors } = useTheme();

  const getEmployeeTypeIcon = (type) => {
    switch (type) {
      case 'employee': return 'account';
      case 'student': return 'school';
      case 'trainee': return 'account-school';
      case 'contractor': return 'account-hard-hat';
      case 'freelance': return 'account-tie';
      default: return 'account';
    }
  };

  const getEmployeeTypeColor = (type) => {
    switch (type) {
      case 'employee': return colors.primary;
      case 'student': return colors.success;
      case 'trainee': return '#FF9500';
      case 'contractor': return '#8B5CF6';
      case 'freelance': return colors.secondary;
      default: return colors.primary;
    }
  };

  if (viewMode === 'list') {
    return (
      <TouchableOpacity
        style={[
          styles.listCard,
          { 
            backgroundColor: colors.surface,
            borderColor: isSelected ? colors.primary : colors.border
          },
          isSelected && { borderWidth: 2 }
        ]}
        onPress={onPress}
        onLongPress={onLongPress}
        activeOpacity={0.7}
      >
        {/* Selection Indicator */}
        {isSelectionMode && (
          <View style={[styles.selectionIndicator, { backgroundColor: colors.primary }]}>
            {isSelected && (
              <Icon name="check" size={16} color={colors.onPrimary} />
            )}
          </View>
        )}

        {/* Employee Image */}
        <View style={styles.listImageContainer}>
          {employee.image_128 ? (
            <Image
              source={{ uri: `data:image/png;base64,${employee.image_128}` }}
              style={styles.listImage}
              resizeMode="cover"
            />
          ) : (
            <View style={[styles.placeholderImage, { backgroundColor: colors.border }]}>
              <Icon name="account" size={24} color={colors.textSecondary} />
            </View>
          )}
        </View>

        {/* Employee Info */}
        <View style={styles.listContent}>
          <View style={styles.listHeader}>
            <Text 
              style={[styles.listEmployeeName, { color: colors.text }]}
              numberOfLines={2}
            >
              {employee.name}
            </Text>
            <TouchableOpacity onPress={onDetailsPress}>
              <Icon name="dots-vertical" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {employee.job_id && (
            <Text style={[styles.jobTitle, { color: colors.textSecondary }]}>
              {employee.job_id[1]}
            </Text>
          )}

          {employee.department_id && (
            <Text style={[styles.department, { color: colors.textSecondary }]}>
              {employee.department_id[1]}
            </Text>
          )}

          <View style={styles.listFooter}>
            <View style={styles.contactContainer}>
              {employee.work_email && (
                <Icon name="email" size={16} color={colors.primary} />
              )}
              {employee.work_phone && (
                <Icon name="phone" size={16} color={colors.success} />
              )}
            </View>

            <View style={styles.typeContainer}>
              <Icon 
                name={getEmployeeTypeIcon(employee.employee_type)} 
                size={16} 
                color={getEmployeeTypeColor(employee.employee_type)} 
              />
              <Text style={[styles.typeText, { color: getEmployeeTypeColor(employee.employee_type) }]}>
                {employee.employee_type || 'Employee'}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  }

  // Grid view
  return (
    <TouchableOpacity
      style={[
        styles.gridCard,
        { 
          backgroundColor: colors.surface,
          borderColor: isSelected ? colors.primary : colors.border,
          width: cardWidth
        },
        isSelected && { borderWidth: 2 }
      ]}
      onPress={onPress}
      onLongPress={onLongPress}
      activeOpacity={0.7}
    >
      {/* Selection Indicator */}
      {isSelectionMode && (
        <View style={[styles.selectionIndicator, { backgroundColor: colors.primary }]}>
          {isSelected && (
            <Icon name="check" size={16} color={colors.onPrimary} />
          )}
        </View>
      )}

      {/* Employee Image */}
      <View style={styles.imageContainer}>
        {employee.image_128 ? (
          <Image
            source={{ uri: `data:image/png;base64,${employee.image_128}` }}
            style={styles.employeeImage}
            resizeMode="cover"
          />
        ) : (
          <View style={[styles.placeholderImage, { backgroundColor: colors.border }]}>
            <Icon name="account" size={32} color={colors.textSecondary} />
          </View>
        )}

        {/* Employee Type Badge */}
        <View style={[styles.typeBadge, { backgroundColor: getEmployeeTypeColor(employee.employee_type) }]}>
          <Icon 
            name={getEmployeeTypeIcon(employee.employee_type)} 
            size={12} 
            color={colors.onPrimary} 
          />
        </View>

        {/* Details Button */}
        <TouchableOpacity 
          style={[styles.detailsButton, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
          onPress={onDetailsPress}
        >
          <Icon name="dots-vertical" size={16} color="white" />
        </TouchableOpacity>
      </View>

      {/* Employee Info */}
      <View style={styles.cardContent}>
        <Text 
          style={[styles.employeeName, { color: colors.text }]}
          numberOfLines={2}
        >
          {employee.name}
        </Text>

        {employee.job_id && (
          <Text style={[styles.jobTitle, { color: colors.textSecondary }]}>
            {employee.job_id[1]}
          </Text>
        )}

        {employee.department_id && (
          <Text style={[styles.department, { color: colors.textSecondary }]}>
            {employee.department_id[1]}
          </Text>
        )}

        <View style={styles.cardFooter}>
          <View style={styles.contactIcons}>
            {employee.work_email && (
              <Icon name="email" size={14} color={colors.primary} />
            )}
            {employee.work_phone && (
              <Icon name="phone" size={14} color={colors.success} />
            )}
            {employee.mobile_phone && (
              <Icon name="cellphone" size={14} color={colors.secondary} />
            )}
          </View>
          
          {employee.employee_type && (
            <Text style={[styles.typeLabel, { color: getEmployeeTypeColor(employee.employee_type) }]}>
              {employee.employee_type}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  // Grid Card Styles
  gridCard: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    marginHorizontal: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  imageContainer: {
    position: 'relative',
    height: 140,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: 'hidden',
  },
  employeeImage: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  typeBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    paddingHorizontal: 6,
    paddingVertical: 4,
    borderRadius: 10,
    minWidth: 24,
    alignItems: 'center',
  },
  detailsButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardContent: {
    padding: 12,
    gap: 4,
  },
  employeeName: {
    fontSize: 14,
    fontWeight: '600',
    lineHeight: 18,
  },
  jobTitle: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  department: {
    fontSize: 11,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  contactIcons: {
    flexDirection: 'row',
    gap: 4,
  },
  typeLabel: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'capitalize',
  },

  // List Card Styles
  listCard: {
    flexDirection: 'row',
    borderRadius: 12,
    borderWidth: 1,
    marginVertical: 4,
    marginHorizontal: 16,
    padding: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  listImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    overflow: 'hidden',
    marginRight: 12,
  },
  listImage: {
    width: '100%',
    height: '100%',
  },
  listContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  listEmployeeName: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 20,
    flex: 1,
    marginRight: 8,
  },
  listFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  contactContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  typeText: {
    fontSize: 11,
    fontWeight: '600',
    textTransform: 'capitalize',
  },

  // Selection Indicator
  selectionIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    borderWidth: 2,
    borderColor: 'white',
  },
});

export default EmployeeCard;
