// Simple Test Chat Screen - Minimal version for testing API connectivity

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Alert,
  SafeAreaView,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import discussAPI from '../../../api/models/discussApi';
import { getUser } from '../../../api/odooClient';

const DiscussChatScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();

  // Get channel info from route params
  const { channelId, channelName, channelType } = route.params || {};

  // State
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [newMessage, setNewMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [error, setError] = useState(null);

  // Set screen title
  useEffect(() => {
    navigation.setOptions({
      title: channelName || 'Chat',
      headerRight: () => (
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => {
            Alert.alert(
              'Channel Info',
              `Channel: ${channelName}\nType: ${channelType || 'channel'}\nID: ${channelId}`,
              [{ text: 'OK' }]
            );
          }}
        >
          <Icon name="information-outline" size={24} color="#007AFF" />
        </TouchableOpacity>
      ),
    });
  }, [navigation, channelId, channelName, channelType]);

  // Get current user
  useEffect(() => {
    const getCurrentUser = async () => {
      try {
        console.log('Getting current user...');
        const user = await getUser();
        console.log('Current user:', user);
        setCurrentUser(user);
      } catch (error) {
        console.error('Error getting current user:', error);
        setError('Failed to get user information');
      }
    };
    getCurrentUser();
  }, []);

  // Fetch messages
  useEffect(() => {
    const fetchMessages = async () => {
      if (!channelId) {
        setError('No channel ID provided');
        setLoading(false);
        return;
      }

      try {
        console.log(`Fetching messages for channel ${channelId}...`);
        setLoading(true);
        setError(null);

        const channelMessages = await discussAPI.getChannelMessages(channelId);
        console.log('Retrieved messages:', channelMessages);
        setMessages(channelMessages || []);
      } catch (error) {
        console.error('Error fetching messages:', error);
        setError('Failed to load messages. Please check the console for details.');
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();
  }, [channelId]);

  // Handle send message
  const handleSendMessage = async () => {
    if (!newMessage.trim() || sendingMessage) {
      return;
    }

    try {
      setSendingMessage(true);
      setError(null);

      console.log(`Sending message: "${newMessage}" to channel ${channelId}`);
      const result = await discussAPI.sendChannelMessage(
        channelId,
        newMessage.trim()
      );
      console.log('Send message result:', result);

      if (result) {
        // Clear input
        setNewMessage('');
        setError('Message sent successfully!');
        
        // Refresh messages after a delay
        setTimeout(async () => {
          try {
            const updatedMessages = await discussAPI.getChannelMessages(channelId);
            setMessages(updatedMessages || []);
            setError(null);
          } catch (refreshError) {
            console.error('Error refreshing messages:', refreshError);
          }
        }, 1000);
      } else {
        setError('Failed to send message. Check console for details.');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setError(`Failed to send message: ${error.message}`);
    } finally {
      setSendingMessage(false);
    }
  };

  // Render message item
  const renderMessage = ({ item }) => {
    const isOwnMessage = currentUser && item.author_id && item.author_id[0] === currentUser.id;

    return (
      <View style={[
        styles.messageContainer,
        isOwnMessage ? styles.ownMessage : styles.otherMessage
      ]}>
        <View style={[
          styles.messageBubble,
          isOwnMessage ? styles.ownMessageBubble : styles.otherMessageBubble
        ]}>
          <Text style={styles.authorName}>{item.authorName}</Text>
          <Text style={[
            styles.messageText,
            { color: isOwnMessage ? '#fff' : '#333' }
          ]}>
            {item.cleanBody || item.body || 'No content'}
          </Text>
          <Text style={[
            styles.messageTime,
            { color: isOwnMessage ? 'rgba(255,255,255,0.7)' : '#999' }
          ]}>
            {item.displayDate}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Debug Info */}
      <View style={styles.debugContainer}>
        <Text style={styles.debugText}>
          Channel ID: {channelId} | Messages: {messages.length}
        </Text>
        {currentUser && (
          <Text style={styles.debugText}>
            User: {currentUser.name} (ID: {currentUser.id})
          </Text>
        )}
      </View>

      {/* Error Display */}
      {error && (
        <View style={[
          styles.statusBar,
          error.includes('sent') || error.includes('successfully') 
            ? styles.successBar 
            : styles.errorBar
        ]}>
          <Text style={[
            styles.statusText,
            error.includes('sent') || error.includes('successfully')
              ? styles.successText
              : styles.errorText
          ]}>
            {error}
          </Text>
        </View>
      )}

      {/* Messages List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading messages...</Text>
        </View>
      ) : (
        <FlatList
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => `message-${item.id}`}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          inverted={true}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Icon name="chat-outline" size={48} color="#ccc" />
              <Text style={styles.emptyText}>No messages yet</Text>
              <Text style={styles.emptySubtext}>
                Start the conversation! Test message will appear here.
              </Text>
            </View>
          }
        />
      )}

      {/* Input Area */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.inputKeyboardContainer}
      >
        <View style={styles.inputContainer}>
          <View style={styles.inputWrapper}>
            <TextInput
              value={newMessage}
              onChangeText={setNewMessage}
              placeholder={`Message ${channelName || 'channel'}...`}
              style={styles.textInput}
              multiline={true}
              maxHeight={100}
            />
          </View>

          <TouchableOpacity
            style={[
              styles.sendButton,
              newMessage.trim() && !sendingMessage
                ? styles.sendButtonActive
                : styles.sendButtonInactive
            ]}
            onPress={handleSendMessage}
            disabled={!newMessage.trim() || sendingMessage}
          >
            {sendingMessage ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Icon
                name="send"
                size={20}
                color={newMessage.trim() ? "#fff" : "#999"}
              />
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  headerButton: {
    padding: 8,
  },
  debugContainer: {
    backgroundColor: '#e3f2fd',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#bbdefb',
  },
  debugText: {
    fontSize: 12,
    color: '#1976d2',
  },
  statusBar: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
  },
  errorBar: {
    backgroundColor: '#ffebee',
    borderBottomColor: '#ffcdd2',
  },
  successBar: {
    backgroundColor: '#e8f5e8',
    borderBottomColor: '#c8e6c9',
  },
  statusText: {
    fontSize: 14,
    textAlign: 'center',
  },
  errorText: {
    color: '#d32f2f',
  },
  successText: {
    color: '#2e7d32',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 8,
    flexGrow: 1,
  },
  messageContainer: {
    marginHorizontal: 16,
    marginVertical: 4,
  },
  ownMessage: {
    alignItems: 'flex-end',
  },
  otherMessage: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
    marginVertical: 2,
  },
  ownMessageBubble: {
    backgroundColor: '#007AFF',
    borderBottomRightRadius: 4,
  },
  otherMessageBubble: {
    backgroundColor: '#e5e5ea',
    borderBottomLeftRadius: 4,
  },
  authorName: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
    color: '#666',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
    marginBottom: 4,
  },
  messageTime: {
    fontSize: 10,
    opacity: 0.7,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    minHeight: 300,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
  inputKeyboardContainer: {
    backgroundColor: '#fff',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  inputWrapper: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    maxHeight: 100,
  },
  textInput: {
    fontSize: 16,
    color: '#333',
    minHeight: 20,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonActive: {
    backgroundColor: '#007AFF',
  },
  sendButtonInactive: {
    backgroundColor: '#f0f0f0',
  },
});

export default DiscussChatScreen;