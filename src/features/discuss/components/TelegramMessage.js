import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Animated
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const TelegramMessage = ({ 
  message, 
  isOwn, 
  showAvatar, 
  showTimestamp, 
  colors,
  onLongPress,
  onPress 
}) => {
  const getMessageStatus = () => {
    if (message.isOptimistic) {
      if (message.status === 'sending') {
        return <Icon name="clock-outline" size={14} color={colors.textSecondary} />;
      } else if (message.status === 'failed') {
        return <Icon name="alert-circle-outline" size={14} color={colors.error} />;
      }
    }
    
    // For sent messages, show delivery status
    if (isOwn && !message.isOptimistic) {
      return <Icon name="check" size={14} color={colors.success} />;
    }
    
    return null;
  };

  const getAvatarText = (name) => {
    if (!name) return '?';
    const words = name.split(' ');
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }
    return name[0].toUpperCase();
  };

  const formatTime = (dateString) => {
    if (message.status === 'sending') return 'sending...';
    if (message.status === 'failed') return 'failed';
    
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <View style={[
      styles.messageContainer,
      isOwn ? styles.ownMessageContainer : styles.otherMessageContainer
    ]}>
      {/* Timestamp (if should show) */}
      {showTimestamp && (
        <View style={styles.timestampContainer}>
          <Text style={[styles.timestampText, { color: colors.textSecondary }]}>
            {new Date(message.create_date).toLocaleDateString() === new Date().toLocaleDateString()
              ? formatTime(message.create_date)
              : new Date(message.create_date).toLocaleDateString([], { 
                  month: 'short', 
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })
            }
          </Text>
        </View>
      )}

      <View style={[
        styles.messageRow,
        isOwn ? styles.ownMessageRow : styles.otherMessageRow
      ]}>
        {/* Avatar (for other messages) */}
        {!isOwn && (
          <View style={styles.avatarContainer}>
            {showAvatar ? (
              message.authorAvatar ? (
                <Image 
                  source={{ uri: message.authorAvatar }} 
                  style={styles.avatar}
                />
              ) : (
                <View style={[styles.avatarPlaceholder, { backgroundColor: colors.primary }]}>
                  <Text style={[styles.avatarText, { color: colors.onPrimary }]}>
                    {getAvatarText(message.authorName)}
                  </Text>
                </View>
              )
            ) : (
              <View style={styles.avatarSpacer} />
            )}
          </View>
        )}

        {/* Message Bubble */}
        <TouchableOpacity
          style={[
            styles.messageBubble,
            isOwn ? [styles.ownBubble, { backgroundColor: colors.primary }] : [styles.otherBubble, { backgroundColor: colors.surface }],
            message.status === 'failed' && styles.failedBubble
          ]}
          onPress={onPress}
          onLongPress={onLongPress}
          activeOpacity={0.8}
        >
          {/* Author name (for group chats) */}
          {!isOwn && showAvatar && message.authorName && (
            <Text style={[styles.authorName, { color: colors.primary }]}>
              {message.authorName}
            </Text>
          )}

          {/* Message content */}
          <Text style={[
            styles.messageText,
            { color: isOwn ? colors.onPrimary : colors.text }
          ]}>
            {message.cleanBody || message.body || 'No content'}
          </Text>

          {/* Message footer with time and status */}
          <View style={styles.messageFooter}>
            <Text style={[
              styles.messageTime,
              { color: isOwn ? colors.onPrimary + 'CC' : colors.textSecondary }
            ]}>
              {formatTime(message.create_date)}
            </Text>
            
            {/* Message status for own messages */}
            {isOwn && (
              <View style={styles.messageStatus}>
                {getMessageStatus()}
              </View>
            )}
          </View>

          {/* Message tail */}
          <View style={[
            styles.messageTail,
            isOwn ? [styles.ownTail, { borderTopColor: colors.primary }] : [styles.otherTail, { borderTopColor: colors.surface }]
          ]} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  messageContainer: {
    marginVertical: 2,
    paddingHorizontal: 16,
  },
  ownMessageContainer: {
    alignItems: 'flex-end',
  },
  otherMessageContainer: {
    alignItems: 'flex-start',
  },
  timestampContainer: {
    alignItems: 'center',
    marginVertical: 8,
  },
  timestampText: {
    fontSize: 12,
    fontWeight: '500',
  },
  messageRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    maxWidth: '85%',
  },
  ownMessageRow: {
    justifyContent: 'flex-end',
  },
  otherMessageRow: {
    justifyContent: 'flex-start',
  },
  avatarContainer: {
    marginRight: 8,
    marginBottom: 4,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  avatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 12,
    fontWeight: '600',
  },
  avatarSpacer: {
    width: 32,
    height: 32,
  },
  messageBubble: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 18,
    position: 'relative',
    minWidth: 60,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  ownBubble: {
    borderBottomRightRadius: 4,
    marginLeft: 40,
  },
  otherBubble: {
    borderBottomLeftRadius: 4,
    marginRight: 40,
  },
  failedBubble: {
    opacity: 0.7,
    borderWidth: 1,
    borderColor: '#ff4444',
  },
  authorName: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 2,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
    marginBottom: 4,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 2,
  },
  messageTime: {
    fontSize: 11,
    fontWeight: '400',
  },
  messageStatus: {
    marginLeft: 4,
  },
  messageTail: {
    position: 'absolute',
    bottom: 0,
    width: 0,
    height: 0,
    borderStyle: 'solid',
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderTopWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
  },
  ownTail: {
    right: -8,
    borderBottomRightRadius: 4,
  },
  otherTail: {
    left: -8,
    borderBottomLeftRadius: 4,
  },
});

export default TelegramMessage;
