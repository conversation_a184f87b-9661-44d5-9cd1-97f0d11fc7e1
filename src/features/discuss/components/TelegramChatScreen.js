import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Alert,
  SafeAreaView,
  Image,
  Animated,
  Dimensions,
  StatusBar
} from 'react-native';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../../contexts/ThemeContext';
import discussAPI from '../../../api/models/discussApi';
import { getUser } from '../../../api/odooClient';
import TelegramMessage from './TelegramMessage';
import TelegramInputBar from './TelegramInputBar';

const { width: screenWidth } = Dimensions.get('window');

const TelegramChatScreen = () => {
  const { colors } = useTheme();
  const navigation = useNavigation();
  const route = useRoute();
  const flatListRef = useRef(null);
  const inputRef = useRef(null);
  const typingAnimation = useRef(new Animated.Value(0)).current;

  // Get channel info from route params
  const { channelId, channelName, channelType, participantInfo } = route.params || {};

  // State
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [newMessage, setNewMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [error, setError] = useState(null);
  const [isTyping, setIsTyping] = useState(false);
  const [onlineStatus, setOnlineStatus] = useState('online');
  const [lastSeen, setLastSeen] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  // Set up navigation header with Telegram-like styling
  useEffect(() => {
    navigation.setOptions({
      headerStyle: {
        backgroundColor: colors.primary,
        elevation: 4,
        shadowOpacity: 0.3,
      },
      headerTintColor: colors.onPrimary,
      headerTitle: () => (
        <TouchableOpacity
          style={styles.headerTitleContainer}
          onPress={() => {
            // Navigate to chat info screen
            Alert.alert('Chat Info', `${channelName}\n${channelType === 'chat' ? 'Direct Message' : 'Channel'}`);
          }}
        >
          <View style={styles.headerAvatar}>
            {participantInfo?.avatar ? (
              <Image source={{ uri: participantInfo.avatar }} style={styles.avatarImage} />
            ) : (
              <View style={[styles.avatarPlaceholder, { backgroundColor: colors.secondary }]}>
                <Text style={[styles.avatarText, { color: colors.onSecondary }]}>
                  {getAvatarText(channelName)}
                </Text>
              </View>
            )}
            {onlineStatus === 'online' && (
              <View style={[styles.onlineIndicator, { backgroundColor: colors.success }]} />
            )}
          </View>
          <View style={styles.headerInfo}>
            <Text style={[styles.headerTitle, { color: colors.onPrimary }]} numberOfLines={1}>
              {channelName || 'Chat'}
            </Text>
            <Text style={[styles.headerSubtitle, { color: colors.onPrimary + 'CC' }]} numberOfLines={1}>
              {onlineStatus === 'online' ? 'online' : lastSeen ? `last seen ${lastSeen}` : 'offline'}
            </Text>
          </View>
        </TouchableOpacity>
      ),
      headerRight: () => (
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => Alert.alert('Call', 'Voice call feature coming soon')}
          >
            <Icon name="phone" size={24} color={colors.onPrimary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => Alert.alert('Video Call', 'Video call feature coming soon')}
          >
            <Icon name="video" size={24} color={colors.onPrimary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => Alert.alert('More', 'More options coming soon')}
          >
            <Icon name="dots-vertical" size={24} color={colors.onPrimary} />
          </TouchableOpacity>
        </View>
      ),
    });
  }, [navigation, channelName, channelType, onlineStatus, lastSeen, colors, participantInfo]);

  // Get current user
  useEffect(() => {
    const getCurrentUser = async () => {
      try {
        const user = await getUser();
        setCurrentUser(user);
      } catch (error) {
        console.error('Error getting current user:', error);
        setError('Failed to get user information');
      }
    };
    getCurrentUser();
  }, []);

  // Fetch messages
  const fetchMessages = useCallback(async (forceRefresh = false) => {
    if (!channelId) {
      setError('No channel ID provided');
      setLoading(false);
      return;
    }

    try {
      if (!forceRefresh) setLoading(true);
      setError(null);

      const channelMessages = await discussAPI.getChannelMessages(channelId, forceRefresh);
      setMessages(channelMessages || []);

      // Scroll to bottom after loading messages
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);

    } catch (error) {
      console.error('Error fetching messages:', error);
      setError('Failed to load messages');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [channelId]);

  // Initial load and focus refresh
  useFocusEffect(
    useCallback(() => {
      fetchMessages();
    }, [fetchMessages])
  );

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    fetchMessages(true);
  }, [fetchMessages]);

  // Handle send message
  const handleSendMessage = async () => {
    if (!newMessage.trim() || sendingMessage) {
      return;
    }

    const messageText = newMessage.trim();
    setNewMessage('');

    // Optimistically add message to UI
    const optimisticMessage = {
      id: `temp-${Date.now()}`,
      body: messageText,
      author_id: [currentUser?.id, currentUser?.name],
      create_date: new Date().toISOString(),
      authorName: currentUser?.name || 'You',
      displayDate: 'sending...',
      cleanBody: messageText,
      attachmentCount: 0,
      isOptimistic: true,
      status: 'sending'
    };

    setMessages(prev => [optimisticMessage, ...prev]);

    try {
      setSendingMessage(true);

      const result = await discussAPI.sendChannelMessage(channelId, messageText);

      if (result) {
        // Remove optimistic message and refresh
        setMessages(prev => prev.filter(msg => msg.id !== optimisticMessage.id));
        setTimeout(() => fetchMessages(true), 500);
      } else {
        // Mark message as failed
        setMessages(prev => prev.map(msg =>
          msg.id === optimisticMessage.id
            ? { ...msg, status: 'failed', displayDate: 'failed' }
            : msg
        ));
      }
    } catch (error) {
      console.error('Error sending message:', error);
      // Mark message as failed
      setMessages(prev => prev.map(msg =>
        msg.id === optimisticMessage.id
          ? { ...msg, status: 'failed', displayDate: 'failed' }
          : msg
      ));
    } finally {
      setSendingMessage(false);
    }
  };

  // Helper functions
  const getAvatarText = (name) => {
    if (!name) return '?';
    const words = name.split(' ');
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }
    return name[0].toUpperCase();
  };

  const formatMessageTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'now';
    if (diffMins < 60) return `${diffMins}m`;
    if (diffHours < 24) return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    if (diffDays === 1) return 'yesterday';
    if (diffDays < 7) return date.toLocaleDateString([], { weekday: 'short' });
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  };

  const shouldShowAvatar = (item, index) => {
    if (index === messages.length - 1) return true;
    const nextMessage = messages[index + 1];
    return !nextMessage || nextMessage.author_id?.[0] !== item.author_id?.[0];
  };

  const shouldShowTimestamp = (item, index) => {
    if (index === messages.length - 1) return true;
    const nextMessage = messages[index + 1];
    if (!nextMessage) return true;

    const currentTime = new Date(item.create_date);
    const nextTime = new Date(nextMessage.create_date);
    const diffMins = Math.abs(currentTime - nextTime) / (1000 * 60);

    return diffMins > 5; // Show timestamp if messages are more than 5 minutes apart
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar backgroundColor={colors.primary} barStyle="light-content" />

      {/* Messages List */}
      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading messages...
          </Text>
        </View>
      ) : (
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={({ item, index }) => (
            <TelegramMessage
              message={item}
              isOwn={currentUser && item.author_id && item.author_id[0] === currentUser.id}
              showAvatar={shouldShowAvatar(item, index)}
              showTimestamp={shouldShowTimestamp(item, index)}
              colors={colors}
            />
          )}
          keyExtractor={(item) => `message-${item.id}`}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          inverted={true}
          onRefresh={handleRefresh}
          refreshing={refreshing}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Icon name="chat-outline" size={64} color={colors.textSecondary} />
              <Text style={[styles.emptyText, { color: colors.text }]}>
                No messages yet
              </Text>
              <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
                Start the conversation!
              </Text>
            </View>
          }
        />
      )}

      {/* Typing Indicator */}
      {isTyping && (
        <Animated.View style={[styles.typingContainer, { backgroundColor: colors.surface }]}>
          <Text style={[styles.typingText, { color: colors.textSecondary }]}>
            typing...
          </Text>
        </Animated.View>
      )}

      {/* Input Area */}
      <TelegramInputBar
        value={newMessage}
        onChangeText={setNewMessage}
        onSend={handleSendMessage}
        sending={sendingMessage}
        colors={colors}
        placeholder={`Message ${channelName || 'chat'}...`}
        inputRef={inputRef}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerAvatar: {
    position: 'relative',
    marginRight: 12,
  },
  avatarImage: {
    width: 36,
    height: 36,
    borderRadius: 18,
  },
  avatarPlaceholder: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 14,
    fontWeight: '600',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: 'white',
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  headerSubtitle: {
    fontSize: 12,
    marginTop: 1,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
    marginLeft: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 8,
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    minHeight: 300,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  typingContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  typingText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
});

export default TelegramChatScreen;
