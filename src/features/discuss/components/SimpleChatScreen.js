import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Alert,
  SafeAreaView
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import discussAPI from '../../../api/models/discussApi';
import { useTheme } from '../../../contexts/ThemeContext';

const SimpleChatScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { colors } = useTheme();
  
  const { channelId, channelName, channelType } = route.params || {};

  // State
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [messageText, setMessageText] = useState('');

  // Load messages
  const loadMessages = useCallback(async () => {
    try {
      setLoading(true);
      console.log(`Loading messages for channel ${channelId}`);
      
      const messagesData = await discussAPI.getChannelMessages(channelId);
      setMessages(messagesData || []);
      console.log(`Loaded ${messagesData?.length || 0} messages`);
    } catch (error) {
      console.error('Error loading messages:', error);
      Alert.alert('Error', 'Failed to load messages');
    } finally {
      setLoading(false);
    }
  }, [channelId]);

  // Send message
  const sendMessage = useCallback(async () => {
    if (!messageText.trim()) return;

    try {
      setSending(true);
      console.log(`Sending message: ${messageText}`);
      
      await discussAPI.sendChannelMessage(channelId, messageText.trim());
      setMessageText('');
      
      // Reload messages to see the new one
      await loadMessages();
      console.log('Message sent successfully');
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message');
    } finally {
      setSending(false);
    }
  }, [channelId, messageText, loadMessages]);

  // Load messages on mount
  useEffect(() => {
    if (channelId) {
      loadMessages();
    }
  }, [channelId, loadMessages]);

  // Set navigation header
  useEffect(() => {
    navigation.setOptions({
      title: channelName || `Chat ${channelId}`,
      headerStyle: {
        backgroundColor: colors.primary,
      },
      headerTintColor: colors.onPrimary,
      headerTitleStyle: {
        fontWeight: 'bold',
      },
    });
  }, [navigation, channelName, channelId, colors]);

  // Render message item
  const renderMessage = ({ item }) => (
    <View style={[styles.messageContainer, { backgroundColor: colors.surface }]}>
      <View style={styles.messageHeader}>
        <Text style={[styles.authorName, { color: colors.primary }]}>
          {item.authorName || 'Unknown'}
        </Text>
        <Text style={[styles.messageDate, { color: colors.textSecondary }]}>
          {item.displayDate || 'now'}
        </Text>
      </View>
      <Text style={[styles.messageBody, { color: colors.text }]}>
        {item.cleanBody || item.body || 'No content'}
      </Text>
    </View>
  );

  // Render empty state
  const renderEmpty = () => (
    <View style={[styles.emptyContainer, { backgroundColor: colors.background }]}>
      <Icon name="message-text-outline" size={64} color={colors.textSecondary} />
      <Text style={[styles.emptyText, { color: colors.text }]}>
        No messages yet
      </Text>
      <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
        Start the conversation!
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <KeyboardAvoidingView 
        style={styles.container} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Messages List */}
        {loading ? (
          <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              Loading messages...
            </Text>
          </View>
        ) : (
          <FlatList
            data={messages}
            renderItem={renderMessage}
            keyExtractor={(item) => `message-${item.id}`}
            style={[styles.messagesList, { backgroundColor: colors.background }]}
            contentContainerStyle={styles.messagesContent}
            ListEmptyComponent={renderEmpty}
            inverted={false}
            showsVerticalScrollIndicator={false}
          />
        )}

        {/* Input Bar */}
        <View style={[styles.inputContainer, { 
          backgroundColor: colors.surface,
          borderTopColor: colors.border 
        }]}>
          <TextInput
            style={[styles.textInput, { 
              backgroundColor: colors.background,
              color: colors.text,
              borderColor: colors.border
            }]}
            value={messageText}
            onChangeText={setMessageText}
            placeholder="Type a message..."
            placeholderTextColor={colors.textSecondary}
            multiline
            maxLength={1000}
            editable={!sending}
          />
          <TouchableOpacity
            style={[styles.sendButton, { 
              backgroundColor: messageText.trim() ? colors.primary : colors.textSecondary 
            }]}
            onPress={sendMessage}
            disabled={!messageText.trim() || sending}
          >
            {sending ? (
              <ActivityIndicator size="small" color={colors.onPrimary} />
            ) : (
              <Icon name="send" size={20} color={colors.onPrimary} />
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    flexGrow: 1,
    padding: 16,
  },
  messageContainer: {
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  authorName: {
    fontSize: 14,
    fontWeight: '600',
  },
  messageDate: {
    fontSize: 12,
  },
  messageBody: {
    fontSize: 16,
    lineHeight: 22,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SimpleChatScreen;
