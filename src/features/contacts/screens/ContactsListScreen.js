// Simple, fast contacts list screen - modeled after helpdesk implementation

import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  RefreshControl,
  TextInput,
  SafeAreaView,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useAuth } from '../../../contexts/AuthContext';
import { useTheme } from '../../../contexts/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { partnersAPI } from '../../../api/models/partnersApi';
import CustomBottomNavigation from '../../../components/CustomBottomNavigation';

const PAGE_SIZE = 80; // Same as helpdesk for good performance

const ContactsListScreen = () => {
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredContacts, setFilteredContacts] = useState([]);
  const [activeFilter, setActiveFilter] = useState('all'); // 'all', 'companies', 'contacts'

  const navigation = useNavigation();
  const { isLoggedIn } = useAuth();
  const { colors } = useTheme();

  // Load data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadContacts();
    }, [])
  );

  // Load contacts from API
  const loadContacts = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);

      // Reset state if refreshing
      if (forceRefresh) {
        setPage(0);
        setHasMore(true);
      }

      // Fetch contacts with pagination
      const contactsData = await partnersAPI.getContacts({
        limit: PAGE_SIZE,
        offset: forceRefresh ? 0 : page * PAGE_SIZE,
      });

      // Update state based on response
      if (Array.isArray(contactsData)) {
        let updatedContacts;
        if (forceRefresh) {
          updatedContacts = sortContacts([...contactsData]);
          setContacts(updatedContacts);
        } else {
          updatedContacts = sortContacts([...contacts, ...contactsData]);
          setContacts(updatedContacts);
        }

        // Check if we have more data to load
        const hasMoreData = contactsData.length === PAGE_SIZE;
        setHasMore(hasMoreData);

        if (!hasMoreData) {
          console.log('Reached end of contacts - no more data to load');
        }
      } else {
        console.error('Unexpected response format:', contactsData);
        setError('Invalid response format from server');
      }
    } catch (err) {
      console.error('Error loading contacts:', err);
      setError('Failed to load contacts. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  };

  // Handle refresh
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadContacts(true);
  }, []);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (!loadingMore && hasMore && !refreshing && contacts.length > 0) {
      console.log(`Loading more contacts. Current count: ${contacts.length}, Page: ${page + 1}`);
      setLoadingMore(true);
      setPage(prevPage => prevPage + 1);
      loadContacts(false);
    } else if (!hasMore) {
      console.log('Reached end of contacts list');
    }
  }, [loadingMore, hasMore, refreshing, contacts.length, page]);

  // Sort contacts: companies first, then individuals
  const sortContacts = useCallback((contactsList) => {
    return contactsList.sort((a, b) => {
      // Companies first
      if (a.is_company && !b.is_company) return -1;
      if (!a.is_company && b.is_company) return 1;

      // Within same type, sort alphabetically by name
      const nameA = (a.name || '').toLowerCase();
      const nameB = (b.name || '').toLowerCase();
      return nameA.localeCompare(nameB);
    });
  }, []);

  // Apply filters (search + type filter)
  const applyFilters = useCallback(() => {
    let filtered = [...contacts];

    // Apply type filter first
    if (activeFilter === 'companies') {
      filtered = filtered.filter(contact => contact.is_company);
    } else if (activeFilter === 'contacts') {
      filtered = filtered.filter(contact => !contact.is_company);
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(contact =>
        contact.name?.toLowerCase().includes(query) ||
        contact.email?.toLowerCase().includes(query) ||
        (contact.phone && String(contact.phone).includes(searchQuery)) ||
        (contact.mobile && String(contact.mobile).includes(searchQuery))
      );
    }

    setFilteredContacts(sortContacts(filtered));
  }, [contacts, searchQuery, activeFilter, sortContacts]);

  // Handle search
  const handleSearch = useCallback((text) => {
    setSearchQuery(text);
  }, []);

  // Handle filter change
  const handleFilterChange = useCallback((filter) => {
    setActiveFilter(filter);
  }, []);

  // Apply filters when contacts, search, or filter changes
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  // Navigate to contact detail
  const handleContactPress = (contact) => {
    navigation.navigate('ContactDetail', { id: contact.id });
  };

  // Create new contact
  const handleAddContact = () => {
    navigation.navigate('ContactForm', { mode: 'create' });
  };

  // Get initials for avatar
  const getInitials = (name) => {
    if (!name) return '??';
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Render contact item
  const renderContactItem = ({ item }) => {
    const isCompany = item.is_company;

    return (
      <TouchableOpacity
        style={[styles.contactItem, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}
        onPress={() => handleContactPress(item)}
      >
        <View style={styles.contactContent}>
          {/* Avatar */}
          <View style={styles.avatarContainer}>
            {item.image_128 ? (
              <Image
                source={{ uri: `data:image/png;base64,${item.image_128}` }}
                style={styles.avatar}
              />
            ) : (
              <View style={[styles.avatarPlaceholder, { backgroundColor: isCompany ? colors.primary : colors.secondary }]}>
                <Text style={[styles.avatarText, { color: colors.onPrimary }]}>
                  {getInitials(item.name)}
                </Text>
              </View>
            )}
            {isCompany && (
              <View style={[styles.companyBadge, { backgroundColor: colors.primary }]}>
                <Icon name="domain" size={12} color={colors.onPrimary} />
              </View>
            )}
          </View>

          {/* Contact Info */}
          <View style={styles.contactInfo}>
            <Text
              style={[
                styles.contactName,
                {
                  color: isCompany ? colors.primary : colors.text,
                  fontWeight: isCompany ? '600' : '500'
                }
              ]}
              numberOfLines={1}
            >
              {item.name}
            </Text>

            {item.email && (
              <Text style={[styles.contactEmail, { color: colors.textSecondary }]} numberOfLines={1}>
                {item.email}
              </Text>
            )}

            {(item.phone || item.mobile) && (
              <Text style={[styles.contactPhone, { color: colors.textSecondary }]} numberOfLines={1}>
                {item.mobile || item.phone}
              </Text>
            )}
          </View>


        </View>
      </TouchableOpacity>
    );
  };

  // Render empty component
  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Icon name="account-group-outline" size={64} color={colors.textSecondary} />
      <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
        {searchQuery ? 'No contacts found matching your search' : 'No contacts available'}
      </Text>
    </View>
  );

  // Render footer
  const renderFooter = () => {
    if (!loadingMore) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={[styles.footerText, { color: colors.textSecondary }]}>Loading more...</Text>
      </View>
    );
  };

  if (!isLoggedIn) {
    navigation.navigate('Login');
    return null;
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <Text style={[styles.title, { color: colors.text }]}>Contacts</Text>
        <TouchableOpacity onPress={handleAddContact}>
          <Icon name="plus" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: colors.surface }]}>
        <Icon name="magnify" size={20} color={colors.textSecondary} style={styles.searchIcon} />
        <TextInput
          style={[styles.searchInput, { color: colors.text }]}
          placeholder="Search contacts..."
          placeholderTextColor={colors.textSecondary}
          value={searchQuery}
          onChangeText={handleSearch}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => handleSearch('')}>
            <Icon name="close" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>

      {/* Filter Buttons */}
      <View style={[styles.filterContainer, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <TouchableOpacity
          style={[
            styles.filterButton,
            {
              backgroundColor: activeFilter === 'all' ? colors.primary : colors.background,
              borderColor: colors.border
            }
          ]}
          onPress={() => handleFilterChange('all')}
        >
          <Text style={[
            styles.filterButtonText,
            { color: activeFilter === 'all' ? colors.onPrimary : colors.text }
          ]}>
            All
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            {
              backgroundColor: activeFilter === 'companies' ? colors.primary : colors.background,
              borderColor: colors.border
            }
          ]}
          onPress={() => handleFilterChange('companies')}
        >
          <Text style={[
            styles.filterButtonText,
            { color: activeFilter === 'companies' ? colors.onPrimary : colors.text }
          ]}>
            Companies
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            {
              backgroundColor: activeFilter === 'contacts' ? colors.primary : colors.background,
              borderColor: colors.border
            }
          ]}
          onPress={() => handleFilterChange('contacts')}
        >
          <Text style={[
            styles.filterButtonText,
            { color: activeFilter === 'contacts' ? colors.onPrimary : colors.text }
          ]}>
            Contacts
          </Text>
        </TouchableOpacity>
      </View>

      {/* Main Content */}
      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading contacts...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Icon name="alert-circle-outline" size={48} color={colors.error} />
          <Text style={[styles.errorText, { color: colors.error }]}>{error}</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={() => loadContacts(true)}
          >
            <Text style={[styles.retryButtonText, { color: colors.onPrimary }]}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={filteredContacts}
          renderItem={renderContactItem}
          keyExtractor={(item) => `contact-${item.id}`}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyComponent}
          ListFooterComponent={renderFooter}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.3}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
            />
          }
          showsVerticalScrollIndicator={false}
        />
      )}

      {/* Custom Bottom Navigation */}
      <CustomBottomNavigation />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginVertical: 16,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  listContent: {
    flexGrow: 1,
  },
  contactItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  contactContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  avatarPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  companyBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  contactEmail: {
    fontSize: 14,
    marginBottom: 2,
  },
  contactPhone: {
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
  footerLoader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  footerText: {
    marginLeft: 8,
    fontSize: 14,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
    borderBottomWidth: 1,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    minWidth: 80,
    alignItems: 'center',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default ContactsListScreen;
