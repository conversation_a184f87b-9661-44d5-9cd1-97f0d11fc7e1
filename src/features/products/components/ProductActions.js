import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const ProductActions = ({
  selectedProducts,
  onAction,
  onClose
}) => {
  const { colors } = useTheme();

  const actions = [
    {
      id: 'export',
      title: 'Export Products',
      subtitle: 'Export selected products to CSV',
      icon: 'export',
      color: colors.primary,
      action: () => handleAction('export')
    },
    {
      id: 'update_stock',
      title: 'Update Stock',
      subtitle: 'Bulk update stock quantities',
      icon: 'package-variant',
      color: colors.secondary,
      action: () => handleAction('update_stock')
    },
    {
      id: 'update_price',
      title: 'Update Prices',
      subtitle: 'Bulk update product prices',
      icon: 'currency-usd',
      color: '#FF9500',
      action: () => handleAction('update_price')
    },
    {
      id: 'duplicate',
      title: 'Duplicate Products',
      subtitle: 'Create copies of selected products',
      icon: 'content-duplicate',
      color: colors.success,
      action: () => handleAction('duplicate')
    },
    {
      id: 'archive',
      title: 'Archive Products',
      subtitle: 'Move products to archive',
      icon: 'archive',
      color: '#8E8E93',
      action: () => handleAction('archive')
    },
    {
      id: 'delete',
      title: 'Delete Products',
      subtitle: 'Permanently delete products',
      icon: 'delete',
      color: colors.error,
      action: () => handleDeleteAction()
    }
  ];

  const handleAction = (actionType) => {
    onAction(actionType);
  };

  const handleDeleteAction = () => {
    Alert.alert(
      'Delete Products',
      `Are you sure you want to delete ${selectedProducts.length} product${selectedProducts.length !== 1 ? 's' : ''}? This action cannot be undone.`,
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => onAction('delete')
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={[styles.title, { color: colors.text }]}>
            Bulk Actions
          </Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            {selectedProducts.length} product{selectedProducts.length !== 1 ? 's' : ''} selected
          </Text>
        </View>
        <TouchableOpacity onPress={onClose}>
          <Icon name="close" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Actions Grid */}
      <View style={styles.actionsGrid}>
        {actions.map((action) => (
          <TouchableOpacity
            key={action.id}
            style={[
              styles.actionCard,
              { 
                backgroundColor: colors.surface,
                borderColor: colors.border
              }
            ]}
            onPress={action.action}
            activeOpacity={0.7}
          >
            <View style={[styles.actionIcon, { backgroundColor: action.color + '15' }]}>
              <Icon name={action.icon} size={24} color={action.color} />
            </View>
            <View style={styles.actionContent}>
              <Text style={[styles.actionTitle, { color: colors.text }]}>
                {action.title}
              </Text>
              <Text style={[styles.actionSubtitle, { color: colors.textSecondary }]}>
                {action.subtitle}
              </Text>
            </View>
            <Icon name="chevron-right" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        ))}
      </View>

      {/* Warning for destructive actions */}
      <View style={[styles.warning, { backgroundColor: colors.error + '10' }]}>
        <Icon name="alert" size={16} color={colors.error} />
        <Text style={[styles.warningText, { color: colors.error }]}>
          Some actions like delete and archive cannot be undone
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    fontWeight: '400',
  },
  actionsGrid: {
    paddingTop: 20,
    gap: 12,
  },
  actionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    gap: 12,
  },
  actionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  actionSubtitle: {
    fontSize: 13,
    lineHeight: 16,
  },
  warning: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 20,
    gap: 8,
  },
  warningText: {
    fontSize: 12,
    fontWeight: '500',
    flex: 1,
  },
});

export default ProductActions;
