import React, { useState } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const ProductSearch = ({
  searchQuery,
  onSearchChange,
  onFilterPress,
  placeholder = 'Search products...',
  autoFocus = false
}) => {
  const { colors } = useTheme();
  const [isFocused, setIsFocused] = useState(false);
  const [scaleValue] = useState(new Animated.Value(1));

  const handleFocus = () => {
    setIsFocused(true);
    Animated.spring(scaleValue, {
      toValue: 1.02,
      useNativeDriver: true,
    }).start();
  };

  const handleBlur = () => {
    setIsFocused(false);
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const clearSearch = () => {
    onSearchChange('');
  };

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.searchContainer,
          {
            backgroundColor: colors.surface,
            borderColor: isFocused ? colors.primary : colors.border,
            transform: [{ scale: scaleValue }]
          }
        ]}
      >
        {/* Search Icon */}
        <Icon 
          name="magnify" 
          size={20} 
          color={isFocused ? colors.primary : colors.textSecondary}
          style={styles.searchIcon}
        />

        {/* Text Input */}
        <TextInput
          style={[
            styles.textInput,
            { 
              color: colors.text,
              flex: 1
            }
          ]}
          placeholder={placeholder}
          placeholderTextColor={colors.textSecondary}
          value={searchQuery}
          onChangeText={onSearchChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          autoFocus={autoFocus}
          returnKeyType="search"
          clearButtonMode="never" // We'll handle clear button manually
        />

        {/* Clear Button */}
        {searchQuery.length > 0 && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={clearSearch}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Icon 
              name="close-circle" 
              size={18} 
              color={colors.textSecondary} 
            />
          </TouchableOpacity>
        )}

        {/* Filter Button */}
        <TouchableOpacity
          style={[
            styles.filterButton,
            { backgroundColor: colors.background }
          ]}
          onPress={onFilterPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Icon 
            name="tune" 
            size={20} 
            color={colors.primary} 
          />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    paddingHorizontal: 12,
    paddingVertical: 10,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  searchIcon: {
    marginRight: 8,
  },
  textInput: {
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 20,
    paddingVertical: 0, // Remove default padding
  },
  clearButton: {
    marginLeft: 8,
    padding: 2,
  },
  filterButton: {
    marginLeft: 8,
    padding: 6,
    borderRadius: 6,
  },
});

export default ProductSearch;
