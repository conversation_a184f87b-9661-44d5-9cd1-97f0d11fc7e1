import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const { width: screenWidth } = Dimensions.get('window');
const cardWidth = (screenWidth - 48) / 2; // Account for padding and gap

const ProductCard = ({
  product,
  viewMode = 'grid',
  onPress,
  onLongPress,
  onDetailsPress,
  isSelected = false,
  isSelectionMode = false,
  index
}) => {
  const { colors } = useTheme();

  const formatPrice = (price) => {
    if (!price) return '$0.00';
    return `$${price.toFixed(2)}`;
  };

  const getStockColor = (qty) => {
    if (qty <= 0) return colors.error;
    if (qty <= 10) return '#FF9500';
    return colors.success;
  };

  const getStockIcon = (qty) => {
    if (qty <= 0) return 'alert-circle';
    if (qty <= 10) return 'alert';
    return 'check-circle';
  };

  if (viewMode === 'list') {
    return (
      <TouchableOpacity
        style={[
          styles.listCard,
          { 
            backgroundColor: colors.surface,
            borderColor: isSelected ? colors.primary : colors.border
          },
          isSelected && { borderWidth: 2 }
        ]}
        onPress={onPress}
        onLongPress={onLongPress}
        activeOpacity={0.7}
      >
        {/* Selection Indicator */}
        {isSelectionMode && (
          <View style={[styles.selectionIndicator, { backgroundColor: colors.primary }]}>
            {isSelected && (
              <Icon name="check" size={16} color={colors.onPrimary} />
            )}
          </View>
        )}

        {/* Product Image */}
        <View style={styles.listImageContainer}>
          {product.image_128 ? (
            <Image
              source={{ uri: `data:image/png;base64,${product.image_128}` }}
              style={styles.listImage}
              resizeMode="cover"
            />
          ) : (
            <View style={[styles.placeholderImage, { backgroundColor: colors.border }]}>
              <Icon name="package-variant" size={24} color={colors.textSecondary} />
            </View>
          )}
        </View>

        {/* Product Info */}
        <View style={styles.listContent}>
          <View style={styles.listHeader}>
            <Text 
              style={[styles.listProductName, { color: colors.text }]}
              numberOfLines={2}
            >
              {product.name}
            </Text>
            <TouchableOpacity onPress={onDetailsPress}>
              <Icon name="dots-vertical" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {product.default_code && (
            <Text style={[styles.productCode, { color: colors.textSecondary }]}>
              {product.default_code}
            </Text>
          )}

          <View style={styles.listFooter}>
            <View style={styles.priceContainer}>
              <Text style={[styles.price, { color: colors.primary }]}>
                {formatPrice(product.list_price)}
              </Text>
            </View>

            <View style={styles.stockContainer}>
              <Icon 
                name={getStockIcon(product.qty_available)} 
                size={16} 
                color={getStockColor(product.qty_available)} 
              />
              <Text style={[styles.stockText, { color: getStockColor(product.qty_available) }]}>
                {product.qty_available || 0}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  }

  // Grid view
  return (
    <TouchableOpacity
      style={[
        styles.gridCard,
        { 
          backgroundColor: colors.surface,
          borderColor: isSelected ? colors.primary : colors.border,
          width: cardWidth
        },
        isSelected && { borderWidth: 2 }
      ]}
      onPress={onPress}
      onLongPress={onLongPress}
      activeOpacity={0.7}
    >
      {/* Selection Indicator */}
      {isSelectionMode && (
        <View style={[styles.selectionIndicator, { backgroundColor: colors.primary }]}>
          {isSelected && (
            <Icon name="check" size={16} color={colors.onPrimary} />
          )}
        </View>
      )}

      {/* Product Image */}
      <View style={styles.imageContainer}>
        {product.image_128 ? (
          <Image
            source={{ uri: `data:image/png;base64,${product.image_128}` }}
            style={styles.productImage}
            resizeMode="cover"
          />
        ) : (
          <View style={[styles.placeholderImage, { backgroundColor: colors.border }]}>
            <Icon name="package-variant" size={32} color={colors.textSecondary} />
          </View>
        )}

        {/* Stock Badge */}
        <View style={[styles.stockBadge, { backgroundColor: getStockColor(product.qty_available) }]}>
          <Text style={[styles.stockBadgeText, { color: colors.onPrimary }]}>
            {product.qty_available || 0}
          </Text>
        </View>

        {/* Details Button */}
        <TouchableOpacity 
          style={[styles.detailsButton, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
          onPress={onDetailsPress}
        >
          <Icon name="dots-vertical" size={16} color="white" />
        </TouchableOpacity>
      </View>

      {/* Product Info */}
      <View style={styles.cardContent}>
        <Text 
          style={[styles.productName, { color: colors.text }]}
          numberOfLines={2}
        >
          {product.name}
        </Text>

        {product.default_code && (
          <Text style={[styles.productCode, { color: colors.textSecondary }]}>
            {product.default_code}
          </Text>
        )}

        {product.categ_id && (
          <Text style={[styles.category, { color: colors.textSecondary }]}>
            {product.categ_id[1]}
          </Text>
        )}

        <View style={styles.cardFooter}>
          <Text style={[styles.price, { color: colors.primary }]}>
            {formatPrice(product.list_price)}
          </Text>
          {product.standard_price && product.standard_price !== product.list_price && (
            <Text style={[styles.costPrice, { color: colors.textSecondary }]}>
              Cost: {formatPrice(product.standard_price)}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  // Grid Card Styles
  gridCard: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    marginHorizontal: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  imageContainer: {
    position: 'relative',
    height: 140,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: 'hidden',
  },
  productImage: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  stockBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 24,
  },
  stockBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    textAlign: 'center',
  },
  detailsButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardContent: {
    padding: 12,
    gap: 4,
  },
  productName: {
    fontSize: 14,
    fontWeight: '600',
    lineHeight: 18,
  },
  productCode: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  category: {
    fontSize: 11,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  cardFooter: {
    marginTop: 4,
    gap: 2,
  },
  price: {
    fontSize: 16,
    fontWeight: '700',
  },
  costPrice: {
    fontSize: 11,
  },

  // List Card Styles
  listCard: {
    flexDirection: 'row',
    borderRadius: 12,
    borderWidth: 1,
    marginVertical: 4,
    marginHorizontal: 16,
    padding: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  listImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 8,
    overflow: 'hidden',
    marginRight: 12,
  },
  listImage: {
    width: '100%',
    height: '100%',
  },
  listContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  listProductName: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 20,
    flex: 1,
    marginRight: 8,
  },
  listFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  priceContainer: {
    flex: 1,
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  stockText: {
    fontSize: 12,
    fontWeight: '600',
  },

  // Selection Indicator
  selectionIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    borderWidth: 2,
    borderColor: 'white',
  },
});

export default ProductCard;
