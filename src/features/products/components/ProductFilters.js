import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const ProductFilters = ({
  filters,
  categories,
  onApplyFilters,
  onClose
}) => {
  const { colors } = useTheme();
  const [localFilters, setLocalFilters] = useState({
    category: filters.category,
    priceRange: { ...filters.priceRange },
    inStock: filters.inStock,
    onSale: filters.onSale,
    sortBy: filters.sortBy
  });

  const sortOptions = [
    { value: 'name', label: 'Name (A-Z)', icon: 'sort-alphabetical-ascending' },
    { value: 'price_asc', label: 'Price (Low to High)', icon: 'sort-numeric-ascending' },
    { value: 'price_desc', label: 'Price (High to Low)', icon: 'sort-numeric-descending' },
    { value: 'stock', label: 'Stock Level', icon: 'sort-numeric-descending' }
  ];

  const updateFilter = (key, value) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const updatePriceRange = (type, value) => {
    setLocalFilters(prev => ({
      ...prev,
      priceRange: {
        ...prev.priceRange,
        [type]: value ? parseFloat(value) : null
      }
    }));
  };

  const clearAllFilters = () => {
    setLocalFilters({
      category: null,
      priceRange: { min: null, max: null },
      inStock: false,
      onSale: false,
      sortBy: 'name'
    });
  };

  const applyFilters = () => {
    onApplyFilters(localFilters);
  };

  const hasActiveFilters = () => {
    return localFilters.category ||
           localFilters.priceRange.min ||
           localFilters.priceRange.max ||
           localFilters.inStock ||
           localFilters.onSale ||
           localFilters.sortBy !== 'name';
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          Filter & Sort
        </Text>
        <View style={styles.headerActions}>
          {hasActiveFilters() && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={clearAllFilters}
            >
              <Text style={[styles.clearText, { color: colors.primary }]}>
                Clear All
              </Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity onPress={onClose}>
            <Icon name="close" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Sort Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Sort By
          </Text>
          <View style={styles.sortOptions}>
            {sortOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.sortOption,
                  {
                    backgroundColor: localFilters.sortBy === option.value 
                      ? colors.primary + '15' 
                      : colors.surface,
                    borderColor: localFilters.sortBy === option.value 
                      ? colors.primary 
                      : colors.border
                  }
                ]}
                onPress={() => updateFilter('sortBy', option.value)}
              >
                <Icon 
                  name={option.icon} 
                  size={20} 
                  color={localFilters.sortBy === option.value ? colors.primary : colors.text} 
                />
                <Text 
                  style={[
                    styles.sortOptionText,
                    { color: localFilters.sortBy === option.value ? colors.primary : colors.text }
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Category Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Category
          </Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.categoryScrollView}
          >
            <TouchableOpacity
              style={[
                styles.categoryChip,
                {
                  backgroundColor: !localFilters.category 
                    ? colors.primary 
                    : colors.surface,
                  borderColor: !localFilters.category 
                    ? colors.primary 
                    : colors.border
                }
              ]}
              onPress={() => updateFilter('category', null)}
            >
              <Text 
                style={[
                  styles.categoryChipText,
                  { color: !localFilters.category ? colors.onPrimary : colors.text }
                ]}
              >
                All Categories
              </Text>
            </TouchableOpacity>
            
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryChip,
                  {
                    backgroundColor: localFilters.category === category.id 
                      ? colors.primary 
                      : colors.surface,
                    borderColor: localFilters.category === category.id 
                      ? colors.primary 
                      : colors.border
                  }
                ]}
                onPress={() => updateFilter('category', category.id)}
              >
                <Text 
                  style={[
                    styles.categoryChipText,
                    { color: localFilters.category === category.id ? colors.onPrimary : colors.text }
                  ]}
                >
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Price Range Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Price Range
          </Text>
          <View style={styles.priceInputContainer}>
            <View style={styles.priceInputWrapper}>
              <Text style={[styles.priceLabel, { color: colors.textSecondary }]}>
                Min Price
              </Text>
              <View style={[styles.priceInputField, { borderColor: colors.border }]}>
                <Text style={[styles.currencySymbol, { color: colors.textSecondary }]}>
                  $
                </Text>
                <TextInput
                  style={[styles.priceInput, { color: colors.text }]}
                  placeholder="0.00"
                  placeholderTextColor={colors.textSecondary}
                  value={localFilters.priceRange.min?.toString() || ''}
                  onChangeText={(text) => updatePriceRange('min', text)}
                  keyboardType="decimal-pad"
                />
              </View>
            </View>

            <View style={styles.priceInputWrapper}>
              <Text style={[styles.priceLabel, { color: colors.textSecondary }]}>
                Max Price
              </Text>
              <View style={[styles.priceInputField, { borderColor: colors.border }]}>
                <Text style={[styles.currencySymbol, { color: colors.textSecondary }]}>
                  $
                </Text>
                <TextInput
                  style={[styles.priceInput, { color: colors.text }]}
                  placeholder="999.99"
                  placeholderTextColor={colors.textSecondary}
                  value={localFilters.priceRange.max?.toString() || ''}
                  onChangeText={(text) => updatePriceRange('max', text)}
                  keyboardType="decimal-pad"
                />
              </View>
            </View>
          </View>
        </View>

        {/* Quick Filters Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Quick Filters
          </Text>
          
          <View style={styles.switchRow}>
            <View style={styles.switchInfo}>
              <Icon name="package-check" size={20} color={colors.success} />
              <Text style={[styles.switchLabel, { color: colors.text }]}>
                In Stock Only
              </Text>
            </View>
            <Switch
              value={localFilters.inStock}
              onValueChange={(value) => updateFilter('inStock', value)}
              trackColor={{ false: colors.border, true: colors.primary + '40' }}
              thumbColor={localFilters.inStock ? colors.primary : colors.textSecondary}
            />
          </View>

          <View style={styles.switchRow}>
            <View style={styles.switchInfo}>
              <Icon name="tag-percent" size={20} color={colors.secondary} />
              <Text style={[styles.switchLabel, { color: colors.text }]}>
                On Sale
              </Text>
            </View>
            <Switch
              value={localFilters.onSale}
              onValueChange={(value) => updateFilter('onSale', value)}
              trackColor={{ false: colors.border, true: colors.secondary + '40' }}
              thumbColor={localFilters.onSale ? colors.secondary : colors.textSecondary}
            />
          </View>
        </View>
      </ScrollView>

      {/* Apply Button */}
      <View style={[styles.footer, { borderTopColor: colors.border }]}>
        <TouchableOpacity
          style={[styles.applyButton, { backgroundColor: colors.primary }]}
          onPress={applyFilters}
        >
          <Text style={[styles.applyButtonText, { color: colors.onPrimary }]}>
            Apply Filters
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  clearButton: {
    paddingVertical: 4,
  },
  clearText: {
    fontSize: 14,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingTop: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  sortOptions: {
    gap: 8,
  },
  sortOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 12,
  },
  sortOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  categoryScrollView: {
    flexGrow: 0,
  },
  categoryChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
    minHeight: 36,
    justifyContent: 'center',
  },
  categoryChipText: {
    fontSize: 13,
    fontWeight: '500',
  },
  priceInputContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  priceInputWrapper: {
    flex: 1,
  },
  priceLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 6,
  },
  priceInputField: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '500',
    marginRight: 4,
  },
  priceInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '400',
    paddingVertical: 0,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  switchInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  switchLabel: {
    fontSize: 15,
    fontWeight: '500',
  },
  footer: {
    borderTopWidth: 1,
    paddingTop: 16,
    paddingBottom: 8,
  },
  applyButton: {
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProductFilters;
