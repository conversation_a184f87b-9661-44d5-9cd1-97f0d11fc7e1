import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import ProductsListScreen from './screens/ProductsListScreen';
import ProductDetailScreen from './screens/ProductDetailScreen';
import ProductEditScreen from './screens/ProductEditScreen';
import { useTheme } from '../../contexts/ThemeContext';

const Stack = createStackNavigator();

const ProductsNavigator = () => {
  const { colors } = useTheme();

  return (
    <Stack.Navigator
      initialRouteName="ProductsList"
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: colors.onPrimary,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        headerBackTitleVisible: false,
      }}
    >
      <Stack.Screen
        name="ProductsList"
        component={ProductsListScreen}
        options={{
          title: 'Products',
          headerLargeTitle: true,
        }}
      />
      <Stack.Screen
        name="ProductDetail"
        component={ProductDetailScreen}
        options={({ route }) => ({
          title: route.params?.productName || 'Product Details',
        })}
      />
      <Stack.Screen
        name="ProductEdit"
        component={ProductEditScreen}
        options={({ route }) => ({
          title: route.params?.isEditing ? 'Edit Product' : 'New Product',
        })}
      />
    </Stack.Navigator>
  );
};

export default ProductsNavigator;
