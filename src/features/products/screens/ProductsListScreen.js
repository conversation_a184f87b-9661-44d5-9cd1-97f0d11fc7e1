import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
  SafeAreaView,
  TextInput,
  Image,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme } from '../../../contexts/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import BottomSheet, { BottomSheetView, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { productsAPI } from '../../../api/models/productsApi';
import ProductCard from '../components/ProductCard';
import ProductFilters from '../components/ProductFilters';
import ProductSearch from '../components/ProductSearch';
import ProductActions from '../components/ProductActions';

const { width: screenWidth } = Dimensions.get('window');

const ProductsListScreen = () => {
  const navigation = useNavigation();
  const { colors } = useTheme();
  
  // Bottom Sheet refs
  const bottomSheetRef = useRef(null);
  const filterBottomSheetRef = useRef(null);
  const actionBottomSheetRef = useRef(null);
  
  // Bottom Sheet snap points
  const snapPoints = useMemo(() => ['25%', '50%', '90%'], []);
  const filterSnapPoints = useMemo(() => ['40%', '70%'], []);
  const actionSnapPoints = useMemo(() => ['30%', '50%'], []);

  // State management
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  
  // Filter state
  const [filters, setFilters] = useState({
    category: null,
    priceRange: { min: null, max: null },
    inStock: false,
    onSale: false,
    sortBy: 'name'
  });
  
  // View state
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Reload when screen focuses
  useFocusEffect(
    useCallback(() => {
      if (products.length === 0) {
        loadInitialData();
      }
    }, [])
  );

  // Search effect
  useEffect(() => {
    if (searchQuery.length > 2 || searchQuery.length === 0) {
      searchProducts();
    }
  }, [searchQuery, filters]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadProducts(true),
        loadCategories()
      ]);
    } catch (error) {
      console.error('Error loading initial data:', error);
      Alert.alert('Error', 'Failed to load products. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const categoriesData = await productsAPI.getCategories();
      setCategories(categoriesData || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const loadProducts = async (reset = false) => {
    try {
      if (reset) {
        setCurrentPage(0);
        setHasMore(true);
      }

      const offset = reset ? 0 : currentPage * 20;
      const domain = buildDomain();
      
      const productsData = await productsAPI.getProducts({
        domain,
        limit: 20,
        offset,
        order: getSortOrder()
      });

      if (productsData && productsData.length > 0) {
        if (reset) {
          setProducts(productsData);
        } else {
          setProducts(prev => [...prev, ...productsData]);
        }
        
        setHasMore(productsData.length === 20);
        setCurrentPage(prev => reset ? 1 : prev + 1);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error loading products:', error);
      throw error;
    }
  };

  const searchProducts = async () => {
    try {
      setLoading(true);
      let productsData;
      
      if (searchQuery.trim()) {
        productsData = await productsAPI.searchProducts(searchQuery, {
          limit: 50
        });
      } else {
        productsData = await productsAPI.getProducts({
          domain: buildDomain(),
          limit: 20,
          order: getSortOrder()
        });
      }
      
      setProducts(productsData || []);
      setCurrentPage(1);
      setHasMore(searchQuery.trim() ? false : true);
    } catch (error) {
      console.error('Error searching products:', error);
    } finally {
      setLoading(false);
    }
  };

  const buildDomain = () => {
    let domain = [['sale_ok', '=', true]];
    
    if (filters.category) {
      domain.push(['categ_id', 'child_of', filters.category]);
    }
    
    if (filters.inStock) {
      domain.push(['qty_available', '>', 0]);
    }
    
    if (filters.priceRange.min !== null) {
      domain.push(['list_price', '>=', filters.priceRange.min]);
    }
    
    if (filters.priceRange.max !== null) {
      domain.push(['list_price', '<=', filters.priceRange.max]);
    }
    
    return domain;
  };

  const getSortOrder = () => {
    switch (filters.sortBy) {
      case 'price_asc':
        return 'list_price asc';
      case 'price_desc':
        return 'list_price desc';
      case 'stock':
        return 'qty_available desc';
      case 'name':
      default:
        return 'name asc';
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadProducts(true);
    } catch (error) {
      // Error already handled in loadProducts
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = async () => {
    if (!hasMore || loadingMore || searchQuery.trim()) return;
    
    try {
      setLoadingMore(true);
      await loadProducts(false);
    } catch (error) {
      // Error already handled in loadProducts
    } finally {
      setLoadingMore(false);
    }
  };

  const handleProductPress = (product) => {
    if (isSelectionMode) {
      toggleProductSelection(product);
    } else {
      navigation.navigate('ProductDetail', { 
        productId: product.id,
        productName: product.name 
      });
    }
  };

  const handleProductLongPress = (product) => {
    if (!isSelectionMode) {
      setIsSelectionMode(true);
      setSelectedProducts([product.id]);
    }
  };

  const toggleProductSelection = (product) => {
    setSelectedProducts(prev => {
      if (prev.includes(product.id)) {
        const newSelection = prev.filter(id => id !== product.id);
        if (newSelection.length === 0) {
          setIsSelectionMode(false);
        }
        return newSelection;
      } else {
        return [...prev, product.id];
      }
    });
  };

  const handleBulkAction = (action) => {
    setSelectedProduct({ ids: selectedProducts, action });
    actionBottomSheetRef.current?.expand();
  };

  const clearSelection = () => {
    setSelectedProducts([]);
    setIsSelectionMode(false);
  };

  const showProductDetails = (product) => {
    setSelectedProduct(product);
    bottomSheetRef.current?.expand();
  };

  const showFilters = () => {
    filterBottomSheetRef.current?.expand();
  };

  const applyFilters = (newFilters) => {
    setFilters(newFilters);
    filterBottomSheetRef.current?.close();
  };

  const renderProductItem = ({ item, index }) => (
    <ProductCard
      product={item}
      viewMode={viewMode}
      onPress={() => handleProductPress(item)}
      onLongPress={() => handleProductLongPress(item)}
      onDetailsPress={() => showProductDetails(item)}
      isSelected={selectedProducts.includes(item.id)}
      isSelectionMode={isSelectionMode}
      index={index}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Search Bar */}
      <ProductSearch
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        onFilterPress={showFilters}
        placeholder="Search products..."
      />
      
      {/* View Controls */}
      <View style={[styles.viewControls, { backgroundColor: colors.surface }]}>
        <View style={styles.viewModeToggle}>
          <TouchableOpacity
            style={[
              styles.viewModeButton,
              viewMode === 'grid' && { backgroundColor: colors.primary }
            ]}
            onPress={() => setViewMode('grid')}
          >
            <Icon 
              name="view-grid" 
              size={20} 
              color={viewMode === 'grid' ? colors.onPrimary : colors.text} 
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.viewModeButton,
              viewMode === 'list' && { backgroundColor: colors.primary }
            ]}
            onPress={() => setViewMode('list')}
          >
            <Icon 
              name="view-list" 
              size={20} 
              color={viewMode === 'list' ? colors.onPrimary : colors.text} 
            />
          </TouchableOpacity>
        </View>
        
        {/* Results Count */}
        <Text style={[styles.resultsCount, { color: colors.textSecondary }]}>
          {products.length} product{products.length !== 1 ? 's' : ''}
        </Text>
      </View>
      
      {/* Selection Mode Header */}
      {isSelectionMode && (
        <View style={[styles.selectionHeader, { backgroundColor: colors.primary }]}>
          <View style={styles.selectionInfo}>
            <TouchableOpacity onPress={clearSelection}>
              <Icon name="close" size={24} color={colors.onPrimary} />
            </TouchableOpacity>
            <Text style={[styles.selectionText, { color: colors.onPrimary }]}>
              {selectedProducts.length} selected
            </Text>
          </View>
          <View style={styles.selectionActions}>
            <TouchableOpacity
              style={styles.selectionAction}
              onPress={() => handleBulkAction('export')}
            >
              <Icon name="export" size={20} color={colors.onPrimary} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.selectionAction}
              onPress={() => handleBulkAction('delete')}
            >
              <Icon name="delete" size={20} color={colors.onPrimary} />
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );

  const renderFooter = () => {
    if (!loadingMore) return null;
    
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading more products...
        </Text>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="package-variant" size={64} color={colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        {searchQuery ? 'No products found' : 'No products available'}
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        {searchQuery 
          ? `Try adjusting your search or filters`
          : 'Products will appear here once they are added'
        }
      </Text>
      {searchQuery && (
        <TouchableOpacity
          style={[styles.clearSearchButton, { backgroundColor: colors.primary }]}
          onPress={() => setSearchQuery('')}
        >
          <Text style={[styles.clearSearchText, { color: colors.onPrimary }]}>
            Clear Search
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (loading && products.length === 0) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading products...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <FlatList
        data={products}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderProductItem}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.3}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode} // Force re-render when view mode changes
        contentContainerStyle={products.length === 0 ? styles.emptyContainer : undefined}
        showsVerticalScrollIndicator={false}
      />

      {/* Floating Action Button */}
      <TouchableOpacity
        style={[styles.fab, { backgroundColor: colors.primary }]}
        onPress={() => navigation.navigate('ProductEdit', { isEditing: false })}
      >
        <Icon name="plus" size={28} color={colors.onPrimary} />
      </TouchableOpacity>

      {/* Product Details Bottom Sheet */}
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: colors.surface }}
        handleIndicatorStyle={{ backgroundColor: colors.border }}
      >
        <BottomSheetScrollView contentContainerStyle={styles.bottomSheetContent}>
          {selectedProduct && (
            <ProductDetails
              product={selectedProduct}
              onClose={() => bottomSheetRef.current?.close()}
              onEdit={() => {
                bottomSheetRef.current?.close();
                navigation.navigate('ProductEdit', { 
                  productId: selectedProduct.id,
                  isEditing: true 
                });
              }}
            />
          )}
        </BottomSheetScrollView>
      </BottomSheet>

      {/* Filters Bottom Sheet */}
      <BottomSheet
        ref={filterBottomSheetRef}
        index={-1}
        snapPoints={filterSnapPoints}
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: colors.surface }}
        handleIndicatorStyle={{ backgroundColor: colors.border }}
      >
        <BottomSheetView style={styles.bottomSheetContent}>
          <ProductFilters
            filters={filters}
            categories={categories}
            onApplyFilters={applyFilters}
            onClose={() => filterBottomSheetRef.current?.close()}
          />
        </BottomSheetView>
      </BottomSheet>

      {/* Actions Bottom Sheet */}
      <BottomSheet
        ref={actionBottomSheetRef}
        index={-1}
        snapPoints={actionSnapPoints}
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: colors.surface }}
        handleIndicatorStyle={{ backgroundColor: colors.border }}
      >
        <BottomSheetView style={styles.bottomSheetContent}>
          <ProductActions
            selectedProducts={selectedProducts}
            onAction={(action) => {
              actionBottomSheetRef.current?.close();
              // Handle bulk actions here
              console.log('Bulk action:', action, selectedProducts);
            }}
            onClose={() => actionBottomSheetRef.current?.close()}
          />
        </BottomSheetView>
      </BottomSheet>
    </SafeAreaView>
  );
};

// Product Details Component for Bottom Sheet
const ProductDetails = ({ product, onClose, onEdit }) => {
  const { colors } = useTheme();

  return (
    <View style={styles.productDetails}>
      <View style={styles.detailsHeader}>
        <Text style={[styles.detailsTitle, { color: colors.text }]}>
          Product Details
        </Text>
        <TouchableOpacity onPress={onClose}>
          <Icon name="close" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      {product.image_128 && (
        <Image
          source={{ uri: `data:image/png;base64,${product.image_128}` }}
          style={styles.detailsImage}
          resizeMode="contain"
        />
      )}

      <Text style={[styles.detailsProductName, { color: colors.text }]}>
        {product.name}
      </Text>

      {product.default_code && (
        <Text style={[styles.detailsCode, { color: colors.textSecondary }]}>
          Code: {product.default_code}
        </Text>
      )}

      <View style={styles.detailsRow}>
        <Text style={[styles.detailsLabel, { color: colors.textSecondary }]}>
          Price:
        </Text>
        <Text style={[styles.detailsValue, { color: colors.primary }]}>
          ${product.list_price?.toFixed(2) || '0.00'}
        </Text>
      </View>

      <View style={styles.detailsRow}>
        <Text style={[styles.detailsLabel, { color: colors.textSecondary }]}>
          Stock:
        </Text>
        <Text style={[styles.detailsValue, { color: colors.text }]}>
          {product.qty_available || 0} units
        </Text>
      </View>

      {product.categ_id && (
        <View style={styles.detailsRow}>
          <Text style={[styles.detailsLabel, { color: colors.textSecondary }]}>
            Category:
          </Text>
          <Text style={[styles.detailsValue, { color: colors.text }]}>
            {product.categ_id[1]}
          </Text>
        </View>
      )}

      <TouchableOpacity
        style={[styles.editButton, { backgroundColor: colors.primary }]}
        onPress={onEdit}
      >
        <Icon name="pencil" size={20} color={colors.onPrimary} />
        <Text style={[styles.editButtonText, { color: colors.onPrimary }]}>
          Edit Product
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    paddingBottom: 16,
  },
  viewControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 8,
  },
  viewModeToggle: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderRadius: 6,
    padding: 2,
  },
  viewModeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  resultsCount: {
    fontSize: 14,
    fontWeight: '500',
  },
  selectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 8,
  },
  selectionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  selectionText: {
    fontSize: 16,
    fontWeight: '600',
  },
  selectionActions: {
    flexDirection: 'row',
    gap: 16,
  },
  selectionAction: {
    padding: 4,
  },
  footerLoader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    gap: 8,
  },
  emptyContainer: {
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 24,
  },
  clearSearchButton: {
    marginTop: 24,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  clearSearchText: {
    fontSize: 16,
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  bottomSheetContent: {
    padding: 16,
  },
  productDetails: {
    gap: 16,
  },
  detailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailsTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  detailsImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
  },
  detailsProductName: {
    fontSize: 18,
    fontWeight: '600',
  },
  detailsCode: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailsLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  detailsValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  editButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
    marginTop: 8,
  },
  editButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProductsListScreen;
