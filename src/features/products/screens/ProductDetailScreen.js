import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
  SafeAreaView,
  Image,
  Dimensions,
  Share
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../../contexts/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import BottomSheet, { BottomSheetView, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { productsAPI } from '../../../api/models/productsApi';

const { width: screenWidth } = Dimensions.get('window');

const ProductDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { colors } = useTheme();
  const { productId } = route.params;

  // Bottom Sheet refs
  const stockBottomSheetRef = useRef(null);
  const imageBottomSheetRef = useRef(null);

  // State
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [stockMovements, setStockMovements] = useState([]);
  const [loadingStock, setLoadingStock] = useState(false);

  useEffect(() => {
    loadProductDetails();
  }, [productId]);

  const loadProductDetails = async () => {
    try {
      setLoading(true);
      const productData = await productsAPI.getProduct(productId);
      
      if (productData) {
        setProduct(productData);
        navigation.setOptions({ title: productData.name });
      } else {
        Alert.alert('Error', 'Product not found');
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error loading product details:', error);
      Alert.alert('Error', 'Failed to load product details');
    } finally {
      setLoading(false);
    }
  };

  const loadStockMovements = async () => {
    try {
      setLoadingStock(true);
      const movements = await productsAPI.getStockMovements(productId);
      setStockMovements(movements || []);
    } catch (error) {
      console.error('Error loading stock movements:', error);
    } finally {
      setLoadingStock(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadProductDetails();
    setRefreshing(false);
  };

  const handleEdit = () => {
    navigation.navigate('ProductEdit', {
      productId: product.id,
      isEditing: true
    });
  };

  const handleShare = async () => {
    try {
      const shareContent = {
        title: product.name,
        message: `${product.name}\nPrice: $${product.list_price?.toFixed(2) || '0.00'}\nCode: ${product.default_code || 'N/A'}`,
      };
      
      await Share.share(shareContent);
    } catch (error) {
      console.error('Error sharing product:', error);
    }
  };

  const showStockDetails = () => {
    loadStockMovements();
    stockBottomSheetRef.current?.expand();
  };

  const showImageViewer = () => {
    imageBottomSheetRef.current?.expand();
  };

  const formatPrice = (price) => {
    if (!price) return '$0.00';
    return `$${price.toFixed(2)}`;
  };

  const getStockColor = (qty) => {
    if (qty <= 0) return colors.error;
    if (qty <= 10) return '#FF9500';
    return colors.success;
  };

  const getStockStatus = (qty) => {
    if (qty <= 0) return 'Out of Stock';
    if (qty <= 10) return 'Low Stock';
    return 'In Stock';
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading product details...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!product) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <Icon name="package-variant" size={64} color={colors.textSecondary} />
          <Text style={[styles.errorText, { color: colors.text }]}>
            Product not found
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* Product Image */}
        <View style={styles.imageSection}>
          <TouchableOpacity
            style={styles.imageContainer}
            onPress={showImageViewer}
            activeOpacity={0.9}
          >
            {product.image_1920 ? (
              <Image
                source={{ uri: `data:image/png;base64,${product.image_1920}` }}
                style={styles.productImage}
                resizeMode="contain"
              />
            ) : (
              <View style={[styles.placeholderImage, { backgroundColor: colors.border }]}>
                <Icon name="package-variant" size={80} color={colors.textSecondary} />
                <Text style={[styles.placeholderText, { color: colors.textSecondary }]}>
                  No Image
                </Text>
              </View>
            )}
            
            {/* Image Overlay */}
            <View style={styles.imageOverlay}>
              <TouchableOpacity
                style={[styles.imageAction, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
                onPress={showImageViewer}
              >
                <Icon name="magnify-plus" size={20} color="white" />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </View>

        {/* Product Info */}
        <View style={[styles.infoSection, { backgroundColor: colors.surface }]}>
          {/* Header */}
          <View style={styles.productHeader}>
            <View style={styles.headerLeft}>
              <Text style={[styles.productName, { color: colors.text }]}>
                {product.name}
              </Text>
              {product.default_code && (
                <Text style={[styles.productCode, { color: colors.textSecondary }]}>
                  Code: {product.default_code}
                </Text>
              )}
            </View>
            <View style={styles.headerActions}>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: colors.background }]}
                onPress={handleShare}
              >
                <Icon name="share-variant" size={20} color={colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: colors.primary }]}
                onPress={handleEdit}
              >
                <Icon name="pencil" size={20} color={colors.onPrimary} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Price Section */}
          <View style={styles.priceSection}>
            <View style={styles.priceRow}>
              <Text style={[styles.priceLabel, { color: colors.textSecondary }]}>
                Sale Price
              </Text>
              <Text style={[styles.price, { color: colors.primary }]}>
                {formatPrice(product.list_price)}
              </Text>
            </View>
            
            {product.standard_price && product.standard_price !== product.list_price && (
              <View style={styles.priceRow}>
                <Text style={[styles.priceLabel, { color: colors.textSecondary }]}>
                  Cost Price
                </Text>
                <Text style={[styles.costPrice, { color: colors.textSecondary }]}>
                  {formatPrice(product.standard_price)}
                </Text>
              </View>
            )}
          </View>

          {/* Stock Section */}
          <TouchableOpacity
            style={styles.stockSection}
            onPress={showStockDetails}
          >
            <View style={styles.stockHeader}>
              <View style={styles.stockInfo}>
                <Icon 
                  name="package-variant" 
                  size={20} 
                  color={getStockColor(product.qty_available)} 
                />
                <Text style={[styles.stockLabel, { color: colors.text }]}>
                  Stock Level
                </Text>
              </View>
              <Icon name="chevron-right" size={20} color={colors.textSecondary} />
            </View>
            
            <View style={styles.stockDetails}>
              <View style={styles.stockItem}>
                <Text style={[styles.stockValue, { color: getStockColor(product.qty_available) }]}>
                  {product.qty_available || 0}
                </Text>
                <Text style={[styles.stockSubtext, { color: colors.textSecondary }]}>
                  On Hand
                </Text>
              </View>
              
              <View style={styles.stockItem}>
                <Text style={[styles.stockValue, { color: colors.text }]}>
                  {product.virtual_available || 0}
                </Text>
                <Text style={[styles.stockSubtext, { color: colors.textSecondary }]}>
                  Available
                </Text>
              </View>
              
              <View style={styles.stockItem}>
                <View style={[styles.statusBadge, { backgroundColor: getStockColor(product.qty_available) + '20' }]}>
                  <Text style={[styles.statusText, { color: getStockColor(product.qty_available) }]}>
                    {getStockStatus(product.qty_available)}
                  </Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>

          {/* Details Grid */}
          <View style={styles.detailsGrid}>
            {/* Category */}
            {product.categ_id && (
              <View style={styles.detailItem}>
                <Icon name="tag" size={16} color={colors.textSecondary} />
                <View style={styles.detailContent}>
                  <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                    Category
                  </Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>
                    {product.categ_id[1]}
                  </Text>
                </View>
              </View>
            )}

            {/* Brand */}
            {product.product_brand_id && (
              <View style={styles.detailItem}>
                <Icon name="medal" size={16} color={colors.textSecondary} />
                <View style={styles.detailContent}>
                  <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                    Brand
                  </Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>
                    {product.product_brand_id[1]}
                  </Text>
                </View>
              </View>
            )}

            {/* Barcode */}
            {product.barcode && (
              <View style={styles.detailItem}>
                <Icon name="barcode" size={16} color={colors.textSecondary} />
                <View style={styles.detailContent}>
                  <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                    Barcode
                  </Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>
                    {product.barcode}
                  </Text>
                </View>
              </View>
            )}

            {/* Unit of Measure */}
            {product.uom_id && (
              <View style={styles.detailItem}>
                <Icon name="ruler" size={16} color={colors.textSecondary} />
                <View style={styles.detailContent}>
                  <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                    Unit of Measure
                  </Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>
                    {product.uom_id[1]}
                  </Text>
                </View>
              </View>
            )}

            {/* Weight */}
            {product.weight > 0 && (
              <View style={styles.detailItem}>
                <Icon name="weight" size={16} color={colors.textSecondary} />
                <View style={styles.detailContent}>
                  <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                    Weight
                  </Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>
                    {product.weight} kg
                  </Text>
                </View>
              </View>
            )}

            {/* Volume */}
            {product.volume > 0 && (
              <View style={styles.detailItem}>
                <Icon name="cube" size={16} color={colors.textSecondary} />
                <View style={styles.detailContent}>
                  <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                    Volume
                  </Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>
                    {product.volume} m³
                  </Text>
                </View>
              </View>
            )}
          </View>

          {/* Description */}
          {product.description_sale && (
            <View style={styles.descriptionSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Description
              </Text>
              <Text style={[styles.description, { color: colors.textSecondary }]}>
                {product.description_sale}
              </Text>
            </View>
          )}

          {/* Product Type Info */}
          <View style={styles.typeSection}>
            <View style={styles.typeRow}>
              <Icon 
                name={product.sale_ok ? "check-circle" : "close-circle"} 
                size={16} 
                color={product.sale_ok ? colors.success : colors.textSecondary} 
              />
              <Text style={[styles.typeText, { color: colors.text }]}>
                Can be Sold
              </Text>
            </View>
            
            <View style={styles.typeRow}>
              <Icon 
                name={product.purchase_ok ? "check-circle" : "close-circle"} 
                size={16} 
                color={product.purchase_ok ? colors.success : colors.textSecondary} 
              />
              <Text style={[styles.typeText, { color: colors.text }]}>
                Can be Purchased
              </Text>
            </View>
            
            <View style={styles.typeRow}>
              <Icon name="package-variant" size={16} color={colors.textSecondary} />
              <Text style={[styles.typeText, { color: colors.text }]}>
                Product Type: {product.type || 'Consumable'}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Stock Movements Bottom Sheet */}
      <BottomSheet
        ref={stockBottomSheetRef}
        index={-1}
        snapPoints={['50%', '90%']}
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: colors.surface }}
        handleIndicatorStyle={{ backgroundColor: colors.border }}
      >
        <BottomSheetView style={styles.bottomSheetHeader}>
          <Text style={[styles.bottomSheetTitle, { color: colors.text }]}>
            Stock Movements
          </Text>
          <TouchableOpacity onPress={() => stockBottomSheetRef.current?.close()}>
            <Icon name="close" size={24} color={colors.text} />
          </TouchableOpacity>
        </BottomSheetView>
        
        <BottomSheetScrollView style={styles.stockMovementsList}>
          {loadingStock ? (
            <View style={styles.stockLoadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                Loading stock movements...
              </Text>
            </View>
          ) : stockMovements.length > 0 ? (
            stockMovements.map((movement, index) => (
              <View 
                key={movement.id} 
                style={[styles.movementItem, { borderBottomColor: colors.border }]}
              >
                <View style={styles.movementHeader}>
                  <Text style={[styles.movementName, { color: colors.text }]}>
                    {movement.name}
                  </Text>
                  <Text style={[styles.movementDate, { color: colors.textSecondary }]}>
                    {new Date(movement.date).toLocaleDateString()}
                  </Text>
                </View>
                <Text style={[styles.movementQuantity, { color: colors.primary }]}>
                  Qty: {movement.product_qty} {movement.product_uom?.[1] || ''}
                </Text>
                {movement.origin && (
                  <Text style={[styles.movementOrigin, { color: colors.textSecondary }]}>
                    Origin: {movement.origin}
                  </Text>
                )}
              </View>
            ))
          ) : (
            <View style={styles.emptyMovements}>
              <Icon name="package-variant" size={48} color={colors.textSecondary} />
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                No stock movements found
              </Text>
            </View>
          )}
        </BottomSheetScrollView>
      </BottomSheet>

      {/* Image Viewer Bottom Sheet */}
      <BottomSheet
        ref={imageBottomSheetRef}
        index={-1}
        snapPoints={['90%']}
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: 'black' }}
        handleIndicatorStyle={{ backgroundColor: 'white' }}
      >
        <BottomSheetView style={styles.imageViewerContainer}>
          <TouchableOpacity
            style={styles.imageViewerClose}
            onPress={() => imageBottomSheetRef.current?.close()}
          >
            <Icon name="close" size={24} color="white" />
          </TouchableOpacity>
          
          {product.image_1920 && (
            <Image
              source={{ uri: `data:image/png;base64,${product.image_1920}` }}
              style={styles.fullScreenImage}
              resizeMode="contain"
            />
          )}
        </BottomSheetView>
      </BottomSheet>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  errorText: {
    fontSize: 18,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  imageSection: {
    height: 300,
    backgroundColor: 'white',
  },
  imageContainer: {
    flex: 1,
    position: 'relative',
  },
  productImage: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  placeholderText: {
    fontSize: 14,
    fontWeight: '500',
  },
  imageOverlay: {
    position: 'absolute',
    top: 16,
    right: 16,
  },
  imageAction: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoSection: {
    flex: 1,
    margin: 16,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  headerLeft: {
    flex: 1,
    marginRight: 16,
  },
  productName: {
    fontSize: 24,
    fontWeight: '700',
    lineHeight: 30,
    marginBottom: 4,
  },
  productCode: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  priceSection: {
    marginBottom: 20,
    gap: 8,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  price: {
    fontSize: 24,
    fontWeight: '700',
  },
  costPrice: {
    fontSize: 16,
    fontWeight: '600',
  },
  stockSection: {
    marginBottom: 20,
    padding: 16,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 8,
  },
  stockHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  stockInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stockLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  stockDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stockItem: {
    alignItems: 'center',
  },
  stockValue: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 2,
  },
  stockSubtext: {
    fontSize: 12,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  detailsGrid: {
    gap: 16,
    marginBottom: 20,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  descriptionSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
  },
  typeSection: {
    gap: 12,
  },
  typeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  typeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  bottomSheetTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  stockMovementsList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  stockLoadingContainer: {
    paddingVertical: 32,
    alignItems: 'center',
    gap: 8,
  },
  movementItem: {
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  movementHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  movementName: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  movementDate: {
    fontSize: 12,
    fontWeight: '500',
  },
  movementQuantity: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  movementOrigin: {
    fontSize: 12,
  },
  emptyMovements: {
    paddingVertical: 64,
    alignItems: 'center',
    gap: 16,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
  },
  imageViewerContainer: {
    flex: 1,
    position: 'relative',
  },
  imageViewerClose: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  fullScreenImage: {
    flex: 1,
    width: '100%',
  },
});

export default ProductDetailScreen;
