import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  TextInput,
  Switch,
  ActivityIndicator
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../../contexts/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { productsAPI } from '../../../api/models/productsApi';

const ProductEditScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { colors } = useTheme();
  const { productId, isEditing = false } = route.params || {};

  // State
  const [loading, setLoading] = useState(isEditing);
  const [saving, setSaving] = useState(false);
  const [product, setProduct] = useState({
    name: '',
    default_code: '',
    list_price: '',
    standard_price: '',
    categ_id: null,
    barcode: '',
    weight: '',
    volume: '',
    sale_ok: true,
    purchase_ok: true,
    type: 'product',
    description_sale: '',
    description_purchase: ''
  });

  useEffect(() => {
    if (isEditing && productId) {
      loadProduct();
    }
  }, [isEditing, productId]);

  const loadProduct = async () => {
    try {
      setLoading(true);
      const productData = await productsAPI.getProduct(productId);
      
      if (productData) {
        setProduct({
          name: productData.name || '',
          default_code: productData.default_code || '',
          list_price: productData.list_price?.toString() || '',
          standard_price: productData.standard_price?.toString() || '',
          categ_id: productData.categ_id,
          barcode: productData.barcode || '',
          weight: productData.weight?.toString() || '',
          volume: productData.volume?.toString() || '',
          sale_ok: productData.sale_ok || false,
          purchase_ok: productData.purchase_ok || false,
          type: productData.type || 'product',
          description_sale: productData.description_sale || '',
          description_purchase: productData.description_purchase || ''
        });
      }
    } catch (error) {
      console.error('Error loading product:', error);
      Alert.alert('Error', 'Failed to load product details');
    } finally {
      setLoading(false);
    }
  };

  const updateField = (field, value) => {
    setProduct(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = () => {
    if (!product.name.trim()) {
      Alert.alert('Validation Error', 'Product name is required');
      return false;
    }
    
    if (product.list_price && isNaN(parseFloat(product.list_price))) {
      Alert.alert('Validation Error', 'Sale price must be a valid number');
      return false;
    }
    
    if (product.standard_price && isNaN(parseFloat(product.standard_price))) {
      Alert.alert('Validation Error', 'Cost price must be a valid number');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setSaving(true);
      
      // Prepare data for API
      const productData = {
        name: product.name.trim(),
        default_code: product.default_code.trim() || false,
        list_price: product.list_price ? parseFloat(product.list_price) : 0,
        standard_price: product.standard_price ? parseFloat(product.standard_price) : 0,
        barcode: product.barcode.trim() || false,
        weight: product.weight ? parseFloat(product.weight) : 0,
        volume: product.volume ? parseFloat(product.volume) : 0,
        sale_ok: product.sale_ok,
        purchase_ok: product.purchase_ok,
        type: product.type,
        description_sale: product.description_sale.trim() || false,
        description_purchase: product.description_purchase.trim() || false
      };

      let result;
      if (isEditing) {
        result = await productsAPI.updateProduct(productId, productData);
      } else {
        result = await productsAPI.createProduct(productData);
      }

      if (result) {
        Alert.alert(
          'Success',
          `Product ${isEditing ? 'updated' : 'created'} successfully`,
          [
            {
              text: 'OK',
              onPress: () => {
                if (isEditing) {
                  navigation.goBack();
                } else {
                  navigation.replace('ProductDetail', { 
                    productId: result.id || result[0],
                    productName: product.name 
                  });
                }
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error saving product:', error);
      Alert.alert('Error', `Failed to ${isEditing ? 'update' : 'create'} product`);
    } finally {
      setSaving(false);
    }
  };

  const productTypes = [
    { value: 'product', label: 'Stockable Product' },
    { value: 'consu', label: 'Consumable' },
    { value: 'service', label: 'Service' }
  ];

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading product...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={navigation.goBack}>
          <Icon name="close" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          {isEditing ? 'Edit Product' : 'New Product'}
        </Text>
        <TouchableOpacity
          onPress={handleSave}
          disabled={saving}
          style={[styles.saveButton, { backgroundColor: colors.primary }]}
        >
          {saving ? (
            <ActivityIndicator size="small" color={colors.onPrimary} />
          ) : (
            <Text style={[styles.saveButtonText, { color: colors.onPrimary }]}>
              Save
            </Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Basic Information */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Basic Information
          </Text>

          <View style={styles.formField}>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>
              Product Name *
            </Text>
            <TextInput
              style={[styles.textInput, { 
                backgroundColor: colors.background,
                borderColor: colors.border,
                color: colors.text 
              }]}
              placeholder="Enter product name"
              placeholderTextColor={colors.textSecondary}
              value={product.name}
              onChangeText={(text) => updateField('name', text)}
              maxLength={200}
            />
          </View>

          <View style={styles.formField}>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>
              Internal Reference
            </Text>
            <TextInput
              style={[styles.textInput, { 
                backgroundColor: colors.background,
                borderColor: colors.border,
                color: colors.text 
              }]}
              placeholder="Product code/SKU"
              placeholderTextColor={colors.textSecondary}
              value={product.default_code}
              onChangeText={(text) => updateField('default_code', text)}
              maxLength={50}
            />
          </View>

          <View style={styles.formField}>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>
              Barcode
            </Text>
            <TextInput
              style={[styles.textInput, { 
                backgroundColor: colors.background,
                borderColor: colors.border,
                color: colors.text 
              }]}
              placeholder="Product barcode"
              placeholderTextColor={colors.textSecondary}
              value={product.barcode}
              onChangeText={(text) => updateField('barcode', text)}
              maxLength={50}
            />
          </View>
        </View>

        {/* Pricing */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Pricing
          </Text>

          <View style={styles.priceRow}>
            <View style={[styles.formField, { flex: 1, marginRight: 8 }]}>
              <Text style={[styles.fieldLabel, { color: colors.text }]}>
                Sale Price
              </Text>
              <View style={[styles.priceInput, { 
                backgroundColor: colors.background,
                borderColor: colors.border 
              }]}>
                <Text style={[styles.currencySymbol, { color: colors.textSecondary }]}>
                  $
                </Text>
                <TextInput
                  style={[styles.priceTextInput, { color: colors.text }]}
                  placeholder="0.00"
                  placeholderTextColor={colors.textSecondary}
                  value={product.list_price}
                  onChangeText={(text) => updateField('list_price', text)}
                  keyboardType="decimal-pad"
                />
              </View>
            </View>

            <View style={[styles.formField, { flex: 1, marginLeft: 8 }]}>
              <Text style={[styles.fieldLabel, { color: colors.text }]}>
                Cost Price
              </Text>
              <View style={[styles.priceInput, { 
                backgroundColor: colors.background,
                borderColor: colors.border 
              }]}>
                <Text style={[styles.currencySymbol, { color: colors.textSecondary }]}>
                  $
                </Text>
                <TextInput
                  style={[styles.priceTextInput, { color: colors.text }]}
                  placeholder="0.00"
                  placeholderTextColor={colors.textSecondary}
                  value={product.standard_price}
                  onChangeText={(text) => updateField('standard_price', text)}
                  keyboardType="decimal-pad"
                />
              </View>
            </View>
          </View>
        </View>

        {/* Product Type */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Product Type
          </Text>

          <View style={styles.typeSelector}>
            {productTypes.map((type) => (
              <TouchableOpacity
                key={type.value}
                style={[
                  styles.typeOption,
                  {
                    backgroundColor: product.type === type.value 
                      ? colors.primary + '15' 
                      : colors.background,
                    borderColor: product.type === type.value 
                      ? colors.primary 
                      : colors.border
                  }
                ]}
                onPress={() => updateField('type', type.value)}
              >
                <Icon 
                  name={product.type === type.value ? "radiobox-marked" : "radiobox-blank"} 
                  size={20} 
                  color={product.type === type.value ? colors.primary : colors.textSecondary} 
                />
                <Text 
                  style={[
                    styles.typeOptionText,
                    { color: product.type === type.value ? colors.primary : colors.text }
                  ]}
                >
                  {type.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Physical Properties */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Physical Properties
          </Text>

          <View style={styles.priceRow}>
            <View style={[styles.formField, { flex: 1, marginRight: 8 }]}>
              <Text style={[styles.fieldLabel, { color: colors.text }]}>
                Weight (kg)
              </Text>
              <TextInput
                style={[styles.textInput, { 
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text 
                }]}
                placeholder="0.00"
                placeholderTextColor={colors.textSecondary}
                value={product.weight}
                onChangeText={(text) => updateField('weight', text)}
                keyboardType="decimal-pad"
              />
            </View>

            <View style={[styles.formField, { flex: 1, marginLeft: 8 }]}>
              <Text style={[styles.fieldLabel, { color: colors.text }]}>
                Volume (m³)
              </Text>
              <TextInput
                style={[styles.textInput, { 
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text 
                }]}
                placeholder="0.00"
                placeholderTextColor={colors.textSecondary}
                value={product.volume}
                onChangeText={(text) => updateField('volume', text)}
                keyboardType="decimal-pad"
              />
            </View>
          </View>
        </View>

        {/* Sales & Purchase */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Sales & Purchase
          </Text>

          <View style={styles.switchRow}>
            <View style={styles.switchInfo}>
              <Text style={[styles.switchLabel, { color: colors.text }]}>
                Can be Sold
              </Text>
              <Text style={[styles.switchDescription, { color: colors.textSecondary }]}>
                Allow this product to be sold
              </Text>
            </View>
            <Switch
              value={product.sale_ok}
              onValueChange={(value) => updateField('sale_ok', value)}
              trackColor={{ false: colors.border, true: colors.primary + '40' }}
              thumbColor={product.sale_ok ? colors.primary : colors.textSecondary}
            />
          </View>

          <View style={styles.switchRow}>
            <View style={styles.switchInfo}>
              <Text style={[styles.switchLabel, { color: colors.text }]}>
                Can be Purchased
              </Text>
              <Text style={[styles.switchDescription, { color: colors.textSecondary }]}>
                Allow this product to be purchased
              </Text>
            </View>
            <Switch
              value={product.purchase_ok}
              onValueChange={(value) => updateField('purchase_ok', value)}
              trackColor={{ false: colors.border, true: colors.primary + '40' }}
              thumbColor={product.purchase_ok ? colors.primary : colors.textSecondary}
            />
          </View>
        </View>

        {/* Descriptions */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Descriptions
          </Text>

          <View style={styles.formField}>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>
              Sales Description
            </Text>
            <TextInput
              style={[styles.textArea, { 
                backgroundColor: colors.background,
                borderColor: colors.border,
                color: colors.text 
              }]}
              placeholder="Description shown on sales orders and invoices"
              placeholderTextColor={colors.textSecondary}
              value={product.description_sale}
              onChangeText={(text) => updateField('description_sale', text)}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formField}>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>
              Purchase Description
            </Text>
            <TextInput
              style={[styles.textArea, { 
                backgroundColor: colors.background,
                borderColor: colors.border,
                color: colors.text 
              }]}
              placeholder="Description shown on purchase orders and bills"
              placeholderTextColor={colors.textSecondary}
              value={product.description_purchase}
              onChangeText={(text) => updateField('description_purchase', text)}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </View>

        {/* Bottom Spacing */}
        <View style={{ height: 32 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    minWidth: 60,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  formField: {
    marginBottom: 16,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    minHeight: 80,
  },
  priceRow: {
    flexDirection: 'row',
  },
  priceInput: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 4,
  },
  priceTextInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 0,
  },
  typeSelector: {
    gap: 12,
  },
  typeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 12,
  },
  typeOptionText: {
    fontSize: 15,
    fontWeight: '500',
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  switchInfo: {
    flex: 1,
    marginRight: 16,
  },
  switchLabel: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 2,
  },
  switchDescription: {
    fontSize: 13,
    lineHeight: 16,
  },
});

export default ProductEditScreen;
