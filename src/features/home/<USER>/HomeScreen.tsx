import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  StatusBar,
  TextInput,
  SafeAreaView
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../contexts/ThemeContext';

// Import components
import MainTile from '../../../components/tiles/MainTile';
import QuickAccessItem from '../components/QuickAccessItem';
import ActivityItem from '../components/ActivityItem';
import SectionHeader from '../components/SectionHeader';

// Import AppContext instead of Redux
import { useSelector, useDispatch } from '../../../contexts/app/AppContext';

const HomeScreen = () => {
  const navigation = useNavigation();
  const { colors } = useTheme();
  const dispatch = useDispatch();
  const [refreshing, setRefreshing] = useState(false);

  // Get user from our context instead of Redux
  const user = useSelector(state => state.user);

  // Mock data for tiles - using Ionicons with theme colors
  const mainApps = [
    { id: 'contacts', name: 'Contacts', icon: <Ionicons name="people" size={24} color="white" />, color: colors.secondary, count: 142, route: 'ContactsList' },
    { id: 'inventory', name: 'Inventory', icon: <Ionicons name="cube" size={24} color="white" />, color: colors.success, count: 87, route: 'InventoryList' },
    { id: 'helpdesk', name: 'Helpdesk', icon: <Ionicons name="notifications" size={24} color="white" />, color: colors.error, count: 12, route: 'HelpdeskList' },
    { id: 'calendar', name: 'Calendar', icon: <Ionicons name="calendar" size={24} color="white" />, color: '#F59E0B', count: 5, route: 'CalendarView' }
  ];

  // Mock data for quick access
  const quickAccessItems = [
    { id: 'chat', name: 'Chat', icon: <Ionicons name="chatbubbles" size={20} color="white" />, color: colors.primary, route: 'Discuss' },
    { id: 'contacts', name: 'Contacts', icon: <Ionicons name="people" size={20} color="white" />, color: colors.secondary, route: 'ContactsList' },
    { id: 'inventory', name: 'Inventory', icon: <Ionicons name="cube" size={20} color="white" />, color: colors.success, route: 'InventoryList' },
    { id: 'helpdesk', name: 'Helpdesk', icon: <Ionicons name="notifications" size={20} color="white" />, color: colors.error, route: 'HelpdeskList' }
  ];

  // Mock data for activity
  const activityItems = [
    {
      id: 1,
      title: 'Sales Quote #1234',
      description: 'Quote created for Client XYZ',
      time: '10 min ago',
      icon: <Ionicons name="document-text-outline" size={20} color="#666" />,
      route: 'SalesDetail',
      params: { id: 1234 }
    },
    {
      id: 2,
      title: 'Support Ticket #4567',
      description: 'New ticket from Customer ABC',
      time: '45 min ago',
      icon: <Ionicons name="help-buoy-outline" size={20} color="#666" />,
      route: 'TicketDetail',
      params: { id: 4567 }
    },
    {
      id: 3,
      title: 'Invoice #INV-2023-089',
      description: 'Invoice generated',
      time: '2 hours ago',
      icon: <Ionicons name="document-outline" size={20} color="#666" />,
      route: 'InvoiceDetail',
      params: { id: 789 }
    }
  ];

  // Mock favorites
  const favorites = [
    { id: 1, name: 'Monthly Sales Report', icon: <Ionicons name="document-text-outline" size={24} color="#333" />, route: 'ReportDetail' },
    { id: 2, name: 'Inventory Restock', icon: <Ionicons name="cube-outline" size={24} color="#333" />, route: 'InventoryDetail' },
    { id: 3, name: 'Team Calendar', icon: <Ionicons name="calendar-outline" size={24} color="#333" />, route: 'CalendarView' }
  ];

  // Handle refresh
  const onRefresh = () => {
    setRefreshing(true);

    // Use our context dispatch instead of Redux
    dispatch({ type: 'REFRESH_DATA' });

    // Simulate a refresh delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1500);
  };

  // Handle navigation to a screen
  const navigateTo = (route, params = {}) => {
    navigation.navigate(route, params);
  };

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: colors.background }]}>
      {/* Status Bar */}
      <StatusBar barStyle="light-content" backgroundColor={colors.primary} />

      {/* Top Navigation Bar */}
      <View style={[styles.header, { backgroundColor: colors.primary, borderBottomColor: colors.border }]}>
        <TouchableOpacity style={styles.menuButton} onPress={() => navigation.toggleDrawer()}>
          <Ionicons name="menu-outline" size={24} color={colors.onPrimary} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.onPrimary }]}>ExoMobile</Text>
        <View style={styles.headerRight}>
          <TouchableOpacity style={styles.iconButton} onPress={() => navigateTo('Notifications')}>
            <Ionicons name="notifications-outline" size={24} color={colors.onPrimary} />
            <View style={[styles.badge, { backgroundColor: colors.error }]}>
              <Text style={[styles.badgeText, { color: colors.onPrimary }]}>3</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton} onPress={() => navigateTo('Settings')}>
            <Ionicons name="settings-outline" size={24} color={colors.onPrimary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <View style={[styles.searchBar, { backgroundColor: colors.background }]}>
          <Ionicons name="search" size={20} color={colors.textSecondary} style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Search across all services..."
            placeholderTextColor={colors.textSecondary}
          />
        </View>
      </View>

      {/* Main Content */}
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* Welcome Message */}
        <View style={styles.welcomeContainer}>
          <Text style={[styles.welcomeText, { color: colors.text }]}>Welcome back, {user.name}</Text>
          <Text style={[styles.subtitleText, { color: colors.textSecondary }]}>What would you like to do today?</Text>
        </View>

        {/* Main Tiles - 2x2 Grid */}
        <View style={styles.tilesContainer}>
          <View style={styles.tileRow}>
            <View style={styles.tileCol}>
              <MainTile
                title={mainApps[0].name}
                icon={mainApps[0].icon}
                color={mainApps[0].color}
                count={mainApps[0].count}
                route={mainApps[0].route}
              />
            </View>
            <View style={styles.tileCol}>
              <MainTile
                title={mainApps[1].name}
                icon={mainApps[1].icon}
                color={mainApps[1].color}
                count={mainApps[1].count}
                route={mainApps[1].route}
              />
            </View>
          </View>
          <View style={styles.tileRow}>
            <View style={styles.tileCol}>
              <MainTile
                title={mainApps[2].name}
                icon={mainApps[2].icon}
                color={mainApps[2].color}
                count={mainApps[2].count}
                route={mainApps[2].route}
              />
            </View>
            <View style={styles.tileCol}>
              <MainTile
                title={mainApps[3].name}
                icon={mainApps[3].icon}
                color={mainApps[3].color}
                count={mainApps[3].count}
                route={mainApps[3].route}
              />
            </View>
          </View>
        </View>

        {/* Quick Access */}
        <View style={styles.sectionContainer}>
          <SectionHeader title="Quick Access" />
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.quickAccessContainer}
          >
            {quickAccessItems.map(item => (
              <QuickAccessItem
                key={item.id}
                title={item.name}
                icon={item.icon}
                color={item.color}
                onPress={() => navigateTo(item.route)}
              />
            ))}
          </ScrollView>
        </View>

        {/* Recent Activity */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeaderContainer}>
            <Text style={[styles.sectionHeaderTitle, { color: colors.text }]}>Recent Activity</Text>
            <TouchableOpacity onPress={() => navigateTo('ActivityList')}>
              <Text style={[styles.sectionHeaderAction, { color: colors.primary }]}>See All</Text>
            </TouchableOpacity>
          </View>

          <View style={[styles.activityList, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            {activityItems.map(item => (
              <ActivityItem
                key={item.id}
                title={item.title}
                description={item.description}
                time={item.time}
                icon={item.icon}
                onPress={() => navigateTo(item.route, item.params)}
              />
            ))}
          </View>
        </View>

        {/* Favorites */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeaderContainer}>
            <Text style={[styles.sectionHeaderTitle, { color: colors.text }]}>Favorites</Text>
            <TouchableOpacity>
              <Text style={[styles.sectionHeaderAction, { color: colors.primary }]}>Edit</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.favoritesContainer}>
            {favorites.map(item => (
              <TouchableOpacity
                key={item.id}
                style={[styles.favoriteItem, { backgroundColor: colors.surface, borderColor: colors.border }]}
                onPress={() => navigateTo(item.route)}
              >
                <View style={styles.favoriteIcon}>
                  {item.icon}
                </View>
                <Text style={[styles.favoriteName, { color: colors.text }]}>{item.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Bottom Navigation Bar */}
      <View style={[styles.bottomNav, { backgroundColor: colors.surface, borderTopColor: colors.border }]}>
        <TouchableOpacity style={styles.bottomNavItem} onPress={() => {}}>
          <Ionicons name="bar-chart-outline" size={24} color={colors.primary} />
          <Text style={[styles.bottomNavText, styles.bottomNavActive, { color: colors.primary }]}>Home</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.bottomNavItem} onPress={() => navigateTo('Discuss')}>
          <Ionicons name="chatbubble-outline" size={24} color={colors.textSecondary} />
          <Text style={[styles.bottomNavText, { color: colors.textSecondary }]}>Chat</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.bottomNavItem} onPress={() => navigateTo('Favorites')}>
          <Ionicons name="star-outline" size={24} color={colors.textSecondary} />
          <Text style={[styles.bottomNavText, { color: colors.textSecondary }]}>Favorites</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.bottomNavItem} onPress={() => navigateTo('Settings')}>
          <Ionicons name="settings-outline" size={24} color={colors.textSecondary} />
          <Text style={[styles.bottomNavText, { color: colors.textSecondary }]}>Settings</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  menuButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    padding: 4,
    marginLeft: 16,
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  searchContainer: {
    padding: 16,
    borderBottomWidth: 1,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    padding: 0,
  },
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 20,
  },
  welcomeContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  welcomeText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  subtitleText: {
    fontSize: 14,
    marginTop: 4,
  },
  tilesContainer: {
    padding: 16,
  },
  tileRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  tileCol: {
    flex: 1,
    paddingHorizontal: 8,
  },
  sectionContainer: {
    marginTop: 16,
  },
  sectionHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  sectionHeaderTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  sectionHeaderAction: {
    fontSize: 14,
  },
  quickAccessContainer: {
    paddingHorizontal: 8,
    paddingVertical: 8,
  },
  activityList: {
    borderRadius: 12,
    marginHorizontal: 16,
    overflow: 'hidden',
    borderWidth: 1,
  },
  favoritesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
  },
  favoriteItem: {
    width: '31%',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
    borderWidth: 1,
  },
  favoriteIcon: {
    marginBottom: 8,
  },
  favoriteName: {
    fontSize: 12,
    textAlign: 'center',
  },
  bottomNav: {
    flexDirection: 'row',
    borderTopWidth: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  bottomNavItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomNavText: {
    fontSize: 12,
    marginTop: 4,
  },
  bottomNavActive: {
    fontWeight: '500',
  },
});

export default HomeScreen;