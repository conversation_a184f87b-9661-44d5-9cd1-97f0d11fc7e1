import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const { width: screenWidth } = Dimensions.get('window');
const cardWidth = (screenWidth - 48) / 2; // Account for padding and gap

const UserCard = ({
  user,
  viewMode = 'grid',
  onPress,
  onLongPress,
  onDetailsPress,
  isSelected = false,
  isSelectionMode = false,
  index
}) => {
  const { colors } = useTheme();

  const getUserTypeIcon = (isPortal, isActive) => {
    if (!isActive) return 'account-off';
    return isPortal ? 'account-circle' : 'account-star';
  };

  const getUserTypeColor = (isPortal, isActive) => {
    if (!isActive) return colors.textSecondary;
    return isPortal ? colors.secondary : colors.primary;
  };

  const getUserTypeLabel = (isPortal, isActive) => {
    if (!isActive) return 'Inactive';
    return isPortal ? 'Portal' : 'Internal';
  };

  const formatLastLogin = (loginDate) => {
    if (!loginDate) return 'Never';
    const date = new Date(loginDate);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays - 1} days ago`;
    return date.toLocaleDateString();
  };

  if (viewMode === 'list') {
    return (
      <TouchableOpacity
        style={[
          styles.listCard,
          { 
            backgroundColor: colors.surface,
            borderColor: isSelected ? colors.primary : colors.border
          },
          isSelected && { borderWidth: 2 }
        ]}
        onPress={onPress}
        onLongPress={onLongPress}
        activeOpacity={0.7}
      >
        {/* Selection Indicator */}
        {isSelectionMode && (
          <View style={[styles.selectionIndicator, { backgroundColor: colors.primary }]}>
            {isSelected && (
              <Icon name="check" size={16} color={colors.onPrimary} />
            )}
          </View>
        )}

        {/* User Image */}
        <View style={styles.listImageContainer}>
          {user.image_128 ? (
            <Image
              source={{ uri: `data:image/png;base64,${user.image_128}` }}
              style={styles.listImage}
              resizeMode="cover"
            />
          ) : (
            <View style={[styles.placeholderImage, { backgroundColor: colors.border }]}>
              <Icon name="account" size={24} color={colors.textSecondary} />
            </View>
          )}
        </View>

        {/* User Info */}
        <View style={styles.listContent}>
          <View style={styles.listHeader}>
            <Text 
              style={[styles.listUserName, { color: colors.text }]}
              numberOfLines={2}
            >
              {user.name}
            </Text>
            <TouchableOpacity onPress={onDetailsPress}>
              <Icon name="dots-vertical" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>

          <Text style={[styles.userLogin, { color: colors.textSecondary }]}>
            @{user.login}
          </Text>

          {user.email && (
            <Text style={[styles.userEmail, { color: colors.textSecondary }]}>
              {user.email}
            </Text>
          )}

          <View style={styles.listFooter}>
            <View style={styles.loginContainer}>
              <Icon name="clock" size={14} color={colors.textSecondary} />
              <Text style={[styles.loginText, { color: colors.textSecondary }]}>
                {formatLastLogin(user.login_date)}
              </Text>
            </View>

            <View style={styles.typeContainer}>
              <Icon 
                name={getUserTypeIcon(user.share, user.active)} 
                size={16} 
                color={getUserTypeColor(user.share, user.active)} 
              />
              <Text style={[styles.typeText, { color: getUserTypeColor(user.share, user.active) }]}>
                {getUserTypeLabel(user.share, user.active)}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  }

  // Grid view
  return (
    <TouchableOpacity
      style={[
        styles.gridCard,
        { 
          backgroundColor: colors.surface,
          borderColor: isSelected ? colors.primary : colors.border,
          width: cardWidth
        },
        isSelected && { borderWidth: 2 }
      ]}
      onPress={onPress}
      onLongPress={onLongPress}
      activeOpacity={0.7}
    >
      {/* Selection Indicator */}
      {isSelectionMode && (
        <View style={[styles.selectionIndicator, { backgroundColor: colors.primary }]}>
          {isSelected && (
            <Icon name="check" size={16} color={colors.onPrimary} />
          )}
        </View>
      )}

      {/* User Image */}
      <View style={styles.imageContainer}>
        {user.image_128 ? (
          <Image
            source={{ uri: `data:image/png;base64,${user.image_128}` }}
            style={styles.userImage}
            resizeMode="cover"
          />
        ) : (
          <View style={[styles.placeholderImage, { backgroundColor: colors.border }]}>
            <Icon name="account" size={32} color={colors.textSecondary} />
          </View>
        )}

        {/* User Type Badge */}
        <View style={[styles.typeBadge, { backgroundColor: getUserTypeColor(user.share, user.active) }]}>
          <Icon 
            name={getUserTypeIcon(user.share, user.active)} 
            size={12} 
            color={colors.onPrimary} 
          />
        </View>

        {/* Details Button */}
        <TouchableOpacity 
          style={[styles.detailsButton, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
          onPress={onDetailsPress}
        >
          <Icon name="dots-vertical" size={16} color="white" />
        </TouchableOpacity>
      </View>

      {/* User Info */}
      <View style={styles.cardContent}>
        <Text 
          style={[styles.userName, { color: colors.text }]}
          numberOfLines={2}
        >
          {user.name}
        </Text>

        <Text style={[styles.userLogin, { color: colors.textSecondary }]}>
          @{user.login}
        </Text>

        {user.company_id && (
          <Text style={[styles.company, { color: colors.textSecondary }]}>
            {user.company_id[1]}
          </Text>
        )}

        <View style={styles.cardFooter}>
          <View style={styles.contactIcons}>
            {user.email && (
              <Icon name="email" size={14} color={colors.primary} />
            )}
            {user.phone && (
              <Icon name="phone" size={14} color={colors.success} />
            )}
            {user.mobile && (
              <Icon name="cellphone" size={14} color={colors.secondary} />
            )}
          </View>
          
          <Text style={[styles.typeLabel, { color: getUserTypeColor(user.share, user.active) }]}>
            {getUserTypeLabel(user.share, user.active)}
          </Text>
        </View>

        <View style={styles.lastLogin}>
          <Icon name="clock" size={12} color={colors.textSecondary} />
          <Text style={[styles.lastLoginText, { color: colors.textSecondary }]}>
            {formatLastLogin(user.login_date)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  // Grid Card Styles
  gridCard: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    marginHorizontal: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  imageContainer: {
    position: 'relative',
    height: 140,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: 'hidden',
  },
  userImage: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  typeBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    paddingHorizontal: 6,
    paddingVertical: 4,
    borderRadius: 10,
    minWidth: 24,
    alignItems: 'center',
  },
  detailsButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardContent: {
    padding: 12,
    gap: 4,
  },
  userName: {
    fontSize: 14,
    fontWeight: '600',
    lineHeight: 18,
  },
  userLogin: {
    fontSize: 12,
    fontFamily: 'monospace',
  },
  company: {
    fontSize: 11,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  contactIcons: {
    flexDirection: 'row',
    gap: 4,
  },
  typeLabel: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  lastLogin: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginTop: 4,
  },
  lastLoginText: {
    fontSize: 10,
  },

  // List Card Styles
  listCard: {
    flexDirection: 'row',
    borderRadius: 12,
    borderWidth: 1,
    marginVertical: 4,
    marginHorizontal: 16,
    padding: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  listImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    overflow: 'hidden',
    marginRight: 12,
  },
  listImage: {
    width: '100%',
    height: '100%',
  },
  listContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  listUserName: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 20,
    flex: 1,
    marginRight: 8,
  },
  userEmail: {
    fontSize: 12,
    marginTop: 2,
  },
  listFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  loginContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  loginText: {
    fontSize: 11,
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  typeText: {
    fontSize: 11,
    fontWeight: '600',
    textTransform: 'capitalize',
  },

  // Selection Indicator
  selectionIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    borderWidth: 2,
    borderColor: 'white',
  },
});

export default UserCard;
