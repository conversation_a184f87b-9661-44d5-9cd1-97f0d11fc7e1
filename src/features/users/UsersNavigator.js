import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import UsersListScreen from './screens/UsersListScreen';
import UserDetailScreen from './screens/UserDetailScreen';
import UserEditScreen from './screens/UserEditScreen';
import { useTheme } from '../../contexts/ThemeContext';

const Stack = createStackNavigator();

const UsersNavigator = () => {
  const { colors } = useTheme();

  return (
    <Stack.Navigator
      initialRouteName="UsersList"
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: colors.onPrimary,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        headerBackTitleVisible: false,
      }}
    >
      <Stack.Screen
        name="UsersList"
        component={UsersListScreen}
        options={{
          title: 'Users',
          headerLargeTitle: true,
        }}
      />
      <Stack.Screen
        name="UserDetail"
        component={UserDetailScreen}
        options={({ route }) => ({
          title: route.params?.userName || 'User Details',
        })}
      />
      <Stack.Screen
        name="UserEdit"
        component={UserEditScreen}
        options={({ route }) => ({
          title: route.params?.isEditing ? 'Edit User' : 'New User',
        })}
      />
    </Stack.Navigator>
  );
};

export default UsersNavigator;
