import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  Linking,
  RefreshControl
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { resUsersAPI as usersAPI } from '../../../api/models/usersApi';

const UserDetailScreen = () => {
  const { colors } = useTheme();
  const navigation = useNavigation();
  const route = useRoute();
  const { userId, userName } = route.params;

  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadUserDetails();
  }, [userId]);

  const loadUserDetails = async () => {
    try {
      setLoading(true);
      const userData = await usersAPI.getUserById(userId);
      setUser(userData);
    } catch (error) {
      console.error('Error loading user details:', error);
      Alert.alert('Error', 'Failed to load user details');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadUserDetails();
    } catch (error) {
      // Error already handled in loadUserDetails
    } finally {
      setRefreshing(false);
    }
  };

  const handleEdit = () => {
    navigation.navigate('UserEdit', {
      userId: user.id,
      isEditing: true
    });
  };

  const handleCall = (phone) => {
    if (phone) {
      Linking.openURL(`tel:${phone}`);
    }
  };

  const handleEmail = (email) => {
    if (email) {
      Linking.openURL(`mailto:${email}`);
    }
  };

  const getUserTypeInfo = (user) => {
    if (!user.active) {
      return {
        label: 'Inactive',
        icon: 'account-off',
        color: colors.textSecondary,
        bgColor: colors.border
      };
    }

    if (user.share) {
      return {
        label: 'Portal User',
        icon: 'account-circle',
        color: colors.secondary,
        bgColor: colors.secondary + '20'
      };
    }

    return {
      label: 'Internal User',
      icon: 'account-star',
      color: colors.primary,
      bgColor: colors.primary + '20'
    };
  };

  const formatLastLogin = (loginDate) => {
    if (!loginDate) return 'Never logged in';
    const date = new Date(loginDate);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays - 1} days ago`;
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading user details...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!user) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <Icon name="account-alert" size={64} color={colors.textSecondary} />
          <Text style={[styles.errorTitle, { color: colors.text }]}>
            User Not Found
          </Text>
          <Text style={[styles.errorSubtitle, { color: colors.textSecondary }]}>
            The user you're looking for doesn't exist or has been removed.
          </Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.primary }]}
            onPress={() => navigation.goBack()}
          >
            <Text style={[styles.backButtonText, { color: colors.onPrimary }]}>
              Go Back
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const userTypeInfo = getUserTypeInfo(user);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* Header Section */}
        <View style={[styles.header, { backgroundColor: colors.surface }]}>
          <View style={styles.headerContent}>
            {/* User Image */}
            <View style={styles.imageContainer}>
              {user.image_128 ? (
                <Image
                  source={{ uri: `data:image/png;base64,${user.image_128}` }}
                  style={styles.userImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={[styles.placeholderImage, { backgroundColor: colors.border }]}>
                  <Icon name="account" size={48} color={colors.textSecondary} />
                </View>
              )}

              {/* Status Badge */}
              <View style={[styles.statusBadge, { backgroundColor: userTypeInfo.bgColor }]}>
                <Icon name={userTypeInfo.icon} size={16} color={userTypeInfo.color} />
              </View>
            </View>

            {/* User Info */}
            <View style={styles.userInfo}>
              <Text style={[styles.userName, { color: colors.text }]}>
                {user.name}
              </Text>
              <Text style={[styles.userLogin, { color: colors.textSecondary }]}>
                @{user.login}
              </Text>
              <View style={[styles.typeChip, { backgroundColor: userTypeInfo.bgColor }]}>
                <Icon name={userTypeInfo.icon} size={14} color={userTypeInfo.color} />
                <Text style={[styles.typeText, { color: userTypeInfo.color }]}>
                  {userTypeInfo.label}
                </Text>
              </View>
            </View>

            {/* Edit Button */}
            <TouchableOpacity
              style={[styles.editButton, { backgroundColor: colors.primary }]}
              onPress={handleEdit}
            >
              <Icon name="pencil" size={20} color={colors.onPrimary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Contact Information */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Contact Information
          </Text>

          {user.email && (
            <TouchableOpacity
              style={styles.contactItem}
              onPress={() => handleEmail(user.email)}
            >
              <Icon name="email" size={20} color={colors.primary} />
              <View style={styles.contactInfo}>
                <Text style={[styles.contactLabel, { color: colors.textSecondary }]}>
                  Email
                </Text>
                <Text style={[styles.contactValue, { color: colors.text }]}>
                  {user.email}
                </Text>
              </View>
              <Icon name="open-in-new" size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          )}

          {user.phone && (
            <TouchableOpacity
              style={styles.contactItem}
              onPress={() => handleCall(user.phone)}
            >
              <Icon name="phone" size={20} color={colors.success} />
              <View style={styles.contactInfo}>
                <Text style={[styles.contactLabel, { color: colors.textSecondary }]}>
                  Phone
                </Text>
                <Text style={[styles.contactValue, { color: colors.text }]}>
                  {user.phone}
                </Text>
              </View>
              <Icon name="phone" size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          )}

          {user.mobile && (
            <TouchableOpacity
              style={styles.contactItem}
              onPress={() => handleCall(user.mobile)}
            >
              <Icon name="cellphone" size={20} color={colors.secondary} />
              <View style={styles.contactInfo}>
                <Text style={[styles.contactLabel, { color: colors.textSecondary }]}>
                  Mobile
                </Text>
                <Text style={[styles.contactValue, { color: colors.text }]}>
                  {user.mobile}
                </Text>
              </View>
              <Icon name="phone" size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>

        {/* System Information */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            System Information
          </Text>

          <View style={styles.infoItem}>
            <Icon name="domain" size={20} color={colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                Company
              </Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {user.company_id ? user.company_id[1] : 'Not assigned'}
              </Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <Icon name="clock" size={20} color={colors.warning} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                Last Login
              </Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {formatLastLogin(user.login_date)}
              </Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <Icon name="calendar-plus" size={20} color={colors.success} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                Created
              </Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {user.create_date ? new Date(user.create_date).toLocaleDateString() : 'Unknown'}
              </Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <Icon name="account-key" size={20} color={colors.secondary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                User ID
              </Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                #{user.id}
              </Text>
            </View>
          </View>
        </View>

        {/* Groups & Permissions */}
        {user.groups_id && user.groups_id.length > 0 && (
          <View style={[styles.section, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Groups & Permissions
            </Text>

            <View style={styles.groupsContainer}>
              {user.groups_id.map((group, index) => (
                <View
                  key={index}
                  style={[styles.groupChip, { backgroundColor: colors.primary + '20' }]}
                >
                  <Icon name="account-group" size={14} color={colors.primary} />
                  <Text style={[styles.groupText, { color: colors.primary }]}>
                    {typeof group === 'object' ? group[1] : group}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary }]}
            onPress={handleEdit}
          >
            <Icon name="pencil" size={20} color={colors.onPrimary} />
            <Text style={[styles.actionButtonText, { color: colors.onPrimary }]}>
              Edit User
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.warning }]}
            onPress={() => Alert.alert('Reset Password', 'Password reset functionality coming soon')}
          >
            <Icon name="key-variant" size={20} color={colors.onPrimary} />
            <Text style={[styles.actionButtonText, { color: colors.onPrimary }]}>
              Reset Password
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: user.active ? colors.error : colors.success }]}
            onPress={() => Alert.alert(
              user.active ? 'Deactivate User' : 'Activate User',
              `${user.active ? 'Deactivate' : 'Activate'} user functionality coming soon`
            )}
          >
            <Icon
              name={user.active ? 'account-off' : 'account-check'}
              size={20}
              color={colors.onPrimary}
            />
            <Text style={[styles.actionButtonText, { color: colors.onPrimary }]}>
              {user.active ? 'Deactivate' : 'Activate'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    gap: 16,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
  },
  errorSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  backButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },

  // Header Styles
  header: {
    padding: 20,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  imageContainer: {
    position: 'relative',
  },
  userImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  placeholderImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  userInfo: {
    flex: 1,
    gap: 4,
  },
  userName: {
    fontSize: 20,
    fontWeight: '700',
  },
  userLogin: {
    fontSize: 14,
    fontFamily: 'monospace',
  },
  typeChip: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  typeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  editButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Section Styles
  section: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },

  // Contact Item Styles
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  contactInfo: {
    flex: 1,
  },
  contactLabel: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  contactValue: {
    fontSize: 16,
    marginTop: 2,
  },

  // Info Item Styles
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  infoValue: {
    fontSize: 16,
    marginTop: 2,
  },

  // Groups Styles
  groupsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  groupChip: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
  },
  groupText: {
    fontSize: 12,
    fontWeight: '500',
  },

  // Action Buttons
  actionsContainer: {
    padding: 16,
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 14,
    borderRadius: 8,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default UserDetailScreen;