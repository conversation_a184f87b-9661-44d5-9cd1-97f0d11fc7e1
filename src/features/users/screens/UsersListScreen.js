import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
  SafeAreaView,
  TextInput,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme } from '../../../contexts/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import BottomSheet, { BottomSheetView, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { resUsersAPI as usersAPI } from '../../../api/models/usersApi';
import UserCard from '../components/UserCard';

const { width: screenWidth } = Dimensions.get('window');

const UsersListScreen = () => {
  const navigation = useNavigation();
  const { colors } = useTheme();

  // Bottom Sheet refs
  const bottomSheetRef = useRef(null);
  const actionBottomSheetRef = useRef(null);

  // Bottom Sheet snap points
  const snapPoints = useMemo(() => ['25%', '50%', '90%'], []);
  const actionSnapPoints = useMemo(() => ['30%', '50%'], []);

  // State management
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  // Filter state
  const [filterType, setFilterType] = useState('all'); // 'all', 'internal', 'portal'

  // View state
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Reload when screen focuses
  useFocusEffect(
    useCallback(() => {
      if (users.length === 0) {
        loadInitialData();
      }
    }, [])
  );

  // Search effect
  useEffect(() => {
    if (searchQuery.length > 2 || searchQuery.length === 0) {
      searchUsers();
    }
  }, [searchQuery, filterType]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      await loadUsers(true);
    } catch (error) {
      console.error('Error loading initial data:', error);
      Alert.alert('Error', 'Failed to load users. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const buildDomain = () => {
    let domain = [['active', '=', true]];

    if (filterType === 'internal') {
      domain.push(['share', '=', false]);
    } else if (filterType === 'portal') {
      domain.push(['share', '=', true]);
    }

    return domain;
  };

  const loadUsers = async (reset = false) => {
    try {
      if (reset) {
        setCurrentPage(0);
        setHasMore(true);
      }

      const offset = reset ? 0 : currentPage * 20;
      const domain = buildDomain();

      const usersData = await usersAPI.getUsers({
        domain,
        limit: 20,
        offset,
        order: 'name asc'
      });

      if (usersData && usersData.length > 0) {
        if (reset) {
          setUsers(usersData);
        } else {
          setUsers(prev => [...prev, ...usersData]);
        }

        setHasMore(usersData.length === 20);
        setCurrentPage(prev => reset ? 1 : prev + 1);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error loading users:', error);
      throw error;
    }
  };

  const searchUsers = async () => {
    try {
      setLoading(true);
      let usersData;

      if (searchQuery.trim()) {
        usersData = await usersAPI.searchUsers(searchQuery, {
          limit: 50
        });
      } else {
        const domain = buildDomain();
        usersData = await usersAPI.getUsers({
          domain,
          limit: 20,
          order: 'name asc'
        });
      }

      setUsers(usersData || []);
      setCurrentPage(1);
      setHasMore(searchQuery.trim() ? false : true);
    } catch (error) {
      console.error('Error searching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadUsers(true);
    } catch (error) {
      // Error already handled in loadUsers
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = async () => {
    if (!hasMore || loadingMore || searchQuery.trim()) return;

    try {
      setLoadingMore(true);
      await loadUsers(false);
    } catch (error) {
      // Error already handled in loadUsers
    } finally {
      setLoadingMore(false);
    }
  };

  const handleUserPress = (user) => {
    if (isSelectionMode) {
      toggleUserSelection(user);
    } else {
      navigation.navigate('UserDetail', {
        userId: user.id,
        userName: user.name
      });
    }
  };

  const handleUserLongPress = (user) => {
    if (!isSelectionMode) {
      setIsSelectionMode(true);
      setSelectedUsers([user.id]);
    }
  };

  const toggleUserSelection = (user) => {
    setSelectedUsers(prev => {
      if (prev.includes(user.id)) {
        const newSelection = prev.filter(id => id !== user.id);
        if (newSelection.length === 0) {
          setIsSelectionMode(false);
        }
        return newSelection;
      } else {
        return [...prev, user.id];
      }
    });
  };

  const clearSelection = () => {
    setSelectedUsers([]);
    setIsSelectionMode(false);
  };

  const showUserDetails = (user) => {
    setSelectedUser(user);
    bottomSheetRef.current?.expand();
  };

  const renderUserItem = ({ item, index }) => (
    <UserCard
      user={item}
      viewMode={viewMode}
      onPress={() => handleUserPress(item)}
      onLongPress={() => handleUserLongPress(item)}
      onDetailsPress={() => showUserDetails(item)}
      isSelected={selectedUsers.includes(item.id)}
      isSelectionMode={isSelectionMode}
      index={index}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <Icon name="magnify" size={20} color={colors.textSecondary} style={styles.searchIcon} />
        <TextInput
          style={[styles.searchInput, { color: colors.text }]}
          placeholder="Search users..."
          placeholderTextColor={colors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
          returnKeyType="search"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Icon name="close-circle" size={18} color={colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>

      {/* Filter Chips */}
      <View style={styles.filtersContainer}>
        <TouchableOpacity
          style={[
            styles.filterChip,
            {
              backgroundColor: filterType === 'all' ? colors.primary : colors.surface,
              borderColor: filterType === 'all' ? colors.primary : colors.border
            }
          ]}
          onPress={() => setFilterType('all')}
        >
          <Text style={[
            styles.filterChipText,
            { color: filterType === 'all' ? colors.onPrimary : colors.text }
          ]}>
            All Users
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterChip,
            {
              backgroundColor: filterType === 'internal' ? colors.primary : colors.surface,
              borderColor: filterType === 'internal' ? colors.primary : colors.border
            }
          ]}
          onPress={() => setFilterType('internal')}
        >
          <Icon
            name="account-star"
            size={16}
            color={filterType === 'internal' ? colors.onPrimary : colors.text}
          />
          <Text style={[
            styles.filterChipText,
            { color: filterType === 'internal' ? colors.onPrimary : colors.text }
          ]}>
            Internal
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterChip,
            {
              backgroundColor: filterType === 'portal' ? colors.primary : colors.surface,
              borderColor: filterType === 'portal' ? colors.primary : colors.border
            }
          ]}
          onPress={() => setFilterType('portal')}
        >
          <Icon
            name="account-circle"
            size={16}
            color={filterType === 'portal' ? colors.onPrimary : colors.text}
          />
          <Text style={[
            styles.filterChipText,
            { color: filterType === 'portal' ? colors.onPrimary : colors.text }
          ]}>
            Portal
          </Text>
        </TouchableOpacity>
      </View>

      {/* View Controls */}
      <View style={[styles.viewControls, { backgroundColor: colors.surface }]}>
        <View style={styles.viewModeToggle}>
          <TouchableOpacity
            style={[
              styles.viewModeButton,
              viewMode === 'grid' && { backgroundColor: colors.primary }
            ]}
            onPress={() => setViewMode('grid')}
          >
            <Icon
              name="view-grid"
              size={20}
              color={viewMode === 'grid' ? colors.onPrimary : colors.text}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.viewModeButton,
              viewMode === 'list' && { backgroundColor: colors.primary }
            ]}
            onPress={() => setViewMode('list')}
          >
            <Icon
              name="view-list"
              size={20}
              color={viewMode === 'list' ? colors.onPrimary : colors.text}
            />
          </TouchableOpacity>
        </View>

        {/* Results Count */}
        <Text style={[styles.resultsCount, { color: colors.textSecondary }]}>
          {users.length} user{users.length !== 1 ? 's' : ''}
        </Text>
      </View>

      {/* Selection Mode Header */}
      {isSelectionMode && (
        <View style={[styles.selectionHeader, { backgroundColor: colors.primary }]}>
          <View style={styles.selectionInfo}>
            <TouchableOpacity onPress={clearSelection}>
              <Icon name="close" size={24} color={colors.onPrimary} />
            </TouchableOpacity>
            <Text style={[styles.selectionText, { color: colors.onPrimary }]}>
              {selectedUsers.length} selected
            </Text>
          </View>
          <View style={styles.selectionActions}>
            <TouchableOpacity
              style={styles.selectionAction}
              onPress={() => Alert.alert('Export', 'Export functionality coming soon')}
            >
              <Icon name="export" size={20} color={colors.onPrimary} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.selectionAction}
              onPress={() => Alert.alert('Deactivate', 'Deactivate users functionality coming soon')}
            >
              <Icon name="account-off" size={20} color={colors.onPrimary} />
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );

  const renderFooter = () => {
    if (!loadingMore) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading more users...
        </Text>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="account-star" size={64} color={colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        {searchQuery ? 'No users found' : 'No users available'}
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        {searchQuery
          ? `Try adjusting your search or filters`
          : 'Users will appear here once they are added'
        }
      </Text>
      {searchQuery && (
        <TouchableOpacity
          style={[styles.clearSearchButton, { backgroundColor: colors.primary }]}
          onPress={() => setSearchQuery('')}
        >
          <Text style={[styles.clearSearchText, { color: colors.onPrimary }]}>
            Clear Search
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (loading && users.length === 0) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading users...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <FlatList
        data={users}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderUserItem}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.3}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode} // Force re-render when view mode changes
        contentContainerStyle={users.length === 0 ? styles.emptyContainer : undefined}
        showsVerticalScrollIndicator={false}
      />

      {/* Floating Action Button */}
      <TouchableOpacity
        style={[styles.fab, { backgroundColor: colors.primary }]}
        onPress={() => navigation.navigate('UserEdit', { isEditing: false })}
      >
        <Icon name="plus" size={28} color={colors.onPrimary} />
      </TouchableOpacity>

      {/* User Details Bottom Sheet */}
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: colors.surface }}
        handleIndicatorStyle={{ backgroundColor: colors.border }}
      >
        <BottomSheetScrollView contentContainerStyle={styles.bottomSheetContent}>
          {selectedUser && (
            <UserDetails
              user={selectedUser}
              onClose={() => bottomSheetRef.current?.close()}
              onEdit={() => {
                bottomSheetRef.current?.close();
                navigation.navigate('UserEdit', {
                  userId: selectedUser.id,
                  isEditing: true
                });
              }}
              onToggleActive={() => {
                bottomSheetRef.current?.close();
                Alert.alert('Toggle Active', 'User activation toggle coming soon');
              }}
              onResetPassword={() => {
                bottomSheetRef.current?.close();
                Alert.alert('Password Reset', 'Password reset functionality coming soon');
              }}
            />
          )}
        </BottomSheetScrollView>
      </BottomSheet>
    </SafeAreaView>
  );
};

// User Details Component for Bottom Sheet
const UserDetails = ({ user, onClose, onEdit, onToggleActive, onResetPassword }) => {
  const { colors } = useTheme();

  const getUserTypeLabel = (isPortal, isActive) => {
    if (!isActive) return 'Inactive';
    return isPortal ? 'Portal User' : 'Internal User';
  };

  const getUserTypeColor = (isPortal, isActive) => {
    if (!isActive) return colors.textSecondary;
    return isPortal ? colors.secondary : colors.primary;
  };

  return (
    <View style={styles.userDetails}>
      <View style={styles.detailsHeader}>
        <Text style={[styles.detailsTitle, { color: colors.text }]}>
          User Details
        </Text>
        <TouchableOpacity onPress={onClose}>
          <Icon name="close" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <Text style={[styles.detailsUserName, { color: colors.text }]}>
        {user.name}
      </Text>

      <Text style={[styles.detailsUserLogin, { color: colors.textSecondary }]}>
        @{user.login}
      </Text>

      <View style={[styles.userTypeBadge, { backgroundColor: getUserTypeColor(user.share, user.active) + '20' }]}>
        <Text style={[styles.userTypeText, { color: getUserTypeColor(user.share, user.active) }]}>
          {getUserTypeLabel(user.share, user.active)}
        </Text>
      </View>

      {user.email && (
        <View style={styles.detailsRow}>
          <Text style={[styles.detailsLabel, { color: colors.textSecondary }]}>
            Email:
          </Text>
          <Text style={[styles.detailsValue, { color: colors.text }]}>
            {user.email}
          </Text>
        </View>
      )}

      {user.company_id && (
        <View style={styles.detailsRow}>
          <Text style={[styles.detailsLabel, { color: colors.textSecondary }]}>
            Company:
          </Text>
          <Text style={[styles.detailsValue, { color: colors.text }]}>
            {user.company_id[1]}
          </Text>
        </View>
      )}

      {user.login_date && (
        <View style={styles.detailsRow}>
          <Text style={[styles.detailsLabel, { color: colors.textSecondary }]}>
            Last Login:
          </Text>
          <Text style={[styles.detailsValue, { color: colors.text }]}>
            {new Date(user.login_date).toLocaleDateString()}
          </Text>
        </View>
      )}

      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.primary }]}
          onPress={onEdit}
        >
          <Icon name="pencil" size={20} color={colors.onPrimary} />
          <Text style={[styles.actionButtonText, { color: colors.onPrimary }]}>
            Edit
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.secondary }]}
          onPress={onResetPassword}
        >
          <Icon name="key" size={20} color={colors.onPrimary} />
          <Text style={[styles.actionButtonText, { color: colors.onPrimary }]}>
            Password
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: user.active ? colors.error : colors.success }]}
          onPress={onToggleActive}
        >
          <Icon
            name={user.active ? "account-off" : "account-check"}
            size={20}
            color={colors.onPrimary}
          />
          <Text style={[styles.actionButtonText, { color: colors.onPrimary }]}>
            {user.active ? 'Deactivate' : 'Activate'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    paddingBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginHorizontal: 16,
    marginVertical: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '400',
    paddingVertical: 0,
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 8,
    marginVertical: 8,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    gap: 4,
  },
  filterChipText: {
    fontSize: 13,
    fontWeight: '500',
  },
  viewControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 8,
  },
  viewModeToggle: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderRadius: 6,
    padding: 2,
  },
  viewModeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  resultsCount: {
    fontSize: 14,
    fontWeight: '500',
  },
  selectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 8,
  },
  selectionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  selectionText: {
    fontSize: 16,
    fontWeight: '600',
  },
  selectionActions: {
    flexDirection: 'row',
    gap: 16,
  },
  selectionAction: {
    padding: 4,
  },
  footerLoader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    gap: 8,
  },
  emptyContainer: {
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 24,
  },
  clearSearchButton: {
    marginTop: 24,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  clearSearchText: {
    fontSize: 16,
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  bottomSheetContent: {
    padding: 16,
  },
  userDetails: {
    gap: 16,
  },
  detailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailsTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  detailsUserName: {
    fontSize: 18,
    fontWeight: '600',
  },
  detailsUserLogin: {
    fontSize: 14,
    fontFamily: 'monospace',
  },
  userTypeBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  userTypeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailsLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  detailsValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 16,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 4,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default UsersListScreen;
