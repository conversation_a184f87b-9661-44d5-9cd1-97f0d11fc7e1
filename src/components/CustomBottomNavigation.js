import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../contexts/ThemeContext';

const CustomBottomNavigation = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { colors } = useTheme();

  // Define navigation items
  const navItems = [
    {
      id: 'home',
      label: 'Home',
      icon: 'view-dashboard',
      route: 'Home',
      activeRoutes: ['Home']
    },
    {
      id: 'contacts',
      label: 'Contacts',
      icon: 'account-group',
      route: 'ContactsList',
      activeRoutes: ['ContactsList', 'ContactDetail', 'ContactForm']
    },
    {
      id: 'chat',
      label: 'Chat',
      icon: 'chat',
      route: 'Discuss',
      activeRoutes: ['Discuss', 'DiscussChat']
    },
    {
      id: 'orders',
      label: 'Orders',
      icon: 'cart',
      route: 'OrdersList', // Placeholder
      activeRoutes: ['OrdersList']
    },
    {
      id: 'helpdesk',
      label: 'Helpdesk',
      icon: 'ticket-account',
      route: 'HelpdeskList',
      activeRoutes: ['HelpdeskList', 'HelpdeskTickets', 'HelpdeskTicketDetail', 'HelpdeskTicketForm']
    }
  ];

  // Check if current route is active
  const isActive = (item) => {
    return item.activeRoutes.includes(route.name);
  };

  // Handle navigation
  const handlePress = (item) => {
    if (item.route === 'OrdersList') {
      // Show placeholder alert for orders
      alert('Orders module coming soon');
      return;
    }
    
    navigation.navigate(item.route);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.surface, borderTopColor: colors.border }]}>
      {navItems.map((item) => {
        const active = isActive(item);
        return (
          <TouchableOpacity
            key={item.id}
            style={styles.navItem}
            onPress={() => handlePress(item)}
            activeOpacity={0.7}
          >
            <Icon
              name={item.icon}
              size={24}
              color={active ? colors.primary : colors.textSecondary}
            />
            <Text
              style={[
                styles.navLabel,
                {
                  color: active ? colors.primary : colors.textSecondary,
                  fontWeight: active ? '600' : '500'
                }
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderTopWidth: 1,
    paddingTop: 8,
    paddingBottom: 8,
    paddingHorizontal: 8,
    height: 70,
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
  },
  navLabel: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
});

export default CustomBottomNavigation;
