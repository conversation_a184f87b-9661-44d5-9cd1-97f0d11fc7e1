// Enhanced Message Thread Component with proper Odoo message_post
// src/components/EnhancedMessageThread.js

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
  Image,
  Dimensions
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../contexts/ThemeContext';
import OdooActionsAPI from '../api/models/odooActionsApi';
import { AttachmentPicker } from './AttachmentPicker';
import { format } from 'date-fns';

const { width } = Dimensions.get('window');

const EnhancedMessageThread = ({ 
  model, 
  recordId, 
  recordName = 'Record',
  enableAttachments = true,
  refreshTrigger = 0 
}) => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [newMessage, setNewMessage] = useState('');
  const [attachments, setAttachments] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const { colors } = useTheme();
  const flatListRef = useRef(null);

  // Load messages when component mounts or refresh trigger changes
  useEffect(() => {
    loadMessages();
  }, [model, recordId, refreshTrigger]);

  /**
   * Load messages using proper Odoo API
   */
  const loadMessages = useCallback(async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      console.log(`Loading messages for ${model} ID ${recordId}`);
      
      // Use the proper Odoo API to get messages
      const messagesData = await OdooActionsAPI.getMessages(model, recordId, 50);
      
      // Transform messages for display
      const transformedMessages = messagesData.map(msg => ({
        id: msg.id,
        body: msg.body || '',
        date: new Date(msg.date),
        author: {
          id: msg.author_id ? msg.author_id[0] : null,
          name: msg.author_id ? msg.author_id[1] : 'System'
        },
        attachmentIds: msg.attachment_ids || [],
        messageType: msg.message_type,
        subtypeId: msg.subtype_id,
        parentId: msg.parent_id
      }));

      // Sort by date (newest first for display)
      transformedMessages.sort((a, b) => b.date - a.date);

      setMessages(transformedMessages);
      console.log(`Loaded ${transformedMessages.length} messages`);

    } catch (error) {
      console.error('Error loading messages:', error);
      Alert.alert(
        'Error', 
        'Failed to load messages. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [model, recordId]);

  /**
   * Send message using proper Odoo message_post function
   */
  const sendMessage = useCallback(async () => {
    if (!newMessage.trim() && attachments.length === 0) {
      Alert.alert('Error', 'Please enter a message or attach a file.');
      return;
    }

    try {
      setSending(true);
      console.log(`Sending message to ${model} ID ${recordId}`);

      // Prepare attachment IDs if any
      const attachmentIds = attachments.map(att => att.id);

      // Use proper Odoo message_post API
      const result = await OdooActionsAPI.postContactMessage(
        recordId, 
        newMessage.trim(), 
        attachmentIds
      );

      console.log('Message sent successfully:', result);

      // Clear the input
      setNewMessage('');
      setAttachments([]);

      // Reload messages to show the new one
      await loadMessages(true);

      // Scroll to top to show the new message
      if (flatListRef.current) {
        flatListRef.current.scrollToOffset({ offset: 0, animated: true });
      }

      // Show success feedback
      Alert.alert('Success', 'Message sent successfully!');

    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert(
        'Error', 
        'Failed to send message. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setSending(false);
    }
  }, [newMessage, attachments, model, recordId]);

  /**
   * Handle attachment selection
   */
  const handleAttachmentAdd = useCallback((attachment) => {
    setAttachments(prev => [...prev, attachment]);
  }, []);

  /**
   * Remove attachment
   */
  const removeAttachment = useCallback((index) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  }, []);

  /**
   * Render individual message
   */
  const renderMessage = useCallback(({ item: message }) => {
    const isSystem = !message.author.id || message.author.name === 'System';
    
    return (
      <View style={[
        styles.messageContainer,
        { borderBottomColor: colors.border }
      ]}>
        <View style={styles.messageHeader}>
          <View style={styles.authorContainer}>
            <View style={[
              styles.avatar,
              { backgroundColor: isSystem ? colors.textSecondary : colors.primary }
            ]}>
              <Text style={[styles.avatarText, { color: colors.onPrimary }]}>
                {message.author.name ? message.author.name.charAt(0).toUpperCase() : 'S'}
              </Text>
            </View>
            <View style={styles.authorInfo}>
              <Text style={[styles.authorName, { color: colors.text }]}>
                {message.author.name}
              </Text>
              <Text style={[styles.messageDate, { color: colors.textSecondary }]}>
                {format(message.date, 'MMM dd, yyyy HH:mm')}
              </Text>
            </View>
          </View>
        </View>

        {/* Message body */}
        {message.body && (
          <View style={styles.messageBody}>
            <Text style={[styles.messageText, { color: colors.text }]}>
              {message.body.replace(/<[^>]*>/g, '')} {/* Strip HTML tags */}
            </Text>
          </View>
        )}

        {/* Attachments */}
        {message.attachmentIds && message.attachmentIds.length > 0 && (
          <View style={styles.attachmentsContainer}>
            {message.attachmentIds.map((attachmentId, index) => (
              <TouchableOpacity
                key={attachmentId}
                style={[styles.attachmentItem, { backgroundColor: colors.surface }]}
                onPress={() => {
                  // Handle attachment download/view
                  console.log('View attachment:', attachmentId);
                }}
              >
                <Icon name="paperclip" size={16} color={colors.primary} />
                <Text style={[styles.attachmentText, { color: colors.text }]}>
                  Attachment {index + 1}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
    );
  }, [colors]);

  /**
   * Render attachment preview
   */
  const renderAttachmentPreview = useCallback(() => {
    if (attachments.length === 0) return null;

    return (
      <View style={[styles.attachmentPreview, { backgroundColor: colors.surface }]}>
        <Text style={[styles.attachmentPreviewTitle, { color: colors.text }]}>
          Attachments ({attachments.length}):
        </Text>
        {attachments.map((attachment, index) => (
          <View key={index} style={styles.attachmentPreviewItem}>
            <Icon name="file" size={16} color={colors.primary} />
            <Text style={[styles.attachmentPreviewName, { color: colors.text }]}>
              {attachment.name}
            </Text>
            <TouchableOpacity onPress={() => removeAttachment(index)}>
              <Icon name="close" size={16} color={colors.error} />
            </TouchableOpacity>
          </View>
        ))}
      </View>
    );
  }, [attachments, colors, removeAttachment]);

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading messages...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Messages - {recordName}
        </Text>
        <TouchableOpacity onPress={() => loadMessages(true)}>
          <Icon name="refresh" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Messages list */}
      <FlatList
        ref={flatListRef}
        data={messages}
        keyExtractor={(item) => `message-${item.id}`}
        renderItem={renderMessage}
        style={styles.messagesList}
        onRefresh={() => loadMessages(true)}
        refreshing={refreshing}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Icon name="message-outline" size={64} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No messages yet. Start the conversation!
            </Text>
          </View>
        )}
      />

      {/* Attachment preview */}
      {renderAttachmentPreview()}

      {/* Message input */}
      <View style={[styles.inputContainer, { backgroundColor: colors.surface, borderTopColor: colors.border }]}>
        <View style={[styles.inputWrapper, { backgroundColor: colors.background, borderColor: colors.border }]}>
          <TextInput
            style={[styles.textInput, { color: colors.text }]}
            value={newMessage}
            onChangeText={setNewMessage}
            placeholder="Type your message..."
            placeholderTextColor={colors.textSecondary}
            multiline
            maxLength={5000}
            editable={!sending}
          />
          
          {enableAttachments && (
            <TouchableOpacity
              style={styles.attachButton}
              onPress={() => {
                // Simple attachment placeholder - you can implement AttachmentPicker later
                Alert.alert('Attachments', 'Attachment feature coming soon!');
              }}
              disabled={sending}
            >
              <Icon name="paperclip" size={20} color={colors.primary} />
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[
              styles.sendButton,
              { 
                backgroundColor: (newMessage.trim() || attachments.length > 0) ? colors.primary : colors.border
              }
            ]}
            onPress={sendMessage}
            disabled={sending || (!newMessage.trim() && attachments.length === 0)}
          >
            {sending ? (
              <ActivityIndicator size="small" color={colors.onPrimary} />
            ) : (
              <Icon name="send" size={20} color={colors.onPrimary} />
            )}
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  messagesList: {
    flex: 1,
  },
  messageContainer: {
    padding: 16,
    borderBottomWidth: 1,
  },
  messageHeader: {
    marginBottom: 8,
  },
  authorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  authorInfo: {
    flex: 1,
  },
  authorName: {
    fontSize: 14,
    fontWeight: '600',
  },
  messageDate: {
    fontSize: 12,
    marginTop: 2,
  },
  messageBody: {
    marginLeft: 44,
  },
  messageText: {
    fontSize: 15,
    lineHeight: 20,
  },
  attachmentsContainer: {
    marginLeft: 44,
    marginTop: 8,
  },
  attachmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 6,
    marginBottom: 4,
  },
  attachmentText: {
    marginLeft: 8,
    fontSize: 14,
  },
  attachmentPreview: {
    margin: 16,
    padding: 12,
    borderRadius: 8,
  },
  attachmentPreviewTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  attachmentPreviewItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  attachmentPreviewName: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
  },
  inputContainer: {
    padding: 16,
    borderTopWidth: 1,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    borderRadius: 24,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
    minHeight: 48,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    maxHeight: 120,
    marginRight: 8,
  },
  attachButton: {
    padding: 8,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
});

export default EnhancedMessageThread;
