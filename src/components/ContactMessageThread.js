// Enhanced Contact Message Thread Component - Following Helpdesk Pattern
// src/components/ContactMessageThread.js

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Image,
  Alert
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../contexts/ThemeContext';
import OdooActionsAPI from '../api/models/odooActionsApi';
import ContactMessageDetailModal from './ContactMessageDetailModal';
import defaultAvatar from '../assets/images/default_avatar.png';

/**
 * Contact Message Thread Component following helpdesk pattern
 * 
 * @param {Object} props - Component props
 * @param {string} props.model - The model name (e.g., 'res.partner')
 * @param {number|string} props.recordId - The record ID
 * @param {string} props.recordName - The record name
 * @param {number} props.refreshTrigger - Trigger to refresh messages
 */
const ContactMessageThread = ({ model, recordId, recordName, refreshTrigger = 0, ...props }) => {
  const { colors } = useTheme();
  const [messages, setMessages] = useState([]);
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('messages'); // 'messages', 'activity', or 'audit'

  // Get messages for each tab
  const getMessagesForTab = () => {
    const allMessages = [...messages, ...activities];
    
    switch (activeTab) {
      case 'messages':
        // Regular messages and emails
        return allMessages
          .filter(item => 
            item.message_type === 'comment' || 
            item.message_type === 'email' ||
            (item.body && item.body.trim() && !item.is_internal)
          )
          .sort((a, b) => new Date(b.date || b.create_date) - new Date(a.date || a.create_date));
          
      case 'activity':
        // Activities, stage changes, assignments, tracking changes
        return allMessages
          .filter(item => {
            if (item.activity_type_id) return true;
            if (item.tracking_value_ids && item.tracking_value_ids.length > 0) return true;
            if (item.message_type === 'notification' && !item.is_internal) return true;
            return false;
          })
          .sort((a, b) => new Date(b.date || b.create_date) - new Date(a.date || a.create_date));
          
      case 'audit':
        // Internal notes, system notifications, automated actions
        return allMessages
          .filter(item => {
            if (item.is_internal) return true;
            if (item.message_type === 'notification' && item.is_internal) return true;
            if (item.subtype_id && item.subtype_id[1] && item.subtype_id[1].includes('note')) return true;
            return false;
          })
          .sort((a, b) => new Date(b.date || b.create_date) - new Date(a.date || a.create_date));
          
      default:
        return [];
    }
  };

  // Format tracking value changes like Odoo
  const formatTrackingChanges = (trackingValues) => {
    if (!trackingValues || trackingValues.length === 0) return null;

    return trackingValues.map(tracking => {
      const fieldName = tracking.field_desc || tracking.field;
      const oldValue = tracking.old_value_text || tracking.old_value || '';
      const newValue = tracking.new_value_text || tracking.new_value || '';

      if (oldValue && newValue) {
        return `${fieldName}: ${oldValue} → ${newValue}`;
      } else if (newValue) {
        return `${fieldName}: ${newValue}`;
      } else if (oldValue) {
        return `${fieldName}: ${oldValue} (removed)`;
      }
      return `${fieldName} changed`;
    }).join('\\n');
  };

  // Fetch messages and activities with proper API calls
  const fetchData = useCallback(async (forceRefresh = false) => {
    try {
      setLoading(true);
      console.log(`Fetching messages for ${model} ID ${recordId}`);

      // Use the proper Odoo API to get messages for this specific record
      const messagesData = await OdooActionsAPI.getMessages(model, recordId, 100);
      console.log(`Loaded ${messagesData.length} messages for ${model} ${recordId}`);
      setMessages(messagesData || []);

      // For now, we'll focus on messages. Activities can be added later if needed
      setActivities([]);
      
    } catch (err) {
      console.error('Error fetching message thread:', err);
      Alert.alert('Error', 'Failed to load messages. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [model, recordId]);

  // Initial data fetch and refresh trigger
  useEffect(() => {
    fetchData();
  }, [fetchData, refreshTrigger]);

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    fetchData(true);
  };

  // Handle message press
  const handleMessagePress = (message) => {
    setSelectedMessage(message);
    setModalVisible(true);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Get author name
  const getAuthorName = (item) => {
    if (item.author_id && Array.isArray(item.author_id)) {
      return item.author_id[1] || 'Unknown';
    }
    return item.email_from || item.create_uid?.[1] || 'System';
  };

  // Get preview text
  const getPreviewText = (item) => {
    // Check for tracking changes first
    if (item.tracking_value_ids && item.tracking_value_ids.length > 0) {
      const trackingText = formatTrackingChanges(item.tracking_value_ids);
      if (trackingText) return trackingText;
    }

    if (item.body) {
      // Strip HTML tags and get first 100 characters
      const text = item.body.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim();
      return text.length > 100 ? text.substring(0, 100) + '...' : text;
    }
    if (item.subject) {
      return item.subject;
    }
    if (item.summary) {
      return item.summary;
    }
    return 'No content';
  };

  // Render message item
  const renderMessageItem = ({ item }) => {
    const isInternalNote = item.message_type === 'notification' || item.is_internal;
    const isAuditNote = item.tracking_value_ids && item.tracking_value_ids.length > 0;
    const isActivity = !item.message_type && item.activity_type_id;
    const isEmail = item.message_type === 'email';

    return (
      <TouchableOpacity
        style={[
          styles.messageItem,
          { backgroundColor: colors.surface },
          isInternalNote && styles.internalNoteItem,
          isAuditNote && styles.auditNoteItem,
          isEmail && styles.emailItem,
        ]}
        onPress={() => handleMessagePress(item)}
      >
        <View style={styles.messageHeader}>
          <View style={styles.authorContainer}>
            <Image source={defaultAvatar} style={styles.avatar} />
            <View style={styles.authorInfo}>
              <Text style={[styles.authorName, { color: colors.text }]}>
                {getAuthorName(item)}
              </Text>
              {isInternalNote && (
                <Text style={[styles.messageTypeLabel, { color: colors.warning }]}>
                  Internal Note
                </Text>
              )}
              {isAuditNote && (
                <Text style={[styles.messageTypeLabel, { color: colors.info }]}>
                  Field Changes
                </Text>
              )}
              {isActivity && (
                <Text style={[styles.messageTypeLabel, { color: colors.success }]}>
                  Activity
                </Text>
              )}
              {isEmail && (
                <Text style={[styles.messageTypeLabel, { color: colors.primary }]}>
                  Email
                </Text>
              )}
            </View>
          </View>
          <Text style={[styles.dateText, { color: colors.textSecondary }]}>
            {formatDate(item.date || item.create_date)}
          </Text>
        </View>

        {item.subject && (
          <Text style={[styles.subjectText, { color: colors.text }]} numberOfLines={1}>
            {item.subject}
          </Text>
        )}

        <Text style={[styles.previewText, { color: colors.textSecondary }]} numberOfLines={2}>
          {getPreviewText(item)}
        </Text>

        {item.attachment_ids && item.attachment_ids.length > 0 && (
          <View style={styles.attachmentIndicator}>
            <Icon name="paperclip" size={14} color={colors.textSecondary} />
            <Text style={[styles.attachmentCount, { color: colors.textSecondary }]}>
              {item.attachment_ids.length} attachment{item.attachment_ids.length !== 1 ? 's' : ''}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  // Handle send new message
  const handleSendMessage = async () => {
    Alert.prompt(
      'Send Message',
      `Send a message to ${recordName}:`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send',
          onPress: async (message) => {
            if (!message || !message.trim()) return;

            try {
              console.log(`Sending message to ${model} ID ${recordId}`);
              
              // Use proper Odoo message_post action
              await OdooActionsAPI.postMessage(model, recordId, message.trim());
              
              Alert.alert('Success', 'Message sent successfully!');
              
              // Refresh messages
              handleRefresh();
            } catch (err) {
              console.error('Error sending message:', err);
              Alert.alert('Error', 'Failed to send message');
            }
          }
        }
      ],
      'plain-text'
    );
  };

  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading messages...
        </Text>
      </View>
    );
  }

  const currentMessages = getMessagesForTab();

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Messages & Activity
        </Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={handleRefresh}
            disabled={refreshing}
          >
            <Icon name="refresh" size={20} color={colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.primary }]}
            onPress={handleSendMessage}
          >
            <Icon name="plus" size={20} color={colors.onPrimary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'messages' && styles.activeTab,
            { borderBottomColor: activeTab === 'messages' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setActiveTab('messages')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'messages' ? colors.primary : colors.textSecondary }
          ]}>
            Messages
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'activity' && styles.activeTab,
            { borderBottomColor: activeTab === 'activity' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setActiveTab('activity')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'activity' ? colors.primary : colors.textSecondary }
          ]}>
            Activity
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'audit' && styles.activeTab,
            { borderBottomColor: activeTab === 'audit' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setActiveTab('audit')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'audit' ? colors.primary : colors.textSecondary }
          ]}>
            Audit
          </Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={currentMessages}
        renderItem={renderMessageItem}
        keyExtractor={(item) => `${item.id}-${item.date || item.create_date}-${activeTab}`}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="message-text-outline" size={48} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No {activeTab} yet for this contact
            </Text>
            {activeTab === 'messages' && (
              <TouchableOpacity
                style={[styles.emptyActionButton, { backgroundColor: colors.primary }]}
                onPress={handleSendMessage}
              >
                <Text style={[styles.emptyActionText, { color: colors.onPrimary }]}>
                  Send First Message
                </Text>
              </TouchableOpacity>
            )}
          </View>
        }
        ItemSeparatorComponent={() => <View style={{ height: 1, backgroundColor: colors.border }} />}
      />

      <ContactMessageDetailModal
        visible={modalVisible}
        message={selectedMessage}
        onClose={() => {
          setModalVisible(false);
          setSelectedMessage(null);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  refreshButton: {
    padding: 8,
  },
  addButton: {
    padding: 8,
    borderRadius: 16,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    borderBottomWidth: 2,
  },
  activeTab: {
    backgroundColor: '#fff',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  listContent: {
    paddingVertical: 8,
  },
  messageItem: {
    padding: 12,
    borderRadius: 4,
    marginHorizontal: 16,
    marginVertical: 2,
    backgroundColor: '#fff',
    borderLeftWidth: 3,
    borderLeftColor: '#e0e0e0',
  },
  internalNoteItem: {
    borderLeftWidth: 4,
    borderLeftColor: '#f39c12',
  },
  auditNoteItem: {
    borderLeftWidth: 4,
    borderLeftColor: '#3498db',
  },
  emailItem: {
    borderLeftWidth: 4,
    borderLeftColor: '#27ae60',
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  authorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 12,
  },
  authorInfo: {
    flex: 1,
  },
  authorName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  messageTypeLabel: {
    fontSize: 10,
    fontWeight: '500',
    textTransform: 'uppercase',
  },
  dateText: {
    fontSize: 12,
    marginLeft: 8,
  },
  subjectText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  previewText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  attachmentIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  attachmentCount: {
    fontSize: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  emptyActionButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  emptyActionText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default ContactMessageThread;
