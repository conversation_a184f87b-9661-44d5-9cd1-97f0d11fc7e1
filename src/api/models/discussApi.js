// Simplified Discuss API - Removed domain filtering for testing

import api from '../../../services/api';
import { searchRelated, clearModelCache } from '../../../services/api';

/**
 * Simplified Discuss Channel API implementation
 * Basic functionality without complex domain filtering
 */
class DiscussChannelAPI {
  constructor() {
    this.model = 'discuss.channel';
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes

    // Clear cache on startup to ensure fresh data
    this.clearCache();
  }

  /**
   * Test basic API connectivity
   */
  async testApiConnectivity() {
    console.log('Testing basic API connectivity...');

    try {
      // Test basic user endpoint first
      const userResponse = await api.get('/api/v2/user');
      console.log('✅ Basic API connectivity working - user endpoint accessible');
      return { success: true, userResponse: userResponse.data };
    } catch (error) {
      console.log('❌ Basic API connectivity failed:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Test what models are available for chat functionality
   */
  async testAvailableModels() {
    console.log('Testing available chat models...');

    // First test basic connectivity
    const connectivityTest = await this.testApiConnectivity();
    if (!connectivityTest.success) {
      console.log('❌ Skipping model tests due to connectivity issues');
      return { connectivity: connectivityTest };
    }

    const modelsToTest = [
      'mail.channel',
      'discuss.channel',
      'im_livechat.channel',
      'mail.thread',
      'res.partner'
    ];

    const results = { connectivity: connectivityTest };

    for (const model of modelsToTest) {
      try {
        console.log(`Testing model: ${model}`);
        const response = await api.get(`/api/v2/search_read/${model}`, {
          params: {
            fields: JSON.stringify(['id', 'name']),
            limit: 1
          }
        });
        results[model] = { success: true, count: response.data?.length || 0 };
        console.log(`✅ ${model} - SUCCESS: Found ${response.data?.length || 0} records`);
      } catch (error) {
        results[model] = { success: false, error: error.message };
        console.log(`❌ ${model} - FAILED: ${error.message}`);
      }
    }

    return results;
  }

  /**
   * Get channels - simplified version without domain filtering
   * GET /api/v2/search_read/discuss.channel
   */
  async getChannels(options = {}) {
    const {
      forceRefresh = false,
      limit = 50,
      offset = 0,
      order = 'write_date desc'
    } = options;

    try {
      const cacheKey = `channels_${limit}_${offset}`;

      // Check cache first
      if (!forceRefresh && this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          console.log('Returning cached channels');
          return cached.data;
        }
      }

      console.log('Fetching channels from API (testing multiple models)');

      // Test available models first
      const modelResults = await this.testAvailableModels();
      console.log('Model test results:', modelResults);

      const fields = [
        'id', 'name', 'description', 'channel_type', 'public',
        'member_count', 'create_date', 'write_date'
      ];

      // Try multiple possible models based on test results
      const modelsToTry = ['mail.channel', 'discuss.channel'];
      let response = null;
      let workingModel = null;

      for (const model of modelsToTry) {
        if (modelResults[model]?.success) {
          try {
            console.log(`Trying working model: ${model}`);
            response = await api.get(`/api/v2/search_read/${model}`, {
              params: {
                fields: JSON.stringify(fields),
                limit,
                offset,
                order
              }
            });
            workingModel = model;
            console.log(`✅ Successfully used model: ${model}`);
            break;
          } catch (error) {
            console.log(`❌ Failed with model ${model}: ${error.message}`);
          }
        }
      }

      if (!response) {
        console.log('All direct models failed, trying fallback call method...');
        // Fallback to call method
        const fallbackResponse = await api.post('/api/v2/call', {
          model: 'mail.channel',
          method: 'search_read',
          args: [[]],
          kwargs: {
            fields: ['id', 'name', 'description', 'channel_type', 'public', 'member_count', 'create_date', 'write_date'],
            limit,
            offset,
            order: 'write_date desc'
          }
        });
        response = { data: fallbackResponse.data?.result || [] };
        workingModel = 'mail.channel (via call)';
      }

      let channels = response.data || [];

      // Filter on the client side to only show channels (not chats)
      channels = channels.filter(channel =>
        channel.channel_type === 'channel' || !channel.channel_type
      );

      // Process channels to add display information
      const processedChannels = channels.map(channel => ({
        ...channel,
        displayName: channel.name || `Channel ${channel.id}`,
        subtitle: channel.description || `${channel.member_count || 0} members`,
        hasUnread: false, // Simplified for now
        unreadCount: 0,
        lastActivity: channel.write_date,
        isChannel: true,
        avatarText: this.getChannelAvatar(channel.name),
        avatarColor: this.getChannelColor(channel.id)
      }));

      // Cache the results
      this.cache.set(cacheKey, {
        data: processedChannels,
        timestamp: Date.now()
      });

      console.log(`Retrieved ${processedChannels.length} channels`);
      return processedChannels;
    } catch (error) {
      console.error('Discuss Channel API Error:', error);
      console.error('Error details:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error message:', error.message);

      // Fallback: try to get channels using direct call method
      try {
        console.log('Trying fallback method for channels');
        const fallbackResponse = await api.post('/api/v2/call', {
          model: 'mail.channel',
          method: 'search_read',
          args: [[]],
          kwargs: {
            fields: ['id', 'name', 'description', 'channel_type', 'public', 'member_count', 'create_date', 'write_date'],
            limit,
            offset,
            order: 'write_date desc'
          }
        });

        let fallbackChannels = fallbackResponse.data?.result || [];

        // Filter for channels only
        fallbackChannels = fallbackChannels.filter(channel =>
          channel.channel_type === 'channel' || !channel.channel_type
        );

        const processedFallbackChannels = fallbackChannels.map(channel => ({
          ...channel,
          displayName: channel.name || `Channel ${channel.id}`,
          subtitle: channel.description || `${channel.member_count || 0} members`,
          hasUnread: false,
          unreadCount: 0,
          lastActivity: channel.write_date,
          isChannel: true,
          avatarText: this.getChannelAvatar(channel.name),
          avatarColor: this.getChannelColor(channel.id)
        }));

        console.log(`Fallback method retrieved ${processedFallbackChannels.length} channels`);
        return processedFallbackChannels;
      } catch (fallbackError) {
        console.error('Fallback method also failed:', fallbackError);
        return [];
      }
    }
  }

  /**
   * Get direct messages - simplified without domain filtering for testing
   */
  async getDirectMessages(options = {}) {
    const {
      forceRefresh = false,
      limit = 50,
      offset = 0,
      order = 'write_date desc'
    } = options;

    try {
      const cacheKey = `direct_messages_${limit}_${offset}`;

      // Check cache first
      if (!forceRefresh && this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          console.log('Returning cached direct messages');
          return cached.data;
        }
      }

      console.log('Fetching direct messages from API (testing models)');

      const fields = [
        'id', 'name', 'channel_type', 'create_date', 'write_date'
      ];

      // Try multiple possible models
      const modelsToTry = ['mail.channel', 'discuss.channel'];
      let response = null;

      for (const model of modelsToTry) {
        try {
          console.log(`Trying model for direct messages: ${model}`);
          response = await api.get(`/api/v2/search_read/${model}`, {
            params: {
              fields: JSON.stringify(fields),
              limit,
              offset,
              order
            }
          });
          console.log(`✅ Successfully used model for direct messages: ${model}`);
          break;
        } catch (error) {
          console.log(`❌ Failed with model ${model}: ${error.message}`);
        }
      }

      if (!response) {
        console.log('All direct models failed for direct messages, trying fallback...');
        // Fallback to call method
        const fallbackResponse = await api.post('/api/v2/call', {
          model: 'mail.channel',
          method: 'search_read',
          args: [[]],
          kwargs: {
            fields: ['id', 'name', 'channel_type', 'create_date', 'write_date'],
            limit,
            offset,
            order: 'write_date desc'
          }
        });
        response = { data: fallbackResponse.data?.result || [] };
      }

      let directMessages = response.data || [];

      // Simple client-side filtering to only show direct messages
      directMessages = directMessages.filter(dm => dm.channel_type === 'chat');

      // Process direct messages to add display information
      const processedDirectMessages = directMessages.map((dm) => ({
        ...dm,
        displayName: dm.name || `Chat ${dm.id}`,
        subtitle: 'Direct message',
        hasUnread: false,
        unreadCount: 0,
        lastActivity: dm.write_date,
        isDirectMessage: true,
        avatarText: this.getPersonAvatar(dm.name || 'Chat'),
        avatarColor: this.getPersonColor(dm.id)
      }));

      // Cache the results
      this.cache.set(cacheKey, {
        data: processedDirectMessages,
        timestamp: Date.now()
      });

      console.log(`Retrieved ${processedDirectMessages.length} direct messages`);
      return processedDirectMessages;
    } catch (error) {
      console.error('Discuss Channel API Error:', error);
      console.error('Error details:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error message:', error.message);

      // Try fallback method for direct messages
      try {
        console.log('Trying fallback method for direct messages');
        const fallbackResponse = await api.post('/api/v2/call', {
          model: 'mail.channel',
          method: 'search_read',
          args: [[]],
          kwargs: {
            fields: ['id', 'name', 'channel_type', 'create_date', 'write_date'],
            limit: 50,
            offset: 0,
            order: 'write_date desc'
          }
        });

        let fallbackDirectMessages = fallbackResponse.data?.result || [];

        // Filter for direct messages only
        fallbackDirectMessages = fallbackDirectMessages.filter(dm => dm.channel_type === 'chat');

        const processedFallbackDirectMessages = fallbackDirectMessages.map((dm) => ({
          ...dm,
          displayName: dm.name || `Chat ${dm.id}`,
          subtitle: 'Direct message',
          hasUnread: false,
          unreadCount: 0,
          lastActivity: dm.write_date,
          isDirectMessage: true,
          avatarText: this.getPersonAvatar(dm.name || 'Chat'),
          avatarColor: this.getPersonColor(dm.id)
        }));

        console.log(`Fallback method retrieved ${processedFallbackDirectMessages.length} direct messages`);
        return processedFallbackDirectMessages;
      } catch (fallbackError) {
        console.error('Fallback method also failed:', fallbackError);
        return [];
      }
    }
  }

  /**
   * Get messages for a channel - simple working version
   */
  async getChannelMessages(channelId, forceRefresh = false) {
    try {
      console.log(`Fetching messages for channel ${channelId}`);

      const fields = [
        'id', 'body', 'author_id', 'create_date', 'write_date',
        'message_type', 'attachment_ids', 'subject', 'email_from'
      ];

      // Try multiple approaches to get channel-specific messages
      let messages = [];

      // Approach 1: Try using channel_ids field with mail.message
      try {
        console.log(`Trying approach 1: channel_ids contains ${channelId}`);
        const response1 = await api.get('/api/v2/search_read/mail.message', {
          params: {
            domain: JSON.stringify([
              ['channel_ids', 'in', [parseInt(channelId)]],
              ['message_type', 'in', ['comment', 'email']]
            ]),
            fields: JSON.stringify(fields),
            limit: 50,
            offset: 0,
            order: 'date desc'
          }
        });

        messages = response1.data || [];
        console.log(`Approach 1 found ${messages.length} messages`);

        if (messages.length > 0) {
          console.log(`Using approach 1 - channel_ids filter`);
        }
      } catch (error) {
        console.log(`Approach 1 failed:`, error.message);
      }

      // Approach 2: Try with discuss.channel model (since that's what works)
      if (messages.length === 0) {
        try {
          console.log(`Trying approach 2: model + res_id with discuss.channel for channel ${channelId}`);
          const response2 = await api.get('/api/v2/search_read/mail.message', {
            params: {
              domain: JSON.stringify([
                ['model', '=', 'discuss.channel'],
                ['res_id', '=', parseInt(channelId)],
                ['message_type', 'in', ['comment', 'email']]
              ]),
              fields: JSON.stringify(fields),
              limit: 50,
              offset: 0,
              order: 'date desc'
            }
          });

          messages = response2.data || [];
          console.log(`Approach 2 found ${messages.length} messages`);

          if (messages.length > 0) {
            console.log(`Using approach 2 - discuss.channel model/res_id filter`);
          }
        } catch (error) {
          console.log(`Approach 2 failed:`, error.message);
        }
      }

      // Approach 3: Get channel details using discuss.channel and use message_ids
      if (messages.length === 0) {
        try {
          console.log(`Trying approach 3: get discuss.channel message_ids for channel ${channelId}`);
          const channelResponse = await api.get('/api/v2/search_read/discuss.channel', {
            params: {
              domain: JSON.stringify([['id', '=', parseInt(channelId)]]),
              fields: JSON.stringify(['id', 'name', 'message_ids']),
              limit: 1
            }
          });

          const channel = channelResponse.data?.[0];
          if (channel && channel.message_ids && channel.message_ids.length > 0) {
            console.log(`Channel has ${channel.message_ids.length} message IDs:`, channel.message_ids.slice(0, 5));

            const messageResponse = await api.get('/api/v2/search_read/mail.message', {
              params: {
                domain: JSON.stringify([
                  ['id', 'in', channel.message_ids],
                  ['message_type', 'in', ['comment', 'email']]
                ]),
                fields: JSON.stringify(fields),
                limit: 50,
                order: 'date desc'
              }
            });

            messages = messageResponse.data || [];
            console.log(`Approach 3 found ${messages.length} messages via discuss.channel message_ids`);
          } else {
            console.log(`Channel ${channelId} has no message_ids or channel not found`);
          }
        } catch (error) {
          console.log(`Approach 3 failed:`, error.message);
        }
      }

      // Process the messages we found
      const processedMessages = this.processMessages(messages);
      console.log(`Final result: ${processedMessages.length} messages for channel ${channelId}`);

      // Log first message for debugging
      if (processedMessages.length > 0) {
        console.log(`First message preview:`, {
          id: processedMessages[0].id,
          body: processedMessages[0].cleanBody?.substring(0, 50) + '...',
          author: processedMessages[0].authorName,
          date: processedMessages[0].displayDate
        });
      }

      return processedMessages;

    } catch (error) {
      console.error(`Error fetching messages for channel ${channelId}:`, error);
      // Return some dummy messages for testing
      return [
        {
          id: 1,
          body: 'Welcome to the channel! This is a test message.',
          author_id: [1, 'System'],
          create_date: new Date().toISOString(),
          authorName: 'System',
          displayDate: 'now',
          cleanBody: 'Welcome to the channel! This is a test message.',
          attachmentCount: 0
        }
      ];
    }
  }

  /**
   * Process messages for display
   */
  processMessages(messages) {
    return messages.map(message => ({
      ...message,
      authorName: this.getAuthorName(message),
      displayDate: this.formatMessageDate(message.date || message.create_date),
      cleanBody: this.cleanMessageBody(message.body),
      attachmentCount: message.attachment_ids ? message.attachment_ids.length : 0
    }));
  }

  /**
   * Send message to channel - simplified version
   */
  async sendChannelMessage(channelId, messageBody, attachmentIds = []) {
    try {
      console.log(`Sending message to channel ${channelId}`);

      // Try the message_post method via call API
      const response = await api.post('/api/v2/call', {
        model: 'mail.channel',
        method: 'message_post',
        args: [[parseInt(channelId)]],
        kwargs: {
          body: messageBody,
          message_type: 'comment',
          attachment_ids: attachmentIds
        }
      });

      if (response.data && response.data.result) {
        console.log(`Successfully sent message to channel ${channelId}`);
        return response.data.result;
      }

      // Alternative method: create message directly
      const fallbackResponse = await api.post('/api/v2/create/mail.message', {
        values: {
          body: messageBody,
          model: 'mail.channel',
          res_id: parseInt(channelId),
          message_type: 'comment',
          attachment_ids: attachmentIds.length > 0 ? [[6, 0, attachmentIds]] : false
        }
      });

      if (fallbackResponse.data) {
        console.log(`Successfully sent message via fallback method`);
        return fallbackResponse.data;
      }

      throw new Error('No response from server');
    } catch (error) {
      console.error('Error sending message:', error);
      console.error('Error details:', error.response?.data);
      throw new Error(`Failed to send message: ${error.message}`);
    }
  }

  /**
   * Create new channel - simplified version
   */
  async createChannel(name, description = '', isPublic = true) {
    try {
      console.log(`Creating new channel: ${name}`);

      const channelData = {
        name: name.trim(),
        description: description.trim(),
        channel_type: 'channel',
        public: isPublic ? 'public' : 'private'
      };

      const response = await api.post('/api/v2/create/mail.channel', {
        values: channelData
      });

      if (response.data) {
        // Clear channels cache
        this.clearChannelsCache();
        console.log(`Successfully created channel with ID: ${response.data}`);
        return response.data;
      }

      throw new Error('No response from server');
    } catch (error) {
      console.error('Error creating channel:', error);
      throw new Error(`Failed to create channel: ${error.message}`);
    }
  }

  /**
   * Search conversations - simplified version
   */
  async searchConversations(searchText, options = {}) {
    const { limit = 20 } = options;

    try {
      // Simple search without complex domain filtering
      const fields = [
        'id', 'name', 'channel_type', 'description', 'write_date'
      ];

      const response = await api.get('/api/v2/search_read/mail.channel', {
        params: {
          fields: JSON.stringify(fields),
          limit,
          order: 'write_date desc'
        }
      });

      const results = response.data || [];

      // Filter results on client side
      const filteredResults = results.filter(item =>
        (item.name && item.name.toLowerCase().includes(searchText.toLowerCase())) ||
        (item.description && item.description.toLowerCase().includes(searchText.toLowerCase()))
      );

      // Process results
      const processedResults = filteredResults.map((item) => {
        if (item.channel_type === 'channel') {
          return {
            ...item,
            displayName: item.name,
            subtitle: 'Channel',
            isChannel: true,
            avatarText: this.getChannelAvatar(item.name),
            avatarColor: this.getChannelColor(item.id)
          };
        } else {
          return {
            ...item,
            displayName: item.name || `Chat ${item.id}`,
            subtitle: 'Direct message',
            isDirectMessage: true,
            avatarText: this.getPersonAvatar(item.name || 'Chat'),
            avatarColor: this.getPersonColor(item.id)
          };
        }
      });

      return processedResults;
    } catch (error) {
      console.error('Error searching conversations:', error);
      return [];
    }
  }

  /**
   * Helper methods
   */
  getAuthorName(message) {
    if (message.author_id && Array.isArray(message.author_id)) {
      return message.author_id[1] || 'Unknown';
    }
    return message.email_from || 'Unknown';
  }

  formatMessageDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) {
      return 'now';
    } else if (diffMins < 60) {
      return `${diffMins}m`;
    } else if (diffHours < 24) {
      return `${diffHours}h`;
    } else if (diffDays === 1) {
      return 'yesterday';
    } else if (diffDays < 7) {
      return `${diffDays}d`;
    } else {
      return date.toLocaleDateString();
    }
  }

  cleanMessageBody(body) {
    if (!body) return '';
    return body.replace(/<[^>]*>/g, '').trim();
  }

  getChannelAvatar(channelName) {
    if (!channelName) return '#';
    return channelName.charAt(0).toUpperCase();
  }

  getPersonAvatar(personName) {
    if (!personName) return '?';
    const names = personName.split(' ');
    if (names.length >= 2) {
      return (names[0].charAt(0) + names[1].charAt(0)).toUpperCase();
    }
    return personName.charAt(0).toUpperCase();
  }

  getChannelColor(channelId) {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
    return colors[channelId % colors.length];
  }

  getPersonColor(id) {
    const colors = ['#FF8A80', '#82B1FF', '#B39DDB', '#A5D6A7', '#FFCC02', '#FFAB91', '#80DEEA'];
    return colors[id % colors.length];
  }

  /**
   * Cache management
   */
  clearCache() {
    this.cache.clear();
    clearModelCache(this.model);
  }

  clearChannelsCache() {
    for (const key of this.cache.keys()) {
      if (key.startsWith('channels_')) {
        this.cache.delete(key);
      }
    }
  }

  clearDirectMessagesCache() {
    for (const key of this.cache.keys()) {
      if (key.startsWith('direct_messages_')) {
        this.cache.delete(key);
      }
    }
  }
}

// Create and export singleton instance
const discussChannelAPI = new DiscussChannelAPI();

export { discussChannelAPI };
export default discussChannelAPI;