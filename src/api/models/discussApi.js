// Fixed API for discuss.channel model - Proper domain filtering for user's channels only

import api from '../../../services/api';
import { searchRelated, clearModelCache } from '../../../services/api';
import { getSessionInfo } from '../../../services/auth';

/**
 * Complete Discuss Channel API implementation with proper domain filtering
 * Only shows channels and direct messages for the current authenticated user
 */
class DiscussChannelAPI {
  constructor() {
    this.model = 'discuss.channel';
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get current user ID for domain filtering
   */
  async getCurrentUserId() {
    try {
      const sessionInfo = await getSessionInfo();
      return sessionInfo?.userId || sessionInfo?.uid || 1;
    } catch (error) {
      console.error('Error getting current user ID:', error);
      return 1; // Fallback to user ID 1
    }
  }

  /**
   * Get channels that the current user is a member of
   * GET /api/v2/search_read/discuss.channel
   */
  async getChannels(options = {}) {
    const {
      forceRefresh = false,
      limit = 50,
      offset = 0,
      order = 'last_interest_dt desc, name asc'
    } = options;

    try {
      const cacheKey = `channels_${limit}_${offset}`;
      
      // Check cache first
      if (!forceRefresh && this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          console.log('Returning cached channels');
          return cached.data;
        }
      }

      console.log('Fetching user channels from API');
      
      // Get current user ID for proper filtering
      const currentUserId = await this.getCurrentUserId();
      
      // Proper domain filter for channels user is member of
      const domain = [
        '&',
        ['channel_type', '=', 'channel'], // Only channels (not direct messages)
        '&',
        ['channel_member_ids.partner_id.user_ids', 'in', [currentUserId]], // User is a member
        ['channel_member_ids.fold_state', '!=', 'folded'] // Not folded/hidden
      ];

      const fields = [
        'id', 'name', 'description', 'channel_type', 'public', 
        'channel_member_ids', 'message_ids', 'member_count', 
        'last_interest_dt', 'last_message_id', 'create_date', 
        'write_date', 'uuid', 'channel_partner_ids', 'group_public_id',
        'message_needaction_counter', 'message_unread_counter', 'is_member'
      ];

      const response = await api.get('/api/v2/search_read/discuss.channel', {
        params: {
          domain: JSON.stringify(domain),
          fields: JSON.stringify(fields),
          limit,
          offset,
          order
        }
      });

      const channels = response.data || [];
      
      // Process channels to add display information
      const processedChannels = channels.map(channel => ({
        ...channel,
        displayName: channel.name || `Channel ${channel.id}`,
        subtitle: channel.description || `${channel.member_count || 0} members`,
        hasUnread: (channel.message_unread_counter || 0) > 0,
        unreadCount: channel.message_unread_counter || 0,
        lastActivity: channel.last_interest_dt || channel.write_date,
        isChannel: true,
        avatarText: this.getChannelAvatar(channel.name),
        avatarColor: this.getChannelColor(channel.id)
      }));
      
      // Cache the results
      this.cache.set(cacheKey, {
        data: processedChannels,
        timestamp: Date.now()
      });

      console.log(`Retrieved ${processedChannels.length} user channels`);
      return processedChannels;
    } catch (error) {
      console.error('Error fetching channels:', error);
      throw this.handleError(error, 'Failed to fetch channels');
    }
  }

  /**
   * Get direct messages for the current user
   * GET /api/v2/search_read/discuss.channel
   */
  async getDirectMessages(options = {}) {
    const {
      forceRefresh = false,
      limit = 50,
      offset = 0,
      order = 'last_interest_dt desc, name asc'
    } = options;

    try {
      const cacheKey = `direct_messages_${limit}_${offset}`;
      
      // Check cache first
      if (!forceRefresh && this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          console.log('Returning cached direct messages');
          return cached.data;
        }
      }

      console.log('Fetching user direct messages from API');
      
      // Get current user ID for proper filtering
      const currentUserId = await this.getCurrentUserId();
      
      // Proper domain filter for direct messages user is part of
      const domain = [
        '&',
        ['channel_type', '=', 'chat'], // Only direct messages
        '&',
        ['channel_member_ids.partner_id.user_ids', 'in', [currentUserId]], // User is a member
        ['channel_member_ids.fold_state', '!=', 'folded'] // Not folded/hidden
      ];

      const fields = [
        'id', 'name', 'channel_type', 'channel_member_ids', 
        'channel_partner_ids', 'last_interest_dt', 'last_message_id', 
        'create_date', 'write_date', 'uuid', 'message_needaction_counter', 
        'message_unread_counter', 'is_member'
      ];

      const response = await api.get('/api/v2/search_read/discuss.channel', {
        params: {
          domain: JSON.stringify(domain),
          fields: JSON.stringify(fields),
          limit,
          offset,
          order
        }
      });

      const directMessages = response.data || [];
      
      // Process direct messages to add display information
      const processedDirectMessages = await Promise.all(
        directMessages.map(async (dm) => {
          const displayName = await this.getDirectMessageDisplayName(dm, currentUserId);
          const partnerInfo = await this.getDirectMessagePartnerInfo(dm, currentUserId);
          
          return {
            ...dm,
            displayName,
            subtitle: 'Direct message',
            hasUnread: (dm.message_unread_counter || 0) > 0,
            unreadCount: dm.message_unread_counter || 0,
            lastActivity: dm.last_interest_dt || dm.write_date,
            isDirectMessage: true,
            partnerInfo,
            avatarText: this.getPersonAvatar(displayName),
            avatarColor: this.getPersonColor(dm.id)
          };
        })
      );
      
      // Cache the results
      this.cache.set(cacheKey, {
        data: processedDirectMessages,
        timestamp: Date.now()
      });

      console.log(`Retrieved ${processedDirectMessages.length} user direct messages`);
      return processedDirectMessages;
    } catch (error) {
      console.error('Error fetching direct messages:', error);
      throw this.handleError(error, 'Failed to fetch direct messages');
    }
  }

  /**
   * Get display name for direct message (other participant's name)
   */
  async getDirectMessageDisplayName(dm, currentUserId) {
    try {
      if (dm.name && dm.name !== 'OdooBot') {
        return dm.name;
      }

      // Get partner IDs excluding current user
      if (dm.channel_partner_ids && dm.channel_partner_ids.length > 0) {
        // Fetch partner details to get names
        const partnerResponse = await api.get('/api/v2/read/res.partner', {
          params: {
            ids: JSON.stringify(dm.channel_partner_ids),
            fields: JSON.stringify(['id', 'name', 'user_ids'])
          }
        });

        if (partnerResponse.data) {
          // Find partner who is not the current user
          const otherPartner = partnerResponse.data.find(partner => 
            !partner.user_ids || !partner.user_ids.includes(currentUserId)
          );
          
          if (otherPartner) {
            return otherPartner.name;
          }
        }
      }

      return `Chat ${dm.id}`;
    } catch (error) {
      console.error('Error getting DM display name:', error);
      return `Chat ${dm.id}`;
    }
  }

  /**
   * Get partner info for direct message
   */
  async getDirectMessagePartnerInfo(dm, currentUserId) {
    try {
      if (dm.channel_partner_ids && dm.channel_partner_ids.length > 0) {
        const partnerResponse = await api.get('/api/v2/read/res.partner', {
          params: {
            ids: JSON.stringify(dm.channel_partner_ids),
            fields: JSON.stringify(['id', 'name', 'user_ids', 'email', 'image_128'])
          }
        });

        if (partnerResponse.data) {
          const otherPartner = partnerResponse.data.find(partner => 
            !partner.user_ids || !partner.user_ids.includes(currentUserId)
          );
          
          return otherPartner || null;
        }
      }
      return null;
    } catch (error) {
      console.error('Error getting DM partner info:', error);
      return null;
    }
  }

  /**
   * Get messages for a channel with proper filtering
   */
  async getChannelMessages(channelId, options = {}) {
    const {
      limit = 50,
      offset = 0,
      beforeMessageId = null,
      afterMessageId = null
    } = options;

    try {
      console.log(`Fetching messages for channel ${channelId}`);

      let domain = [
        ['model', '=', 'discuss.channel'],
        ['res_id', '=', parseInt(channelId)]
      ];

      // Add pagination constraints if provided
      if (beforeMessageId) {
        domain.push(['id', '<', beforeMessageId]);
      }
      if (afterMessageId) {
        domain.push(['id', '>', afterMessageId]);
      }

      const fields = [
        'id', 'body', 'author_id', 'create_date', 'write_date', 
        'message_type', 'attachment_ids', 'partner_ids', 'subject',
        'email_from', 'record_name', 'model', 'res_id'
      ];

      const response = await api.get('/api/v2/search_read/mail.message', {
        params: {
          domain: JSON.stringify(domain),
          fields: JSON.stringify(fields),
          limit,
          offset,
          order: 'create_date desc'
        }
      });

      const messages = response.data || [];
      
      // Process messages for display
      const processedMessages = messages.map(message => ({
        ...message,
        authorName: this.getAuthorName(message),
        displayDate: this.formatMessageDate(message.create_date),
        cleanBody: this.cleanMessageBody(message.body),
        attachmentCount: message.attachment_ids ? message.attachment_ids.length : 0
      }));

      console.log(`Retrieved ${processedMessages.length} messages for channel ${channelId}`);
      return processedMessages;
    } catch (error) {
      console.error(`Error fetching messages for channel ${channelId}:`, error);
      throw this.handleError(error, 'Failed to fetch messages');
    }
  }

  /**
   * Send message to channel
   */
  async sendChannelMessage(channelId, messageBody, attachmentIds = []) {
    try {
      console.log(`Sending message to channel ${channelId}`);

      const messageData = {
        body: messageBody,
        message_type: 'comment',
        attachment_ids: attachmentIds.length > 0 ? [[6, 0, attachmentIds]] : []
      };

      const response = await api.post('/api/v2/call/discuss.channel/message_post', {
        ids: [parseInt(channelId)],
        kwargs: messageData
      });

      if (response.data) {
        console.log(`Successfully sent message to channel ${channelId}`);
        return response.data;
      }

      throw new Error('No response from server');
    } catch (error) {
      console.error('Error sending message:', error);
      throw this.handleError(error, 'Failed to send message');
    }
  }

  /**
   * Create new channel
   */
  async createChannel(name, description = '', isPublic = true, memberPartnerIds = []) {
    try {
      console.log(`Creating new channel: ${name}`);

      const currentUserId = await this.getCurrentUserId();
      
      const channelData = {
        name: name.trim(),
        description: description.trim(),
        channel_type: 'channel',
        public: isPublic ? 'public' : 'private',
        channel_partner_ids: memberPartnerIds.length > 0 ? [[6, 0, memberPartnerIds]] : []
      };

      const response = await api.post('/api/v2/create/discuss.channel', {
        values: channelData
      });

      if (response.data) {
        // Clear channels cache
        this.clearChannelsCache();
        console.log(`Successfully created channel with ID: ${response.data}`);
        return response.data;
      }

      throw new Error('No response from server');
    } catch (error) {
      console.error('Error creating channel:', error);
      throw this.handleError(error, 'Failed to create channel');
    }
  }

  /**
   * Start direct message with a partner
   */
  async startDirectMessage(partnerIds) {
    try {
      console.log(`Starting direct message with partners: ${partnerIds}`);

      const currentUserId = await this.getCurrentUserId();
      
      // Get current user's partner ID
      const currentUserResponse = await api.get('/api/v2/read/res.users', {
        params: {
          ids: JSON.stringify([currentUserId]),
          fields: JSON.stringify(['partner_id'])
        }
      });

      let allPartnerIds = [...partnerIds];
      if (currentUserResponse.data && currentUserResponse.data[0]?.partner_id) {
        const currentPartnerIdArray = currentUserResponse.data[0].partner_id;
        const currentPartnerId = Array.isArray(currentPartnerIdArray) 
          ? currentPartnerIdArray[0] 
          : currentPartnerIdArray;
        allPartnerIds.push(currentPartnerId);
      }

      const channelData = {
        channel_type: 'chat',
        channel_partner_ids: [[6, 0, allPartnerIds]]
      };

      const response = await api.post('/api/v2/create/discuss.channel', {
        values: channelData
      });

      if (response.data) {
        // Clear direct messages cache
        this.clearDirectMessagesCache();
        console.log(`Successfully created direct message with ID: ${response.data}`);
        return response.data;
      }

      throw new Error('No response from server');
    } catch (error) {
      console.error('Error starting direct message:', error);
      throw this.handleError(error, 'Failed to start direct message');
    }
  }

  /**
   * Mark channel as read
   */
  async markChannelAsRead(channelId) {
    try {
      const response = await api.post('/api/v2/call/discuss.channel/channel_seen', {
        ids: [parseInt(channelId)]
      });

      if (response.data) {
        // Clear cache to refresh unread counts
        this.clearCache();
        return response.data;
      }

      return true;
    } catch (error) {
      console.error(`Error marking channel ${channelId} as read:`, error);
      return false;
    }
  }

  /**
   * Search channels and direct messages
   */
  async searchConversations(searchText, options = {}) {
    const { limit = 20 } = options;
    
    try {
      const currentUserId = await this.getCurrentUserId();
      
      const searchDomain = [
        '&',
        ['channel_member_ids.partner_id.user_ids', 'in', [currentUserId]],
        '|',
        ['name', 'ilike', searchText],
        ['channel_partner_ids.name', 'ilike', searchText]
      ];

      const fields = [
        'id', 'name', 'channel_type', 'channel_partner_ids', 
        'last_interest_dt', 'message_unread_counter'
      ];

      const response = await api.get('/api/v2/search_read/discuss.channel', {
        params: {
          domain: JSON.stringify(searchDomain),
          fields: JSON.stringify(fields),
          limit,
          order: 'last_interest_dt desc'
        }
      });

      const results = response.data || [];
      
      // Process results
      const processedResults = await Promise.all(
        results.map(async (item) => {
          if (item.channel_type === 'channel') {
            return {
              ...item,
              displayName: item.name,
              subtitle: 'Channel',
              isChannel: true,
              avatarText: this.getChannelAvatar(item.name),
              avatarColor: this.getChannelColor(item.id)
            };
          } else {
            const displayName = await this.getDirectMessageDisplayName(item, currentUserId);
            return {
              ...item,
              displayName,
              subtitle: 'Direct message',
              isDirectMessage: true,
              avatarText: this.getPersonAvatar(displayName),
              avatarColor: this.getPersonColor(item.id)
            };
          }
        })
      );

      return processedResults;
    } catch (error) {
      console.error('Error searching conversations:', error);
      return [];
    }
  }

  /**
   * Helper methods
   */
  getAuthorName(message) {
    if (message.author_id && Array.isArray(message.author_id)) {
      return message.author_id[1] || 'Unknown';
    }
    return message.email_from || 'Unknown';
  }

  formatMessageDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) {
      return 'now';
    } else if (diffMins < 60) {
      return `${diffMins}m`;
    } else if (diffHours < 24) {
      return `${diffHours}h`;
    } else if (diffDays === 1) {
      return 'yesterday';
    } else if (diffDays < 7) {
      return `${diffDays}d`;
    } else {
      return date.toLocaleDateString();
    }
  }

  cleanMessageBody(body) {
    if (!body) return '';
    return body.replace(/<[^>]*>/g, '').trim();
  }

  getChannelAvatar(channelName) {
    if (!channelName) return '#';
    return channelName.charAt(0).toUpperCase();
  }

  getPersonAvatar(personName) {
    if (!personName) return '?';
    const names = personName.split(' ');
    if (names.length >= 2) {
      return (names[0].charAt(0) + names[1].charAt(0)).toUpperCase();
    }
    return personName.charAt(0).toUpperCase();
  }

  getChannelColor(channelId) {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
    return colors[channelId % colors.length];
  }

  getPersonColor(id) {
    const colors = ['#FF8A80', '#82B1FF', '#B39DDB', '#A5D6A7', '#FFCC02', '#FFAB91', '#80DEEA'];
    return colors[id % colors.length];
  }

  /**
   * Cache management
   */
  clearCache() {
    this.cache.clear();
    clearModelCache(this.model);
  }

  clearChannelsCache() {
    for (const key of this.cache.keys()) {
      if (key.startsWith('channels_')) {
        this.cache.delete(key);
      }
    }
  }

  clearDirectMessagesCache() {
    for (const key of this.cache.keys()) {
      if (key.startsWith('direct_messages_')) {
        this.cache.delete(key);
      }
    }
  }

  handleError(error, message) {
    const errorInfo = {
      message,
      originalError: error.message,
      status: error.response?.status,
      data: error.response?.data
    };
    
    console.error('Discuss Channel API Error:', errorInfo);
    return new Error(`${message}: ${error.message}`);
  }
}

// Create and export singleton instance
const discussChannelAPI = new DiscussChannelAPI();

export { discussChannelAPI };
export default discussChannelAPI;