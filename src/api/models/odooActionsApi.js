// Enhanced Odoo Actions API - Call actual Odoo functions
// src/api/models/odooActionsApi.js

import { createOdooClient } from '../odooClient';

// Use the existing odooClient from the codebase
const api = createOdooClient();

/**
 * Odoo Actions API - Call actual Odoo model methods and server actions
 * This preserves all Odoo business logic, notifications, and workflows
 */
export class OdooActionsAPI {

  /**
   * Generic method to call any Odoo model method
   * @param {string} model - Odoo model name (e.g., 'res.partner', 'helpdesk.ticket')
   * @param {string} method - Method name (e.g., 'message_post', 'action_confirm')
   * @param {Array} recordIds - Array of record IDs to apply the method to
   * @param {Object} kwargs - Additional keyword arguments for the method
   * @returns {Promise} - Result from Odoo
   */
  static async callModelMethod(model, method, recordIds = [], kwargs = {}) {
    try {
      console.log(`Calling Odoo method: ${model}.${method}`, { recordIds, kwargs });

      const response = await api.post('/api/v2/call', {
        model,
        method,
        args: [recordIds], // First argument is usually the record IDs
        kwargs
      });

      console.log(`Success: ${model}.${method}`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error calling ${model}.${method}:`, error);
      throw error;
    }
  }

  /**
   * Execute a server action by ID
   * @param {number} actionId - Server action ID from Odoo
   * @param {Array} recordIds - Record IDs to apply the action to
   * @param {Object} context - Additional context
   * @returns {Promise} - Result from Odoo
   */
  static async executeServerAction(actionId, recordIds = [], context = {}) {
    try {
      console.log(`Executing server action: ${actionId}`, { recordIds, context });

      const response = await api.post(`/api/v2/action/${actionId}`, {
        ids: recordIds,
        context
      });

      console.log(`Server action ${actionId} executed successfully`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error executing server action ${actionId}:`, error);
      throw error;
    }
  }

  // ============= MESSAGING ACTIONS =============

  /**
   * Post a message using Odoo's proper message_post method
   * This triggers all Odoo business logic: notifications, followers, etc.
   * @param {string} model - Model name (e.g., 'res.partner', 'helpdesk.ticket')
   * @param {number} recordId - Record ID
   * @param {string} body - Message content
   * @param {Object} options - Additional message options
   * @returns {Promise} - Created message data
   */
  static async postMessage(model, recordId, body, options = {}) {
    const {
      messageType = 'comment',
      subtypeXmlid = null,
      attachmentIds = [],
      partnerIds = [],
      parentId = null
    } = options;

    return this.callModelMethod(model, 'message_post', [recordId], {
      body,
      message_type: messageType,
      subtype_xmlid: subtypeXmlid,
      attachment_ids: attachmentIds,
      partner_ids: partnerIds,
      parent_id: parentId
    });
  }

  /**
   * Post a message to a contact using proper Odoo message_post
   * @param {number} partnerId - Partner/Contact ID
   * @param {string} message - Message content
   * @param {Array} attachmentIds - Optional attachment IDs
   * @returns {Promise} - Created message data
   */
  static async postContactMessage(partnerId, message, attachmentIds = []) {
    return this.postMessage('res.partner', partnerId, message, {
      messageType: 'comment',
      attachmentIds
    });
  }

  /**
   * Get messages for a record using proper Odoo method
   * @param {string} model - Model name
   * @param {number} recordId - Record ID
   * @param {number} limit - Number of messages to fetch
   * @returns {Promise} - Messages data
   */
  static async getMessages(model, recordId, limit = 50) {
    try {
      console.log(`Fetching messages for ${model} ID ${recordId} (type: ${typeof recordId})`);

      const domain = [
        ['model', '=', model],
        ['res_id', '=', parseInt(recordId)], // Ensure recordId is an integer
        ['message_type', 'in', ['comment', 'email']]
      ];

      console.log('Message domain filter:', JSON.stringify(domain));

      const response = await api.get('/api/v2/search_read/mail.message', {
        params: {
          domain: JSON.stringify(domain),
          fields: JSON.stringify([
            'id', 'body', 'date', 'author_id', 'attachment_ids',
            'message_type', 'subtype_id', 'parent_id', 'model', 'res_id'
          ]),
          limit,
          order: 'date desc'
        }
      });

      console.log(`Found ${response.data.length} messages for ${model} ID ${recordId}`);

      // Debug: Log first few messages to see what we're getting
      if (response.data.length > 0) {
        console.log('Sample messages:', response.data.slice(0, 3).map(msg => ({
          id: msg.id,
          model: msg.model,
          res_id: msg.res_id,
          message_type: msg.message_type,
          body_preview: msg.body ? msg.body.substring(0, 50) + '...' : 'No body'
        })));
      }

      return response.data;
    } catch (error) {
      console.error(`Error fetching messages for ${model} ${recordId}:`, error);
      throw error;
    }
  }

  // ============= HELPDESK ACTIONS =============

  /**
   * Confirm a helpdesk ticket using Odoo's action_confirm
   * @param {number} ticketId - Ticket ID
   * @returns {Promise} - Result from Odoo
   */
  static async confirmTicket(ticketId) {
    return this.callModelMethod('helpdesk.ticket', 'action_confirm', [ticketId]);
  }

  /**
   * Close a helpdesk ticket
   * @param {number} ticketId - Ticket ID
   * @returns {Promise} - Result from Odoo
   */
  static async closeTicket(ticketId) {
    return this.callModelMethod('helpdesk.ticket', 'action_close', [ticketId]);
  }

  /**
   * Set ticket as solved
   * @param {number} ticketId - Ticket ID
   * @returns {Promise} - Result from Odoo
   */
  static async solveTicket(ticketId) {
    return this.callModelMethod('helpdesk.ticket', 'action_set_solved', [ticketId]);
  }

  /**
   * Assign ticket to user
   * @param {number} ticketId - Ticket ID
   * @param {number} userId - User ID to assign to
   * @returns {Promise} - Result from Odoo
   */
  static async assignTicket(ticketId, userId) {
    return this.callModelMethod('helpdesk.ticket', 'write', [ticketId], {
      user_id: userId
    });
  }

  // ============= CONTACT ACTIONS =============

  /**
   * Archive/Unarchive a contact
   * @param {number} partnerId - Partner ID
   * @param {boolean} active - True to activate, false to archive
   * @returns {Promise} - Result from Odoo
   */
  static async toggleContactActive(partnerId, active = false) {
    return this.callModelMethod('res.partner', 'write', [partnerId], {
      active
    });
  }

  /**
   * Convert lead to opportunity (if using CRM)
   * @param {number} leadId - Lead ID
   * @returns {Promise} - Result from Odoo
   */
  static async convertLead(leadId) {
    return this.callModelMethod('crm.lead', 'action_convert', [leadId]);
  }

  // ============= TIMESHEET ACTIONS =============

  /**
   * Start timer for timesheet
   * @param {number} timesheetId - Timesheet line ID
   * @returns {Promise} - Result from Odoo
   */
  static async startTimer(timesheetId) {
    return this.callModelMethod('account.analytic.line', 'action_timer_start', [timesheetId]);
  }

  /**
   * Stop timer for timesheet
   * @param {number} timesheetId - Timesheet line ID
   * @returns {Promise} - Result from Odoo
   */
  static async stopTimer(timesheetId) {
    return this.callModelMethod('account.analytic.line', 'action_timer_stop', [timesheetId]);
  }

  // ============= HR ATTENDANCE ACTIONS =============

  /**
   * Clock in employee
   * @param {number} employeeId - Employee ID
   * @param {Object} location - Optional location data
   * @returns {Promise} - Result from Odoo
   */
  static async clockIn(employeeId, location = null) {
    const kwargs = {};
    if (location) {
      kwargs.latitude = location.latitude;
      kwargs.longitude = location.longitude;
    }

    return this.callModelMethod('hr.attendance', 'action_check_in', [employeeId], kwargs);
  }

  /**
   * Clock out employee
   * @param {number} employeeId - Employee ID
   * @param {Object} location - Optional location data
   * @returns {Promise} - Result from Odoo
   */
  static async clockOut(employeeId, location = null) {
    const kwargs = {};
    if (location) {
      kwargs.latitude = location.latitude;
      kwargs.longitude = location.longitude;
    }

    return this.callModelMethod('hr.attendance', 'action_check_out', [employeeId], kwargs);
  }

  // ============= GENERAL RECORD ACTIONS =============

  /**
   * Duplicate a record
   * @param {string} model - Model name
   * @param {number} recordId - Record ID
   * @returns {Promise} - New record data
   */
  static async duplicateRecord(model, recordId) {
    return this.callModelMethod(model, 'copy', [recordId]);
  }

  /**
   * Archive a record
   * @param {string} model - Model name
   * @param {number} recordId - Record ID
   * @returns {Promise} - Result from Odoo
   */
  static async archiveRecord(model, recordId) {
    return this.callModelMethod(model, 'action_archive', [recordId]);
  }

  /**
   * Unarchive a record
   * @param {string} model - Model name
   * @param {number} recordId - Record ID
   * @returns {Promise} - Result from Odoo
   */
  static async unarchiveRecord(model, recordId) {
    return this.callModelMethod(model, 'action_unarchive', [recordId]);
  }

  // ============= WORKFLOW ACTIONS =============

  /**
   * Send email using Odoo's email composer
   * @param {string} model - Model name
   * @param {number} recordId - Record ID
   * @param {number} templateId - Email template ID
   * @returns {Promise} - Result from Odoo
   */
  static async sendEmailFromTemplate(model, recordId, templateId) {
    return this.callModelMethod('mail.compose.message', 'send_mail', [], {
      default_model: model,
      default_res_id: recordId,
      default_template_id: templateId
    });
  }

  /**
   * Print report
   * @param {string} reportName - Report name/ID
   * @param {Array} recordIds - Record IDs
   * @returns {Promise} - Report data
   */
  static async printReport(reportName, recordIds) {
    try {
      const response = await api.get(`/api/v2/report/${reportName}`, {
        params: {
          ids: JSON.stringify(recordIds)
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error printing report ${reportName}:`, error);
      throw error;
    }
  }

  // ============= CUSTOM ACTION FINDER =============

  /**
   * Find available actions for a model (useful for dynamic UI)
   * @param {string} model - Model name
   * @returns {Promise} - Available actions
   */
  static async getModelActions(model) {
    try {
      const response = await api.get('/api/v2/search_read/ir.actions.act_window', {
        params: {
          domain: JSON.stringify([['res_model', '=', model]]),
          fields: JSON.stringify(['name', 'res_model', 'type', 'xml_id'])
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching actions for model ${model}:`, error);
      throw error;
    }
  }
}

export default OdooActionsAPI;
