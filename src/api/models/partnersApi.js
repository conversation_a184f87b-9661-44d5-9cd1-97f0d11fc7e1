// Simple, fast contacts API - modeled after helpdesk implementation

import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ODOO_CONFIG } from '../../config/odoo';

// Helper function to make API requests with proper authentication (same as helpdesk)
const makeApiRequest = async (method, endpoint, params = null, data = null) => {
  try {
    // Get the stored token data
    const tokenData = await AsyncStorage.getItem('odooTokenData');
    if (!tokenData) {
      console.error('No access token available. Please authenticate first.');
      return null;
    }

    const parsedToken = JSON.parse(tokenData);
    const accessToken = parsedToken.accessToken;
    const serverConfig = parsedToken.serverConfig || ODOO_CONFIG;

    const url = `${serverConfig.baseURL}${endpoint}`;
    console.log(`Making ${method.toUpperCase()} request to: ${url}`);
    if (params) console.log('Params:', JSON.stringify(params));
    if (data) console.log('Data:', JSON.stringify(data));

    const requestConfig = {
      method,
      url,
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'DATABASE': serverConfig.db,
        'Content-Type': 'application/json'
      }
    };

    if (params) requestConfig.params = params;
    if (data) requestConfig.data = data;

    const response = await axios(requestConfig);
    console.log(`Request successful (${response.status})`);
    return response.data;
  } catch (error) {
    console.error(`API request error: ${error.message}`);
    if (error.response) {
      console.error(`Status code: ${error.response.status}`);
      console.error('Error response:', error.response.data);
    }
    return null;
  }
};

// Main contacts API object
const partnersAPI = {};

/**
 * Fetch contacts with pagination like helpdesk
 * @param {Object} options - Query options
 * @param {Array} options.domain - Domain filter
 * @param {Array} options.fields - Fields to fetch
 * @param {Number} options.limit - Maximum number of records
 * @param {Number} options.offset - Offset for pagination
 * @returns {Promise<Array>} - List of contacts
 */
partnersAPI.getContacts = async (options = {}) => {
  const {
    domain = ["|", ["is_company", "=", true], ["parent_id", "=", false]],
    fields = [
      'id',
      'name',
      'email',
      'phone',
      'mobile',
      'image_128',
      'street',
      'city',
      'country_id',
      'is_company',
      'parent_id'
    ],
    limit = 80,
    offset = 0,
  } = options;

  console.log('Fetching contacts');

  const params = {
    fields: JSON.stringify(fields),
    limit,
    offset
  };

  // Add domain if provided
  if (domain && domain.length > 0) {
    params.domain = JSON.stringify(domain);
  }

  const data = await makeApiRequest('get', '/api/v2/search_read/res.partner', params);

  if (data) {
    console.log(`Retrieved ${data.length} contacts`);
    return data;
  }

  return [];
};

/**
 * Fetch a single contact by ID
 * @param {Number} contactId - Contact ID
 * @param {Array} fields - Fields to fetch
 * @returns {Promise<Object>} - Contact details
 */
partnersAPI.getContact = async (
  contactId,
  fields = [
    'id',
    'name',
    'email',
    'phone',
    'mobile',
    'image_128',
    'street',
    'city',
    'country_id',
    'is_company',
    'parent_id'
  ]
) => {
  console.log(`Fetching contact ${contactId}`);

  const params = {
    ids: JSON.stringify([contactId]),
    fields: JSON.stringify(fields)
  };

  const data = await makeApiRequest('get', '/api/v2/read/res.partner', params);

  if (data && data.length > 0) {
    console.log(`Retrieved contact: ${data[0].name}`);
    return data[0];
  }

  return null;
};

// Compatibility methods for existing code
partnersAPI.getList = partnersAPI.getContacts;
partnersAPI.getById = partnersAPI.getContact;
partnersAPI.getAllContacts = partnersAPI.getContacts;

export { partnersAPI };
export default partnersAPI;
