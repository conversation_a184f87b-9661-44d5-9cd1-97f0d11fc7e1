// Updated HR Employee API - Complete implementation for Odoo 18
// Compatible with the existing project architecture

import api from '../../services/api';
import { searchRelated, clearModelCache } from '../../services/api';

/**
 * Complete HR Employee API implementation
 * Supports all CRUD operations and HR-specific functionality
 */
class HREmployeeAPI {
  constructor() {
    this.model = 'hr.employee';
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get employees with advanced filtering and pagination
   * GET /api/v2/search_read/hr.employee
   */
  async getEmployees(options = {}) {
    const {
      domain = [['active', '=', true]],
      fields = [
        'id', 'name', 'work_email', 'work_phone', 'mobile_phone',
        'image_128', 'department_id', 'job_id', 'parent_id', 'coach_id',
        'work_location', 'employee_type', 'resource_calendar_id', 'tz',
        'lang', 'marital', 'birthday', 'work_contact_id', 'user_id', 'active'
      ],
      limit = 50,
      offset = 0,
      order = 'name asc',
      forceRefresh = false
    } = options;

    try {
      const cacheKey = `employees_${JSON.stringify(domain)}_${limit}_${offset}`;

      // Check cache first
      if (!forceRefresh && this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          console.log('Returning cached employees');
          return cached.data;
        }
      }

      console.log('Fetching employees from API');
      const response = await api.get('/api/v2/search_read/hr.employee', {
        params: {
          domain: JSON.stringify(domain),
          fields: JSON.stringify(fields),
          limit,
          offset,
          order
        }
      });

      const employees = response.data || [];

      // Cache the results
      this.cache.set(cacheKey, {
        data: employees,
        timestamp: Date.now()
      });

      console.log(`Retrieved ${employees.length} employees`);
      return employees;
    } catch (error) {
      console.error('Error fetching employees:', error);
      throw this.handleError(error, 'Failed to fetch employees');
    }
  }

  /**
   * Get single employee by ID
   * GET /api/v2/read/hr.employee
   */
  async getEmployee(employeeId, fields = null) {
    const defaultFields = [
      'id', 'name', 'work_email', 'work_phone', 'mobile_phone', 'image_1920',
      'department_id', 'job_id', 'parent_id', 'coach_id', 'work_location',
      'employee_type', 'resource_calendar_id', 'tz', 'lang', 'marital',
      'birthday', 'work_contact_id', 'user_id', 'active', 'identification_id',
      'passport_id', 'bank_account_id', 'address_home_id', 'emergency_contact',
      'emergency_phone', 'km_home_work', 'spouse_complete_name',
      'spouse_birthdate', 'children', 'barcode', 'pin', 'badge_ids',
      'last_attendance_id', 'hours_last_month', 'hours_today'
    ];

    try {
      const cacheKey = `employee_${employeeId}`;

      // Check cache first
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          console.log(`Returning cached employee ${employeeId}`);
          return cached.data;
        }
      }

      console.log(`Fetching employee ${employeeId}`);
      const response = await api.get('/api/v2/read/hr.employee', {
        params: {
          ids: JSON.stringify([employeeId]),
          fields: JSON.stringify(fields || defaultFields)
        }
      });

      const employee = response.data && response.data[0] ? response.data[0] : null;

      if (employee) {
        // Cache the result
        this.cache.set(cacheKey, {
          data: employee,
          timestamp: Date.now()
        });
        console.log(`Retrieved employee: ${employee.name}`);
      }

      return employee;
    } catch (error) {
      console.error(`Error fetching employee ${employeeId}:`, error);
      throw this.handleError(error, `Failed to fetch employee ${employeeId}`);
    }
  }

  /**
   * Create new employee
   * POST /api/v2/create/hr.employee
   */
  async createEmployee(employeeData) {
    try {
      console.log('Creating new employee:', employeeData);

      // Validate required fields
      if (!employeeData.name) {
        throw new Error('Employee name is required');
      }

      const response = await api.post('/api/v2/create/hr.employee', {
        values: employeeData
      });

      if (response.data) {
        // Clear cache to ensure fresh data
        this.clearCache();
        console.log('Employee created successfully with ID:', response.data);
        return response.data;
      }

      throw new Error('Invalid response from server');
    } catch (error) {
      console.error('Error creating employee:', error);
      throw this.handleError(error, 'Failed to create employee');
    }
  }

  /**
   * Update existing employee
   * PUT /api/v2/write/hr.employee
   */
  async updateEmployee(employeeId, employeeData) {
    try {
      console.log(`Updating employee ${employeeId}:`, employeeData);

      const response = await api.put('/api/v2/write/hr.employee', {
        ids: [employeeId],
        values: employeeData
      });

      if (response.data !== false) {
        // Clear cache for this employee
        this.cache.delete(`employee_${employeeId}`);
        this.clearCacheStartingWith('employees_');
        console.log('Employee updated successfully');
        return response.data;
      }

      throw new Error('Update operation failed');
    } catch (error) {
      console.error(`Error updating employee ${employeeId}:`, error);
      throw this.handleError(error, `Failed to update employee ${employeeId}`);
    }
  }

  /**
   * Manual attendance check-in/out
   * POST /api/v2/call/hr.employee/attendance_manual
   */
  async attendanceManual(employeeId, action = 'check_in') {
    try {
      console.log(`Manual attendance ${action} for employee ${employeeId}`);

      const response = await api.post('/api/v2/call/hr.employee/attendance_manual', {
        ids: [employeeId],
        kwargs: {
          action: action,
          manual: true
        }
      });

      if (response.data) {
        // Clear attendance related cache
        this.clearCacheStartingWith(`attendance_${employeeId}`);
        console.log(`Attendance ${action} successful`);
        return response.data;
      }

      throw new Error(`Attendance ${action} failed`);
    } catch (error) {
      console.error(`Error with attendance ${action} for employee ${employeeId}:`, error);
      throw this.handleError(error, `Failed to ${action} for employee`);
    }
  }

  /**
   * Search employees with text query
   */
  async searchEmployees(searchText, options = {}) {
    const { limit = 20, offset = 0, includeInactive = false } = options;

    const baseDomain = includeInactive ? [] : [['active', '=', true]];
    const searchDomain = [
      ...baseDomain,
      '|', '|', '|', '|',
      ['name', 'ilike', searchText],
      ['work_email', 'ilike', searchText],
      ['department_id.name', 'ilike', searchText],
      ['job_id.name', 'ilike', searchText],
      ['barcode', 'ilike', searchText]
    ];

    return this.getEmployees({
      domain: searchDomain,
      limit,
      offset,
      order: 'name asc'
    });
  }

  /**
   * Get employees by department
   */
  async getEmployeesByDepartment(departmentId, options = {}) {
    const domain = [
      ['active', '=', true],
      ['department_id', '=', departmentId]
    ];

    return this.getEmployees({
      ...options,
      domain
    });
  }

  /**
   * Get employees by manager
   */
  async getEmployeesByManager(managerId, options = {}) {
    const domain = [
      ['active', '=', true],
      ['parent_id', '=', managerId]
    ];

    return this.getEmployees({
      ...options,
      domain
    });
  }

  /**
   * Get employee attendance records
   */
  async getAttendanceRecords(employeeId, options = {}) {
    const {
      limit = 50,
      offset = 0,
      dateFrom = null,
      dateTo = null
    } = options;

    try {
      let domain = [['employee_id', '=', employeeId]];

      if (dateFrom) {
        domain.push(['check_in', '>=', dateFrom]);
      }

      if (dateTo) {
        domain.push(['check_in', '<=', dateTo]);
      }

      const response = await api.get('/api/v2/search_read/hr.attendance', {
        params: {
          domain: JSON.stringify(domain),
          fields: JSON.stringify([
            'id', 'check_in', 'check_out', 'worked_hours',
            'employee_id', 'activity_ids'
          ]),
          limit,
          offset,
          order: 'check_in desc'
        }
      });

      const records = response.data || [];
      console.log(`Retrieved ${records.length} attendance records for employee ${employeeId}`);
      return records;
    } catch (error) {
      console.error(`Error fetching attendance records for employee ${employeeId}:`, error);
      throw this.handleError(error, 'Failed to fetch attendance records');
    }
  }

  /**
   * Get departments
   */
  async getDepartments(options = {}) {
    return searchRelated('hr.department', [], [
      'id', 'name', 'parent_id', 'manager_id', 'member_ids', 'company_id'
    ], options.limit || 100, options.offset || 0);
  }

  /**
   * Get job positions
   */
  async getJobPositions(options = {}) {
    return searchRelated('hr.job', [], [
      'id', 'name', 'department_id', 'description', 'requirements',
      'no_of_recruitment', 'employee_ids', 'company_id'
    ], options.limit || 100, options.offset || 0);
  }

  /**
   * Get employee contracts
   */
  async getEmployeeContracts(employeeId, options = {}) {
    try {
      const { limit = 10, offset = 0, activeOnly = true } = options;

      let domain = [['employee_id', '=', employeeId]];
      if (activeOnly) {
        domain.push(['state', '=', 'open']);
      }

      const response = await api.get('/api/v2/search_read/hr.contract', {
        params: {
          domain: JSON.stringify(domain),
          fields: JSON.stringify([
            'id', 'name', 'employee_id', 'job_id', 'department_id',
            'date_start', 'date_end', 'wage', 'state', 'contract_type_id'
          ]),
          limit,
          offset,
          order: 'date_start desc'
        }
      });

      return response.data || [];
    } catch (error) {
      console.error(`Error fetching contracts for employee ${employeeId}:`, error);
      return [];
    }
  }

  /**
   * Get employee statistics
   */
  async getEmployeeStats() {
    try {
      const [totalActive, totalInactive, byDepartment] = await Promise.all([
        this.getEmployees({ domain: [['active', '=', true]], fields: ['id'], limit: 1000 }),
        this.getEmployees({ domain: [['active', '=', false]], fields: ['id'], limit: 1000 }),
        this.getEmployees({ fields: ['id', 'department_id'], limit: 1000 })
      ]);

      const departmentStats = {};
      byDepartment.forEach(emp => {
        if (emp.department_id) {
          const deptName = emp.department_id[1] || 'Unknown';
          departmentStats[deptName] = (departmentStats[deptName] || 0) + 1;
        }
      });

      return {
        total: totalActive.length + totalInactive.length,
        active: totalActive.length,
        inactive: totalInactive.length,
        byDepartment: departmentStats
      };
    } catch (error) {
      console.error('Error fetching employee statistics:', error);
      return { total: 0, active: 0, inactive: 0, byDepartment: {} };
    }
  }

  /**
   * Batch operations
   */
  async batchUpdateEmployees(updates) {
    try {
      const results = [];
      for (const update of updates) {
        const result = await this.updateEmployee(update.id, update.values);
        results.push({ id: update.id, success: !!result, result });
      }
      return results;
    } catch (error) {
      console.error('Error in batch update:', error);
      throw this.handleError(error, 'Batch update failed');
    }
  }

  /**
   * Utility methods
   */
  clearCache() {
    this.cache.clear();
    clearModelCache(this.model);
  }

  clearCacheStartingWith(prefix) {
    for (const key of this.cache.keys()) {
      if (key.startsWith(prefix)) {
        this.cache.delete(key);
      }
    }
  }

  handleError(error, message) {
    const errorInfo = {
      message,
      originalError: error.message,
      status: error.response?.status,
      data: error.response?.data
    };

    console.error('HR Employee API Error:', errorInfo);
    return new Error(`${message}: ${error.message}`);
  }

  // Legacy compatibility methods
  getList = this.getEmployees;
  getById = this.getEmployee;
  search = this.searchEmployees;
  create = this.createEmployee;
  update = this.updateEmployee;
}

// Create and export singleton instance
const hrEmployeeAPI = new HREmployeeAPI();

export { hrEmployeeAPI };
export default hrEmployeeAPI;
