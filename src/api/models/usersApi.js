// Updated Res Users API - Complete implementation for Odoo 18
// Compatible with the existing project architecture

import api from '../../services/api';
import { searchRelated, clearModelCache } from '../../services/api';

/**
 * Complete Res Users API implementation
 * Supports all CRUD operations and user management functionality
 */
class ResUsersAPI {
  constructor() {
    this.model = 'res.users';
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get users with advanced filtering and pagination
   * GET /api/v2/search_read/res.users
   */
  async getUsers(options = {}) {
    const {
      domain = [['active', '=', true]],
      fields = [
        'id', 'name', 'login', 'email', 'phone', 'mobile', 'image_128',
        'partner_id', 'company_id', 'company_ids', 'groups_id', 'lang',
        'tz', 'signature', 'active', 'share', 'employee_id', 'create_date',
        'login_date', 'action_id', 'menu_id'
      ],
      limit = 50,
      offset = 0,
      order = 'name asc',
      forceRefresh = false
    } = options;

    try {
      const cacheKey = `users_${JSON.stringify(domain)}_${limit}_${offset}`;

      // Check cache first
      if (!forceRefresh && this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          console.log('Returning cached users');
          return cached.data;
        }
      }

      console.log('Fetching users from API');
      const response = await api.get('/api/v2/search_read/res.users', {
        params: {
          domain: JSON.stringify(domain),
          fields: JSON.stringify(fields),
          limit,
          offset,
          order
        }
      });

      const users = response.data || [];

      // Cache the results
      this.cache.set(cacheKey, {
        data: users,
        timestamp: Date.now()
      });

      console.log(`Retrieved ${users.length} users`);
      return users;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw this.handleError(error, 'Failed to fetch users');
    }
  }

  /**
   * Get single user by ID
   * GET /api/v2/read/res.users
   */
  async getUser(userId, fields = null) {
    const defaultFields = [
      'id', 'name', 'login', 'email', 'phone', 'mobile', 'image_1920',
      'partner_id', 'company_id', 'company_ids', 'groups_id', 'lang',
      'tz', 'signature', 'active', 'share', 'employee_id', 'create_date',
      'login_date', 'access_token', 'sel_groups_1_2_3', 'sel_groups_4_5_6',
      'sel_groups_7_8_9', 'action_id', 'menu_id', 'totp_enabled',
      'notification_type', 'odoobot_state', 'is_admin'
    ];

    try {
      const cacheKey = `user_${userId}`;

      // Check cache first
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          console.log(`Returning cached user ${userId}`);
          return cached.data;
        }
      }

      console.log(`Fetching user ${userId}`);
      const response = await api.get('/api/v2/read/res.users', {
        params: {
          ids: JSON.stringify([userId]),
          fields: JSON.stringify(fields || defaultFields)
        }
      });

      const user = response.data && response.data[0] ? response.data[0] : null;

      if (user) {
        // Cache the result
        this.cache.set(cacheKey, {
          data: user,
          timestamp: Date.now()
        });
        console.log(`Retrieved user: ${user.name}`);
      }

      return user;
    } catch (error) {
      console.error(`Error fetching user ${userId}:`, error);
      throw this.handleError(error, `Failed to fetch user ${userId}`);
    }
  }

  /**
   * Create new user
   * POST /api/v2/create/res.users
   */
  async createUser(userData) {
    try {
      console.log('Creating new user:', userData);

      // Validate required fields
      if (!userData.name) {
        throw new Error('User name is required');
      }
      if (!userData.login) {
        throw new Error('User login is required');
      }

      // Set default values
      const userDataWithDefaults = {
        active: true,
        lang: 'en_US',
        tz: 'UTC',
        ...userData
      };

      const response = await api.post('/api/v2/create/res.users', {
        values: userDataWithDefaults
      });

      if (response.data) {
        // Clear cache to ensure fresh data
        this.clearCache();
        console.log('User created successfully with ID:', response.data);
        return response.data;
      }

      throw new Error('Invalid response from server');
    } catch (error) {
      console.error('Error creating user:', error);
      throw this.handleError(error, 'Failed to create user');
    }
  }

  /**
   * Update existing user
   * PUT /api/v2/write/res.users
   */
  async updateUser(userId, userData) {
    try {
      console.log(`Updating user ${userId}:`, userData);

      const response = await api.put('/api/v2/write/res.users', {
        ids: [userId],
        values: userData
      });

      if (response.data !== false) {
        // Clear cache for this user
        this.cache.delete(`user_${userId}`);
        this.clearCacheStartingWith('users_');
        console.log('User updated successfully');
        return response.data;
      }

      throw new Error('Update operation failed');
    } catch (error) {
      console.error(`Error updating user ${userId}:`, error);
      throw this.handleError(error, `Failed to update user ${userId}`);
    }
  }

  /**
   * Search users with text query
   */
  async searchUsers(searchText, options = {}) {
    const { limit = 20, offset = 0, includeInactive = false } = options;

    const baseDomain = includeInactive ? [] : [['active', '=', true]];
    const searchDomain = [
      ...baseDomain,
      '|', '|', '|', '|',
      ['name', 'ilike', searchText],
      ['login', 'ilike', searchText],
      ['email', 'ilike', searchText],
      ['partner_id.name', 'ilike', searchText],
      ['partner_id.email', 'ilike', searchText]
    ];

    return this.getUsers({
      domain: searchDomain,
      limit,
      offset,
      order: 'name asc'
    });
  }

  /**
   * Get users by company
   */
  async getUsersByCompany(companyId, options = {}) {
    const domain = [
      ['active', '=', true],
      ['company_id', '=', companyId]
    ];

    return this.getUsers({
      ...options,
      domain
    });
  }

  /**
   * Get users by group
   */
  async getUsersByGroup(groupId, options = {}) {
    const domain = [
      ['active', '=', true],
      ['groups_id', 'in', [groupId]]
    ];

    return this.getUsers({
      ...options,
      domain
    });
  }

  /**
   * Get internal users (non-portal users)
   */
  async getInternalUsers(options = {}) {
    const domain = [
      ['active', '=', true],
      ['share', '=', false]
    ];

    return this.getUsers({
      ...options,
      domain
    });
  }

  /**
   * Get portal users
   */
  async getPortalUsers(options = {}) {
    const domain = [
      ['active', '=', true],
      ['share', '=', true]
    ];

    return this.getUsers({
      ...options,
      domain
    });
  }

  /**
   * Get administrator users
   */
  async getAdminUsers(options = {}) {
    try {
      // First get the admin group ID
      const adminGroups = await this.getUserGroups({
        domain: [['name', '=', 'Administration / Settings']],
        limit: 1
      });

      if (adminGroups.length === 0) {
        return [];
      }

      const adminGroupId = adminGroups[0].id;
      return this.getUsersByGroup(adminGroupId, options);
    } catch (error) {
      console.error('Error fetching admin users:', error);
      return [];
    }
  }

  /**
   * Get user groups
   */
  async getUserGroups(options = {}) {
    const {
      domain = [],
      fields = ['id', 'name', 'category_id', 'users', 'implied_ids', 'trans_implied_ids'],
      limit = 100,
      offset = 0
    } = options;

    try {
      const response = await api.get('/api/v2/search_read/res.groups', {
        params: {
          domain: JSON.stringify(domain),
          fields: JSON.stringify(fields),
          limit,
          offset,
          order: 'name asc'
        }
      });

      const groups = response.data || [];
      console.log(`Retrieved ${groups.length} user groups`);
      return groups;
    } catch (error) {
      console.error('Error fetching user groups:', error);
      return [];
    }
  }

  /**
   * Get companies
   */
  async getCompanies(options = {}) {
    return searchRelated('res.company', [], [
      'id', 'name', 'email', 'phone', 'website', 'logo', 'currency_id',
      'country_id', 'state_id', 'city', 'street', 'zip'
    ], options.limit || 100, options.offset || 0);
  }

  /**
   * Get user access rights
   */
  async getUserAccessRights(userId) {
    try {
      console.log(`Getting access rights for user ${userId}`);

      const user = await this.getUser(userId, ['groups_id', 'is_admin']);

      if (user && user.groups_id) {
        const groups = await this.getUserGroups({
          domain: [['id', 'in', user.groups_id]]
        });

        return {
          user_id: userId,
          is_admin: user.is_admin || false,
          groups: groups || [],
          group_names: groups.map(g => g.name),
          has_admin_access: user.is_admin || groups.some(g =>
            g.name.includes('Administration') || g.name.includes('Settings')
          )
        };
      }

      return {
        user_id: userId,
        is_admin: false,
        groups: [],
        group_names: [],
        has_admin_access: false
      };
    } catch (error) {
      console.error(`Error getting access rights for user ${userId}:`, error);
      return {
        user_id: userId,
        is_admin: false,
        groups: [],
        group_names: [],
        has_admin_access: false
      };
    }
  }

  /**
   * Add user to group
   */
  async addUserToGroup(userId, groupId) {
    try {
      const user = await this.getUser(userId, ['groups_id']);
      if (!user) {
        throw new Error('User not found');
      }

      const currentGroups = user.groups_id || [];
      if (currentGroups.includes(groupId)) {
        console.log(`User ${userId} already in group ${groupId}`);
        return true;
      }

      const newGroups = [...currentGroups, groupId];
      return this.updateUser(userId, { groups_id: [[6, 0, newGroups]] });
    } catch (error) {
      console.error(`Error adding user ${userId} to group ${groupId}:`, error);
      throw this.handleError(error, 'Failed to add user to group');
    }
  }

  /**
   * Remove user from group
   */
  async removeUserFromGroup(userId, groupId) {
    try {
      const user = await this.getUser(userId, ['groups_id']);
      if (!user) {
        throw new Error('User not found');
      }

      const currentGroups = user.groups_id || [];
      if (!currentGroups.includes(groupId)) {
        console.log(`User ${userId} not in group ${groupId}`);
        return true;
      }

      const newGroups = currentGroups.filter(id => id !== groupId);
      return this.updateUser(userId, { groups_id: [[6, 0, newGroups]] });
    } catch (error) {
      console.error(`Error removing user ${userId} from group ${groupId}:`, error);
      throw this.handleError(error, 'Failed to remove user from group');
    }
  }

  /**
   * Reset user password
   */
  async resetUserPassword(userId) {
    try {
      console.log(`Resetting password for user ${userId}`);

      const response = await api.post('/api/v2/call/res.users/reset_password', {
        ids: [userId],
        kwargs: {}
      });

      if (response.data) {
        console.log('Password reset successfully');
        return response.data;
      }

      throw new Error('Password reset failed');
    } catch (error) {
      console.error(`Error resetting password for user ${userId}:`, error);
      throw this.handleError(error, 'Failed to reset password');
    }
  }

  /**
   * Change user password
   */
  async changeUserPassword(userId, newPassword) {
    try {
      console.log(`Changing password for user ${userId}`);

      const response = await api.post('/api/v2/call/res.users/change_password', {
        ids: [userId],
        kwargs: { new_passwd: newPassword }
      });

      if (response.data) {
        console.log('Password changed successfully');
        return response.data;
      }

      throw new Error('Password change failed');
    } catch (error) {
      console.error(`Error changing password for user ${userId}:`, error);
      throw this.handleError(error, 'Failed to change password');
    }
  }

  /**
   * Activate/Deactivate user
   */
  async setUserActive(userId, active = true) {
    try {
      const result = await this.updateUser(userId, { active });
      console.log(`User ${userId} ${active ? 'activated' : 'deactivated'} successfully`);
      return result;
    } catch (error) {
      console.error(`Error ${active ? 'activating' : 'deactivating'} user ${userId}:`, error);
      throw this.handleError(error, `Failed to ${active ? 'activate' : 'deactivate'} user`);
    }
  }

  /**
   * Get user preferences
   */
  async getUserPreferences(userId) {
    try {
      const user = await this.getUser(userId, [
        'lang', 'tz', 'notification_type', 'signature', 'action_id', 'menu_id'
      ]);

      if (user) {
        return {
          language: user.lang,
          timezone: user.tz,
          notification_type: user.notification_type,
          signature: user.signature,
          default_action: user.action_id,
          default_menu: user.menu_id
        };
      }

      return null;
    } catch (error) {
      console.error(`Error getting preferences for user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(userId, preferences) {
    try {
      const updateData = {};

      if (preferences.language) updateData.lang = preferences.language;
      if (preferences.timezone) updateData.tz = preferences.timezone;
      if (preferences.notification_type) updateData.notification_type = preferences.notification_type;
      if (preferences.signature !== undefined) updateData.signature = preferences.signature;
      if (preferences.default_action) updateData.action_id = preferences.default_action;
      if (preferences.default_menu) updateData.menu_id = preferences.default_menu;

      return this.updateUser(userId, updateData);
    } catch (error) {
      console.error(`Error updating preferences for user ${userId}:`, error);
      throw this.handleError(error, 'Failed to update user preferences');
    }
  }

  /**
   * Get user statistics
   */
  async getUserStats() {
    try {
      const [totalActive, totalInactive, internalUsers, portalUsers] = await Promise.all([
        this.getUsers({ domain: [['active', '=', true]], fields: ['id'], limit: 1000 }),
        this.getUsers({ domain: [['active', '=', false]], fields: ['id'], limit: 1000 }),
        this.getInternalUsers({ fields: ['id'], limit: 1000 }),
        this.getPortalUsers({ fields: ['id'], limit: 1000 })
      ]);

      return {
        total: totalActive.length + totalInactive.length,
        active: totalActive.length,
        inactive: totalInactive.length,
        internal: internalUsers.length,
        portal: portalUsers.length
      };
    } catch (error) {
      console.error('Error fetching user statistics:', error);
      return { total: 0, active: 0, inactive: 0, internal: 0, portal: 0 };
    }
  }

  /**
   * Get user login history
   */
  async getUserLoginHistory(userId, options = {}) {
    try {
      const { limit = 50, offset = 0 } = options;

      const response = await api.get('/api/v2/search_read/res.users.log', {
        params: {
          domain: JSON.stringify([['create_uid', '=', userId]]),
          fields: JSON.stringify(['id', 'create_date', 'create_uid']),
          limit,
          offset,
          order: 'create_date desc'
        }
      });

      return response.data || [];
    } catch (error) {
      console.error(`Error fetching login history for user ${userId}:`, error);
      return [];
    }
  }

  /**
   * Batch operations
   */
  async batchUpdateUsers(updates) {
    try {
      const results = [];
      for (const update of updates) {
        const result = await this.updateUser(update.id, update.values);
        results.push({ id: update.id, success: !!result, result });
      }
      return results;
    } catch (error) {
      console.error('Error in batch update:', error);
      throw this.handleError(error, 'Batch update failed');
    }
  }

  /**
   * Batch activate/deactivate users
   */
  async batchSetUsersActive(userIds, active = true) {
    try {
      const updates = userIds.map(id => ({ id, values: { active } }));
      return this.batchUpdateUsers(updates);
    } catch (error) {
      console.error('Error in batch activate/deactivate:', error);
      throw this.handleError(error, 'Batch activation failed');
    }
  }

  /**
   * Get user's partner information
   */
  async getUserPartner(userId) {
    try {
      const user = await this.getUser(userId, ['partner_id']);

      if (user && user.partner_id) {
        const partnerId = Array.isArray(user.partner_id) ? user.partner_id[0] : user.partner_id;

        const response = await api.get('/api/v2/read/res.partner', {
          params: {
            ids: JSON.stringify([partnerId]),
            fields: JSON.stringify([
              'id', 'name', 'email', 'phone', 'mobile', 'street', 'street2',
              'city', 'state_id', 'zip', 'country_id', 'website', 'category_id'
            ])
          }
        });

        return response.data && response.data[0] ? response.data[0] : null;
      }

      return null;
    } catch (error) {
      console.error(`Error getting partner for user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Utility methods
   */
  clearCache() {
    this.cache.clear();
    clearModelCache(this.model);
  }

  clearCacheStartingWith(prefix) {
    for (const key of this.cache.keys()) {
      if (key.startsWith(prefix)) {
        this.cache.delete(key);
      }
    }
  }

  handleError(error, message) {
    const errorInfo = {
      message,
      originalError: error.message,
      status: error.response?.status,
      data: error.response?.data
    };

    console.error('Res Users API Error:', errorInfo);
    return new Error(`${message}: ${error.message}`);
  }

  // Legacy compatibility methods
  getList = this.getUsers;
  getById = this.getUser;
  search = this.searchUsers;
  create = this.createUser;
  update = this.updateUser;
}

// Create and export singleton instance
const resUsersAPI = new ResUsersAPI();

export { resUsersAPI };
export default resUsersAPI;
