// Products API - following the pattern of partnersApi.js

import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ODOO_CONFIG } from '../../config/odoo';

// Helper function to make API requests with proper authentication
const makeApiRequest = async (method, endpoint, params = null, data = null) => {
  try {
    // Get the stored token data
    const tokenData = await AsyncStorage.getItem('odooTokenData');
    if (!tokenData) {
      console.error('No access token available. Please authenticate first.');
      return null;
    }

    const parsedToken = JSON.parse(tokenData);
    const accessToken = parsedToken.accessToken;
    const serverConfig = parsedToken.serverConfig || ODOO_CONFIG;

    const url = `${serverConfig.baseURL}${endpoint}`;
    console.log(`Making ${method.toUpperCase()} request to: ${url}`);
    if (params) console.log('Params:', JSON.stringify(params));
    if (data) console.log('Data:', JSON.stringify(data));

    const requestConfig = {
      method,
      url,
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'DATABASE': serverConfig.db,
        'Content-Type': 'application/json'
      }
    };

    if (params) requestConfig.params = params;
    if (data) requestConfig.data = data;

    const response = await axios(requestConfig);
    console.log(`Request successful (${response.status})`);
    return response.data;
  } catch (error) {
    console.error(`API request error: ${error.message}`);
    if (error.response) {
      console.error(`Status code: ${error.response.status}`);
      console.error('Error response:', error.response.data);
    }
    return null;
  }
};

// Main products API object
const productsAPI = {};

/**
 * Fetch products with pagination and filtering
 * @param {Object} options - Query options
 * @param {Array} options.domain - Domain filter
 * @param {Array} options.fields - Fields to fetch
 * @param {Number} options.limit - Maximum number of records
 * @param {Number} options.offset - Offset for pagination
 * @param {String} options.order - Order by field
 * @returns {Promise<Array>} - List of products
 */
productsAPI.getProducts = async (options = {}) => {
  const {
    domain = [['sale_ok', '=', true]], // Default: products that can be sold
    fields = [
      'id',
      'name',
      'default_code', // Internal reference
      'list_price',
      'standard_price',
      'qty_available',
      'virtual_available',
      'image_128',
      'categ_id',
      'product_brand_id',
      'barcode',
      'weight',
      'volume',
      'sale_ok',
      'purchase_ok',
      'type',
      'tracking',
      'description_sale',
      'currency_id',
      'uom_id',
      'uom_po_id'
    ],
    limit = 50,
    offset = 0,
    order = 'name asc'
  } = options;

  console.log('Fetching products with domain filter');
  console.log('Using domain filter:', JSON.stringify(domain));

  const params = {
    fields: JSON.stringify(fields),
    limit,
    offset,
    order
  };

  // Always add domain filter
  if (domain && domain.length > 0) {
    params.domain = JSON.stringify(domain);
  }

  const data = await makeApiRequest('get', '/api/v2/search_read/product.product', params);

  if (data) {
    console.log(`Retrieved ${data.length} products`);
    return data;
  }

  return [];
};

/**
 * Fetch a single product by ID
 * @param {Number} productId - Product ID
 * @param {Array} fields - Fields to fetch
 * @returns {Promise<Object>} - Product details
 */
productsAPI.getProduct = async (
  productId,
  fields = [
    'id',
    'name',
    'default_code',
    'list_price',
    'standard_price',
    'qty_available',
    'virtual_available',
    'image_1920',
    'categ_id',
    'product_brand_id',
    'barcode',
    'weight',
    'volume',
    'sale_ok',
    'purchase_ok',
    'type',
    'tracking',
    'description_sale',
    'description_purchase',
    'currency_id',
    'uom_id',
    'uom_po_id',
    'seller_ids',
    'route_ids',
    'product_template_attribute_value_ids'
  ]
) => {
  console.log(`Fetching product ${productId}`);

  const params = {
    ids: JSON.stringify([productId]),
    fields: JSON.stringify(fields)
  };

  const data = await makeApiRequest('get', '/api/v2/read/product.product', params);

  if (data && data.length > 0) {
    console.log(`Retrieved product: ${data[0].name}`);
    return data[0];
  }

  return null;
};

/**
 * Search products by text
 * @param {String} searchText - Search query
 * @param {Object} options - Additional options
 * @returns {Promise<Array>} - Matching products
 */
productsAPI.searchProducts = async (searchText, options = {}) => {
  const { limit = 20, offset = 0 } = options;
  
  const searchDomain = [
    '&',
    ['sale_ok', '=', true],
    '|', '|', '|',
    ['name', 'ilike', searchText],
    ['default_code', 'ilike', searchText],
    ['barcode', 'ilike', searchText],
    ['description_sale', 'ilike', searchText]
  ];

  return productsAPI.getProducts({
    domain: searchDomain,
    limit,
    offset,
    order: 'name asc'
  });
};

/**
 * Get product categories
 * @param {Object} options - Query options
 * @returns {Promise<Array>} - List of categories
 */
productsAPI.getCategories = async (options = {}) => {
  const {
    domain = [],
    fields = ['id', 'name', 'parent_id', 'child_id', 'product_count'],
    limit = 100,
    offset = 0
  } = options;

  const params = {
    fields: JSON.stringify(fields),
    limit,
    offset,
    order: 'name asc'
  };

  if (domain && domain.length > 0) {
    params.domain = JSON.stringify(domain);
  }

  const data = await makeApiRequest('get', '/api/v2/search_read/product.category', params);

  if (data) {
    console.log(`Retrieved ${data.length} categories`);
    return data;
  }

  return [];
};

/**
 * Get products by category
 * @param {Number} categoryId - Category ID
 * @param {Object} options - Additional options
 * @returns {Promise<Array>} - Products in category
 */
productsAPI.getProductsByCategory = async (categoryId, options = {}) => {
  const domain = [
    ['sale_ok', '=', true],
    ['categ_id', 'child_of', categoryId]
  ];

  return productsAPI.getProducts({
    ...options,
    domain
  });
};

/**
 * Get product variants for a template
 * @param {Number} templateId - Product template ID
 * @param {Object} options - Additional options
 * @returns {Promise<Array>} - Product variants
 */
productsAPI.getProductVariants = async (templateId, options = {}) => {
  const domain = [
    ['product_tmpl_id', '=', templateId]
  ];

  return productsAPI.getProducts({
    ...options,
    domain
  });
};

/**
 * Update product stock quantity
 * @param {Number} productId - Product ID
 * @param {Number} quantity - New quantity
 * @param {String} reason - Reason for change
 * @returns {Promise<Object>} - Update result
 */
productsAPI.updateStock = async (productId, quantity, reason = 'Mobile App Update') => {
  console.log(`Updating stock for product ${productId} to ${quantity}`);

  const data = {
    product_id: productId,
    product_qty: quantity,
    reason: reason
  };

  const result = await makeApiRequest('post', '/api/v2/create/stock.quant', null, { values: data });

  if (result) {
    console.log('Stock updated successfully');
    return result;
  }

  return null;
};

/**
 * Create a new product
 * @param {Object} productData - Product data
 * @returns {Promise<Object>} - Created product
 */
productsAPI.createProduct = async (productData) => {
  console.log('Creating new product');

  const result = await makeApiRequest('post', '/api/v2/create/product.product', null, { values: productData });

  if (result) {
    console.log('Product created successfully');
    return result;
  }

  return null;
};

/**
 * Update an existing product
 * @param {Number} productId - Product ID
 * @param {Object} productData - Updated product data
 * @returns {Promise<Object>} - Update result
 */
productsAPI.updateProduct = async (productId, productData) => {
  console.log(`Updating product ${productId}`);

  const result = await makeApiRequest('put', '/api/v2/write/product.product', null, {
    ids: [productId],
    values: productData
  });

  if (result) {
    console.log('Product updated successfully');
    return result;
  }

  return null;
};

/**
 * Get product stock movements
 * @param {Number} productId - Product ID
 * @param {Object} options - Additional options
 * @returns {Promise<Array>} - Stock movements
 */
productsAPI.getStockMovements = async (productId, options = {}) => {
  const {
    limit = 50,
    offset = 0
  } = options;

  const domain = [
    ['product_id', '=', productId],
    ['state', '=', 'done']
  ];

  const params = {
    domain: JSON.stringify(domain),
    fields: JSON.stringify([
      'id',
      'name',
      'product_id',
      'product_qty',
      'product_uom',
      'location_id',
      'location_dest_id',
      'date',
      'origin',
      'reference'
    ]),
    limit,
    offset,
    order: 'date desc'
  };

  const data = await makeApiRequest('get', '/api/v2/search_read/stock.move', params);

  if (data) {
    console.log(`Retrieved ${data.length} stock movements`);
    return data;
  }

  return [];
};

/**
 * Get product pricing information
 * @param {Number} productId - Product ID
 * @param {Number} quantity - Quantity for pricing
 * @param {Number} partnerId - Customer ID for special pricing
 * @returns {Promise<Object>} - Pricing information
 */
productsAPI.getProductPricing = async (productId, quantity = 1, partnerId = null) => {
  console.log(`Getting pricing for product ${productId}`);

  const params = {
    product_id: productId,
    quantity,
    partner_id: partnerId
  };

  const data = await makeApiRequest('post', '/api/v2/call/product.product/price_get', params);

  if (data) {
    console.log('Retrieved pricing information');
    return data;
  }

  return null;
};

/**
 * Get low stock products
 * @param {Number} threshold - Stock threshold
 * @returns {Promise<Array>} - Low stock products
 */
productsAPI.getLowStockProducts = async (threshold = 10) => {
  const domain = [
    ['sale_ok', '=', true],
    ['qty_available', '<=', threshold],
    ['type', '=', 'product'] // Only stockable products
  ];

  return productsAPI.getProducts({
    domain,
    order: 'qty_available asc'
  });
};

// Compatibility methods for existing code
productsAPI.getList = productsAPI.getProducts;
productsAPI.getById = productsAPI.getProduct;
productsAPI.search = productsAPI.searchProducts;

// Cache methods for background sync compatibility
productsAPI.getProductsFromCache = async () => {
  // Return empty array for now - background sync will handle this
  return [];
};

export { productsAPI };
export default productsAPI;
