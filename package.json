{"name": "exomobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.5", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/netinfo": "^11.3.0", "@react-native-picker/picker": "^2.11.0", "@react-navigation/drawer": "^6.6.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@shopify/flash-list": "1.7.6", "axios": "^1.6.0", "expo": "53.0.9", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.9", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-image-picker": "^16.1.4", "expo-media-library": "~17.1.6", "expo-sharing": "~13.1.5", "expo-status-bar": "~2.2.3", "metro": "^0.82.0", "metro-config": "^0.82.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-blob-util": "^0.21.2", "react-native-fast-image": "^8.6.3", "react-native-file-viewer": "^2.1.5", "react-native-gesture-handler": "~2.24.0", "react-native-pdf": "^6.7.7", "react-native-reanimated": "~3.17.4", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-native-webview": "^13.13.5", "sanitize-html": "^2.17.0", "winston": "^3.17.0"}, "devDependencies": {"@babel/core": "^7.24.0", "@types/react": "~19.0.10", "typescript": "^5.1.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}, "private": true}