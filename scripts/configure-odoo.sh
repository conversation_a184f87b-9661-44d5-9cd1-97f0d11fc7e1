#!/bin/bash
# configure-odoo.sh - Script to configure the Odoo connection

set -e  # Exit on error

# Default values
SERVER_URL="http://localhost:8018"
DATABASE="OCR"
USERNAME="admin"
PASSWORD="admin"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --url)
      SERVER_URL="$2"
      shift 2
      ;;
    --db)
      DATABASE="$2"
      shift 2
      ;;
    --username)
      USERNAME="$2"
      shift 2
      ;;
    --password)
      PASSWORD="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

echo "Configuring Odoo connection with:"
echo "  Server URL: $SERVER_URL"
echo "  Database: $DATABASE"
echo "  Username: $USERNAME"
echo "  Password: ********"

# Create or update Odoo configuration file
CONFIG_DIR="src/config"
mkdir -p "$CONFIG_DIR"

echo "// Odoo configuration
// Auto-generated by configure-odoo.sh

export const ODOO_CONFIG = {
  baseURL: '$SERVER_URL',
  db: '$DATABASE',
  username: '$USERNAME',
  password: '$PASSWORD',
};" > "$CONFIG_DIR/odoo.js"

echo "Odoo configuration saved to $CONFIG_DIR/odoo.js"

# Create a script to test the Odoo connection
TEST_SCRIPT="scripts/test-odoo-connection.js"

echo "// Test script for Odoo connection
// Run with: node $TEST_SCRIPT

const axios = require('axios');

const config = {
  baseURL: '$SERVER_URL',
  db: '$DATABASE',
  username: '$USERNAME',
  password: '$PASSWORD',
};

async function testConnection() {
  console.log('Testing connection to Odoo server...');
  
  try {
    // Test authentication
    const response = await axios.post(\`\${config.baseURL}/web/session/authenticate\`, {
      jsonrpc: '2.0',
      params: {
        db: config.db,
        login: config.username,
        password: config.password,
      },
    });
    
    if (response.data.error) {
      console.error('Authentication error:', response.data.error);
      return false;
    }
    
    console.log('Authentication successful!');
    console.log('User info:', response.data.result);
    
    return true;
  } catch (error) {
    console.error('Connection error:', error.message);
    return false;
  }
}

testConnection()
  .then(success => {
    if (success) {
      console.log('Connection test passed!');
    } else {
      console.log('Connection test failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
" > "$TEST_SCRIPT"

echo "Created Odoo connection test script at $TEST_SCRIPT"
echo "You can test the connection by running: node $TEST_SCRIPT"

# Make the script executable
chmod +x "$TEST_SCRIPT"

# Create a models directory to store Odoo model API files
MODELS_DIR="src/api/models"
mkdir -p "$MODELS_DIR"

# Create a basic template for model API
echo "// Template for Odoo model API
// Copy this file to create APIs for specific models

import odooClient, { odooAPI } from '../odooClient';

/**
 * API service for a specific Odoo model
 * @param {string} modelName - The Odoo model name (e.g., 'res.partner')
 */
export const createModelAPI = (modelName) => {
  return {
    /**
     * Get list of records
     * @param {Array} domain - Search domain
     * @param {Array} fields - Fields to fetch
     * @param {number} limit - Maximum number of records
     * @param {number} offset - Offset for pagination
     * @param {boolean} forceRefresh - Force refresh from server
     */
    getList: (domain = [], fields = ['id', 'name'], limit = 80, offset = 0, forceRefresh = false) => {
      return odooAPI.searchRead(modelName, domain, fields, limit, offset, forceRefresh);
    },
    
    /**
     * Get single record by ID
     * @param {number} id - Record ID
     * @param {Array} fields - Fields to fetch
     * @param {boolean} forceRefresh - Force refresh from server
     */
    getById: (id, fields = [], forceRefresh = false) => {
      return odooAPI.read(modelName, id, fields, forceRefresh);
    },
    
    /**
     * Create new record
     * @param {Object} data - Record data
     */
    create: (data) => {
      return odooAPI.create(modelName, data);
    },
    
    /**
     * Update record
     * @param {number} id - Record ID
     * @param {Object} data - Update data
     */
    update: (id, data) => {
      return odooAPI.update(modelName, id, data);
    },
    
    /**
     * Delete record
     * @param {number} id - Record ID
     */
    delete: (id) => {
      return odooAPI.delete(modelName, id);
    },
    
    /**
     * Call model method
     * @param {number} id - Record ID
     * @param {string} method - Method name
     * @param {Array} args - Positional arguments
     * @param {Object} kwargs - Keyword arguments
     */
    callMethod: (id, method, args = [], kwargs = {}) => {
      return odooAPI.callMethod(modelName, method, id, args, kwargs);
    },
    
    /**
     * Get model fields
     * @param {Array} attributes - Field attributes to fetch
     * @param {boolean} forceRefresh - Force refresh from server
     */
    getFields: (attributes = ['type', 'string', 'required', 'selection', 'relation'], forceRefresh = false) => {
      return odooAPI.getFields(modelName, attributes, forceRefresh);
    },
  };
};

// Example usage:
// export const partnersAPI = createModelAPI('res.partner');
" > "$MODELS_DIR/modelApiTemplate.js"

# Create a specific API for res.partner as an example
echo "// API for res.partner model

import { createModelAPI } from './modelApiTemplate';

export const partnersAPI = createModelAPI('res.partner');

// Add any custom methods for this specific model
export const getCustomerPartners = (limit = 20, offset = 0, forceRefresh = false) => {
  return partnersAPI.getList(
    [['customer_rank', '>', 0]], // Domain to filter customers
    ['id', 'name', 'email', 'phone', 'street', 'city', 'country_id'], // Fields
    limit,
    offset,
    forceRefresh
  );
};

export default partnersAPI;
" > "$MODELS_DIR/partnersApi.js"

echo "Created model API template and example at $MODELS_DIR"

echo "Odoo configuration completed!"
echo "You can now connect to your Odoo server at $SERVER_URL with the ExoMobile app."
