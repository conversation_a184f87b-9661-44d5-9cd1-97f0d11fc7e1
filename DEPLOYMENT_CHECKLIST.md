# ExoMobile Deployment Checklist

## ✅ Completed Setup

- [x] **Project Configuration**
  - [x] Updated `app.json` with complete configuration
  - [x] Enhanced `eas.json` for builds and submissions
  - [x] Created required app assets (icon, splash, favicon)

- [x] **Development Environment**
  - [x] Expo CLI installed and configured
  - [x] EAS CLI installed
  - [x] Logged in as `itmsadmin`
  - [x] Project ID configured: `1b9f1730-f28b-42b3-a17f-0916ec64d122`

## 🚀 Ready to Deploy

### Option 1: Quick Start with Expo Go

```bash
# Start development server
npx expo start

# Or use the deployment script
./deploy.sh
```

### Option 2: Publish to Expo Go

```bash
# Publish your app to Expo Go
npx expo publish

# Users can then scan QR code to test
```

### Option 3: Build for App Stores

```bash
# Build for iOS
eas build --platform ios --profile production

# Build for Android  
eas build --platform android --profile production

# Build for both
eas build --platform all --profile production
```

## 📋 Next Steps for App Store Deployment

### For iOS App Store:

1. **Apple Developer Account**
   - [ ] Enroll in Apple Developer Program ($99/year)
   - [ ] Create certificates and provisioning profiles
   - [ ] Update `eas.json` with your Apple ID and team ID

2. **App Store Connect**
   - [ ] Create new app in App Store Connect
   - [ ] Fill out app information
   - [ ] Upload screenshots (required sizes)
   - [ ] Set app pricing and availability

3. **Submit for Review**
   ```bash
   eas submit --platform ios --profile production
   ```

### For Google Play Store:

1. **Google Play Console**
   - [ ] Create Google Play Developer account ($25 one-time)
   - [ ] Create service account for API access
   - [ ] Download service account JSON file

2. **Store Listing**
   - [ ] Create new app in Play Console
   - [ ] Upload screenshots and assets
   - [ ] Fill out store listing information
   - [ ] Set content rating

3. **Submit for Review**
   ```bash
   eas submit --platform android --profile production
   ```

## 🔧 Configuration Updates Needed

### Update eas.json for your accounts:

```json
{
  "submit": {
    "production": {
      "ios": {
        "appleId": "<EMAIL>",
        "ascAppId": "your-app-store-connect-app-id",
        "appleTeamId": "your-apple-team-id"
      },
      "android": {
        "serviceAccountKeyPath": "./google-service-account.json",
        "track": "internal"
      }
    }
  }
}
```

## 📱 Testing Strategy

### 1. Local Testing
- [ ] Test on iOS simulator
- [ ] Test on Android emulator
- [ ] Test on physical devices

### 2. Expo Go Testing
- [ ] Publish to Expo Go
- [ ] Test on multiple devices
- [ ] Verify all features work

### 3. Production Testing
- [ ] Build production versions
- [ ] Test production builds
- [ ] Verify app store compliance

## 🎨 Asset Improvements

The current assets are basic placeholders. Consider:

- [ ] Create professional app icon (1024x1024)
- [ ] Design branded splash screen
- [ ] Create app store screenshots
- [ ] Add app store preview videos

## 📊 App Store Requirements

### iOS App Store:
- [ ] App icon (1024x1024)
- [ ] Screenshots for all device sizes
- [ ] App description and keywords
- [ ] Privacy policy URL
- [ ] Support URL

### Google Play Store:
- [ ] High-res icon (512x512)
- [ ] Feature graphic (1024x500)
- [ ] Screenshots for phones and tablets
- [ ] App description
- [ ] Privacy policy

## 🚨 Important Notes

1. **Bundle Identifiers**: 
   - iOS: `com.exomobile.app`
   - Android: `com.exomobile.app`

2. **Version Management**:
   - Current version: `1.0.0`
   - iOS build number: `1`
   - Android version code: `1`

3. **Permissions**:
   - Camera access for attachments
   - Photo library access
   - Document picker access
   - Network access for API calls

## 🛠 Useful Commands

```bash
# Check build status
eas build:list

# Check submission status  
eas submit:list

# View project info
npx expo config

# Clear cache if needed
npx expo r -c

# Use deployment script
./deploy.sh
```

## 📞 Support

If you encounter issues:
1. Check the deployment guide: `deployment-guide.md`
2. Use the deployment script: `./deploy.sh`
3. Check Expo documentation: https://docs.expo.dev/
4. Check EAS documentation: https://docs.expo.dev/eas/
