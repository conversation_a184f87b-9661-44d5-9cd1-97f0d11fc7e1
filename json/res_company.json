{"model": "res.company", "fields": [{"name": "is_company_details_empty", "type": "boolean", "label": "Is Company Details Empty", "required": false, "relation": ""}, {"name": "snailmail_duplex", "type": "boolean", "label": "Both sides", "required": false, "relation": ""}, {"name": "report_footer", "type": "html", "label": "Report Footer", "required": false, "relation": ""}, {"name": "account_journal_early_pay_discount_gain_account_id", "type": "many2one", "label": "Cash Discount Write-Off Gain Account", "required": false, "relation": "account.account"}, {"name": "invoice_is_ubl_cii", "type": "boolean", "label": "Generate Peppol format by default", "required": false, "relation": ""}, {"name": "social_twitter", "type": "char", "label": "Twitter Account", "required": false, "relation": ""}, {"name": "expense_product_id", "type": "many2one", "label": "Default Expense Category", "required": false, "relation": "product.product"}, {"name": "message_has_sms_error", "type": "boolean", "label": "SMS Delivery error", "required": false, "relation": ""}, {"name": "account_storno", "type": "boolean", "label": "Storno accounting", "required": false, "relation": ""}, {"name": "currency_exchange_journal_id", "type": "many2one", "label": "Exchange Gain or Loss Journal", "required": false, "relation": "account.journal"}, {"name": "expense_currency_exchange_account_id", "type": "many2one", "label": "Loss Exchange Rate Account", "required": false, "relation": "account.account"}, {"name": "sequence", "type": "integer", "label": "Sequence", "required": false, "relation": ""}, {"name": "website_id", "type": "many2one", "label": "Website", "required": false, "relation": "website"}, {"name": "bank_ids", "type": "one2many", "label": "Banks", "required": false, "relation": "res.partner.bank"}, {"name": "account_enabled_tax_country_ids", "type": "many2many", "label": "l10n-used countries", "required": false, "relation": "res.country"}, {"name": "color", "type": "integer", "label": "Color", "required": false, "relation": ""}, {"name": "overtime_company_threshold", "type": "integer", "label": "Tolerance Time In Favor Of Company", "required": false, "relation": ""}, {"name": "account_discount_expense_allocation_id", "type": "many2one", "label": "Separate account for expense discount", "required": false, "relation": "account.account"}, {"name": "attendance_kiosk_key", "type": "char", "label": "Attendance Kiosk Key", "required": false, "relation": ""}, {"name": "expense_journal_id", "type": "many2one", "label": "Default Expense Journal", "required": false, "relation": "account.journal"}, {"name": "default_cash_difference_income_account_id", "type": "many2one", "label": "Cash Difference Income", "required": false, "relation": "account.account"}, {"name": "all_child_ids", "type": "one2many", "label": "All Child", "required": false, "relation": "res.company"}, {"name": "email_primary_color", "type": "char", "label": "<PERSON><PERSON> Header Color", "required": false, "relation": ""}, {"name": "income_currency_exchange_account_id", "type": "many2one", "label": "Gain Exchange Rate Account", "required": false, "relation": "account.account"}, {"name": "snailmail_cover", "type": "boolean", "label": "Add a Cover Page", "required": false, "relation": ""}, {"name": "message_has_error", "type": "boolean", "label": "Message Delivery error", "required": false, "relation": ""}, {"name": "logo", "type": "binary", "label": "Company Logo", "required": false, "relation": ""}, {"name": "account_use_credit_limit", "type": "boolean", "label": "Sales Credit Limit", "required": false, "relation": ""}, {"name": "attendance_kiosk_mode", "type": "selection", "label": "Attendance Mode", "required": false, "relation": ""}, {"name": "website", "type": "char", "label": "Website Link", "required": false, "relation": ""}, {"name": "invoice_is_email", "type": "boolean", "label": "Email by default", "required": false, "relation": ""}, {"name": "leave_timesheet_task_id", "type": "many2one", "label": "Time Off Task", "required": false, "relation": "project.task"}, {"name": "social_instagram", "type": "char", "label": "Instagram Account", "required": false, "relation": ""}, {"name": "account_sale_tax_id", "type": "many2one", "label": "Default Sale Tax", "required": false, "relation": "account.tax"}, {"name": "project_time_mode_id", "type": "many2one", "label": "Project Time Unit", "required": false, "relation": "uom.uom"}, {"name": "mobile", "type": "char", "label": "Mobile", "required": false, "relation": ""}, {"name": "chart_template", "type": "selection", "label": "Chart Template", "required": false, "relation": ""}, {"name": "alias_domain_name", "type": "char", "label": "Alias Domain Name", "required": false, "relation": ""}, {"name": "tax_calculation_rounding_method", "type": "selection", "label": "Tax Calculation Rounding Method", "required": false, "relation": ""}, {"name": "account_journal_payment_debit_account_id", "type": "many2one", "label": "Journal Outstanding Receipts", "required": false, "relation": "account.account"}, {"name": "country_id", "type": "many2one", "label": "Country", "required": false, "relation": "res.country"}, {"name": "account_discount_income_allocation_id", "type": "many2one", "label": "Separate account for income discount", "required": false, "relation": "account.account"}, {"name": "resource_calendar_id", "type": "many2one", "label": "Default Working Hours", "required": false, "relation": "resource.calendar"}, {"name": "uses_default_logo", "type": "boolean", "label": "Uses Default Logo", "required": false, "relation": ""}, {"name": "iap_enrich_auto_done", "type": "boolean", "label": "<PERSON><PERSON>", "required": false, "relation": ""}, {"name": "account_purchase_tax_id", "type": "many2one", "label": "Default Purchase Tax", "required": false, "relation": "account.tax"}, {"name": "social_tiktok", "type": "char", "label": "Tik<PERSON><PERSON> Account", "required": false, "relation": ""}, {"name": "company_details", "type": "html", "label": "Company Details", "required": false, "relation": ""}, {"name": "active", "type": "boolean", "label": "Active", "required": false, "relation": ""}, {"name": "alias_domain_id", "type": "many2one", "label": "Email Domain", "required": false, "relation": "mail.alias.domain"}, {"name": "message_is_follower", "type": "boolean", "label": "Is Follower", "required": false, "relation": ""}, {"name": "invoice_is_snailmail", "type": "boolean", "label": "Send by Post", "required": false, "relation": ""}, {"name": "quick_edit_mode", "type": "selection", "label": "Quick encoding", "required": false, "relation": ""}, {"name": "website_message_ids", "type": "one2many", "label": "Website Messages", "required": false, "relation": "mail.message"}, {"name": "email_secondary_color", "type": "char", "label": "Em<PERSON>", "required": false, "relation": ""}, {"name": "account_cash_basis_base_account_id", "type": "many2one", "label": "Base Tax Received Account", "required": false, "relation": "account.account"}, {"name": "hr_presence_control_ip_list", "type": "char", "label": "Valid IP addresses", "required": false, "relation": ""}, {"name": "write_uid", "type": "many2one", "label": "Last Updated by", "required": false, "relation": "res.users"}, {"name": "fiscalyear_last_day", "type": "integer", "label": "Fiscalyear Last Day", "required": true, "relation": ""}, {"name": "account_journal_suspense_account_id", "type": "many2one", "label": "Journal Suspense Account", "required": false, "relation": "account.account"}, {"name": "bounce_email", "type": "char", "label": "<PERSON><PERSON><PERSON>", "required": false, "relation": ""}, {"name": "invoice_terms_html", "type": "html", "label": "Default Terms and Conditions as a Web page", "required": false, "relation": ""}, {"name": "layout_background_image", "type": "binary", "label": "Background Image", "required": false, "relation": ""}, {"name": "bounce_formatted", "type": "char", "label": "<PERSON><PERSON><PERSON>", "required": false, "relation": ""}, {"name": "timesheet_encode_uom_id", "type": "many2one", "label": "Timesheet Encoding Unit", "required": false, "relation": "uom.uom"}, {"name": "parent_ids", "type": "many2many", "label": "Parent", "required": false, "relation": "res.company"}, {"name": "email", "type": "char", "label": "Email", "required": false, "relation": ""}, {"name": "catchall_email", "type": "char", "label": "<PERSON><PERSON>", "required": false, "relation": ""}, {"name": "company_expense_allowed_payment_method_line_ids", "type": "many2many", "label": "Payment methods available for expenses paid by company", "required": false, "relation": "account.payment.method.line"}, {"name": "expects_chart_of_accounts", "type": "boolean", "label": "Expects a Chart of Accounts", "required": false, "relation": ""}, {"name": "attendance_kiosk_url", "type": "char", "label": "Attendance Kiosk Url", "required": false, "relation": ""}, {"name": "hr_presence_last_compute_date", "type": "datetime", "label": "Hr Presence Last Compute Date", "required": false, "relation": ""}, {"name": "tax_cash_basis_journal_id", "type": "many2one", "label": "Cash Basis Journal", "required": false, "relation": "account.journal"}, {"name": "attendance_barcode_source", "type": "selection", "label": "Barcode Source", "required": false, "relation": ""}, {"name": "snailmail_color", "type": "boolean", "label": "Snailmail Color", "required": false, "relation": ""}, {"name": "create_date", "type": "datetime", "label": "Created on", "required": false, "relation": ""}, {"name": "account_fiscal_country_id", "type": "many2one", "label": "Fiscal Country", "required": false, "relation": "res.country"}, {"name": "hr_attendance_overtime", "type": "boolean", "label": "Count Extra Hours", "required": false, "relation": ""}, {"name": "display_name", "type": "char", "label": "Display Name", "required": false, "relation": ""}, {"name": "account_default_pos_receivable_account_id", "type": "many2one", "label": "Default PoS Receivable Account", "required": false, "relation": "account.account"}, {"name": "message_needaction_counter", "type": "integer", "label": "Number of Actions", "required": false, "relation": ""}, {"name": "default_from_email", "type": "char", "label": "De<PERSON><PERSON> From", "required": false, "relation": ""}, {"name": "catchall_formatted", "type": "char", "label": "<PERSON><PERSON>", "required": false, "relation": ""}, {"name": "nomenclature_id", "type": "many2one", "label": "Nomenclature", "required": false, "relation": "barcode.nomenclature"}, {"name": "root_id", "type": "many2one", "label": "Root", "required": false, "relation": "res.company"}, {"name": "transfer_account_id", "type": "many2one", "label": "Inter-Banks Transfer Account", "required": false, "relation": "account.account"}, {"name": "message_needaction", "type": "boolean", "label": "Action Needed", "required": false, "relation": ""}, {"name": "social_linkedin", "type": "char", "label": "LinkedIn Account", "required": false, "relation": ""}, {"name": "fiscalyear_last_month", "type": "selection", "label": "Fiscalyear Last Month", "required": true, "relation": ""}, {"name": "country_code", "type": "char", "label": "Country Code", "required": false, "relation": ""}, {"name": "city", "type": "char", "label": "City", "required": false, "relation": ""}, {"name": "currency_id", "type": "many2one", "label": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "relation": "res.currency"}, {"name": "attendance_from_systray", "type": "boolean", "label": "Attendance From Systray", "required": false, "relation": ""}, {"name": "overtime_employee_threshold", "type": "integer", "label": "Tolerance Time In Favor Of Employee", "required": false, "relation": ""}, {"name": "fiscal_position_ids", "type": "one2many", "label": "Fiscal Position", "required": false, "relation": "account.fiscal.position"}, {"name": "invoice_terms", "type": "html", "label": "Default Terms and Conditions", "required": false, "relation": ""}, {"name": "vat", "type": "char", "label": "Tax ID", "required": false, "relation": ""}, {"name": "max_tax_lock_date", "type": "date", "label": "Max Tax Lock Date", "required": false, "relation": ""}, {"name": "message_attachment_count", "type": "integer", "label": "Attachment Count", "required": false, "relation": ""}, {"name": "message_has_error_counter", "type": "integer", "label": "Number of errors", "required": false, "relation": ""}, {"name": "street2", "type": "char", "label": "Street2", "required": false, "relation": ""}, {"name": "multi_vat_foreign_country_ids", "type": "many2many", "label": "Foreign VAT countries", "required": false, "relation": "res.country"}, {"name": "logo_web", "type": "binary", "label": "Logo Web", "required": false, "relation": ""}, {"name": "message_ids", "type": "one2many", "label": "Messages", "required": false, "relation": "mail.message"}, {"name": "revenue_accrual_account_id", "type": "many2one", "label": "Revenue Accrual Account", "required": false, "relation": "account.account"}, {"name": "user_ids", "type": "many2many", "label": "Accepted Users", "required": false, "relation": "res.users"}, {"name": "partner_id", "type": "many2one", "label": "Partner", "required": true, "relation": "res.partner"}, {"name": "qr_code", "type": "boolean", "label": "Display QR-code on invoices", "required": false, "relation": ""}, {"name": "bank_account_code_prefix", "type": "char", "label": "Prefix of the bank accounts", "required": false, "relation": ""}, {"name": "account_opening_journal_id", "type": "many2one", "label": "Opening Journal", "required": false, "relation": "account.journal"}, {"name": "message_partner_ids", "type": "many2many", "label": "Followers (Partners)", "required": false, "relation": "res.partner"}, {"name": "write_date", "type": "datetime", "label": "Last Updated on", "required": false, "relation": ""}, {"name": "hr_attendance_display_overtime", "type": "boolean", "label": "Display Extra Hours", "required": false, "relation": ""}, {"name": "state_id", "type": "many2one", "label": "Fed. State", "required": false, "relation": "res.country.state"}, {"name": "email_formatted", "type": "char", "label": "Formatted Email", "required": false, "relation": ""}, {"name": "automatic_entry_default_journal_id", "type": "many2one", "label": "Automatic Entry Default Journal", "required": false, "relation": "account.journal"}, {"name": "cash_account_code_prefix", "type": "char", "label": "Prefix of the cash accounts", "required": false, "relation": ""}, {"name": "social_youtube", "type": "char", "label": "Youtube Account", "required": false, "relation": ""}, {"name": "external_report_layout_id", "type": "many2one", "label": "Document Template", "required": false, "relation": "ir.ui.view"}, {"name": "display_invoice_amount_total_words", "type": "boolean", "label": "Total amount of invoice in letters", "required": false, "relation": ""}, {"name": "id", "type": "integer", "label": "ID", "required": false, "relation": ""}, {"name": "account_opening_date", "type": "date", "label": "Opening Entry", "required": true, "relation": ""}, {"name": "attendance_kiosk_use_pin", "type": "boolean", "label": "Employee PIN Identification", "required": false, "relation": ""}, {"name": "layout_background", "type": "selection", "label": "Layout Background", "required": true, "relation": ""}, {"name": "account_journal_early_pay_discount_loss_account_id", "type": "many2one", "label": "Cash Discount Write-Off Loss Account", "required": false, "relation": "account.account"}, {"name": "parent_id", "type": "many2one", "label": "Parent Company", "required": false, "relation": "res.company"}, {"name": "parent_path", "type": "char", "label": "Parent Path", "required": false, "relation": ""}, {"name": "primary_color", "type": "char", "label": "Primary Color", "required": false, "relation": ""}, {"name": "terms_type", "type": "selection", "label": "Terms & Conditions format", "required": false, "relation": ""}, {"name": "payment_onboarding_payment_method", "type": "selection", "label": "Selected onboarding payment method", "required": false, "relation": ""}, {"name": "transfer_account_code_prefix", "type": "char", "label": "Prefix of the transfer accounts", "required": false, "relation": ""}, {"name": "attendance_kiosk_delay", "type": "integer", "label": "Attendance Kiosk Delay", "required": false, "relation": ""}, {"name": "account_journal_payment_credit_account_id", "type": "many2one", "label": "Journal Outstanding Payments", "required": false, "relation": "account.account"}, {"name": "phone", "type": "char", "label": "Phone", "required": false, "relation": ""}, {"name": "company_registry", "type": "char", "label": "Company ID", "required": false, "relation": ""}, {"name": "child_ids", "type": "one2many", "label": "Branches", "required": false, "relation": "res.company"}, {"name": "social_facebook", "type": "char", "label": "Facebook Account", "required": false, "relation": ""}, {"name": "rating_ids", "type": "one2many", "label": "Ratings", "required": false, "relation": "rating.rating"}, {"name": "partner_gid", "type": "integer", "label": "Company database ID", "required": false, "relation": ""}, {"name": "secondary_color", "type": "char", "label": "Secondary Color", "required": false, "relation": ""}, {"name": "paperformat_id", "type": "many2one", "label": "Paper format", "required": false, "relation": "report.paperformat"}, {"name": "appbar_image", "type": "binary", "label": "A<PERSON> Menu Footer Image", "required": false, "relation": ""}, {"name": "resource_calendar_ids", "type": "one2many", "label": "Working Hours", "required": false, "relation": "resource.calendar"}, {"name": "create_uid", "type": "many2one", "label": "Created by", "required": false, "relation": "res.users"}, {"name": "report_header", "type": "html", "label": "Company Tagline", "required": false, "relation": ""}, {"name": "period_lock_date", "type": "date", "label": "Journals Entries Lock Date", "required": false, "relation": ""}, {"name": "has_message", "type": "boolean", "label": "Has Message", "required": false, "relation": ""}, {"name": "bank_journal_ids", "type": "one2many", "label": "Bank Journals", "required": false, "relation": "account.journal"}, {"name": "anglo_saxon_accounting", "type": "boolean", "label": "Use anglo-saxon accounting", "required": false, "relation": ""}, {"name": "zip", "type": "char", "label": "Zip", "required": false, "relation": ""}, {"name": "invoice_is_download", "type": "boolean", "label": "Download by default", "required": false, "relation": ""}, {"name": "tax_exigibility", "type": "boolean", "label": "Use Cash Basis", "required": false, "relation": ""}, {"name": "account_opening_move_id", "type": "many2one", "label": "Opening Journal Entry", "required": false, "relation": "account.move"}, {"name": "overtime_start_date", "type": "date", "label": "Extra Hours Starting Date", "required": false, "relation": ""}, {"name": "hr_presence_control_email_amount", "type": "integer", "label": "# emails to send", "required": false, "relation": ""}, {"name": "street", "type": "char", "label": "Street", "required": false, "relation": ""}, {"name": "expense_accrual_account_id", "type": "many2one", "label": "Expense Accrual Account", "required": false, "relation": "account.account"}, {"name": "font", "type": "selection", "label": "Font", "required": false, "relation": ""}, {"name": "tax_lock_date", "type": "date", "label": "Tax Return Lock Date", "required": false, "relation": ""}, {"name": "internal_project_id", "type": "many2one", "label": "Internal Project", "required": false, "relation": "project.project"}, {"name": "employee_properties_definition", "type": "properties_definition", "label": "Employee Properties", "required": false, "relation": ""}, {"name": "incoterm_id", "type": "many2one", "label": "De<PERSON>ult incoterm", "required": false, "relation": "account.incote<PERSON>"}, {"name": "message_follower_ids", "type": "one2many", "label": "Followers", "required": false, "relation": "mail.followers"}, {"name": "social_github", "type": "char", "label": "<PERSON><PERSON><PERSON><PERSON> Account", "required": false, "relation": ""}, {"name": "fiscalyear_lock_date", "type": "date", "label": "All Users Lock Date", "required": false, "relation": ""}, {"name": "name", "type": "char", "label": "Company Name", "required": true, "relation": ""}, {"name": "default_cash_difference_expense_account_id", "type": "many2one", "label": "Cash Difference Expense", "required": false, "relation": "account.account"}], "relationships": [{"from_model": "res.company", "to_model": "account.account", "field": "account_cash_basis_base_account_id", "type": "many2one", "label": "Base Tax Received Account"}, {"from_model": "res.company", "to_model": "account.account", "field": "account_default_pos_receivable_account_id", "type": "many2one", "label": "Default PoS Receivable Account"}, {"from_model": "res.company", "to_model": "account.account", "field": "account_discount_expense_allocation_id", "type": "many2one", "label": "Separate account for expense discount"}, {"from_model": "res.company", "to_model": "account.account", "field": "account_discount_income_allocation_id", "type": "many2one", "label": "Separate account for income discount"}, {"from_model": "res.company", "to_model": "res.country", "field": "account_fiscal_country_id", "type": "many2one", "label": "Fiscal Country"}, {"from_model": "res.company", "to_model": "account.account", "field": "account_journal_early_pay_discount_gain_account_id", "type": "many2one", "label": "Cash Discount Write-Off Gain Account"}, {"from_model": "res.company", "to_model": "account.account", "field": "account_journal_early_pay_discount_loss_account_id", "type": "many2one", "label": "Cash Discount Write-Off Loss Account"}, {"from_model": "res.company", "to_model": "account.account", "field": "account_journal_payment_credit_account_id", "type": "many2one", "label": "Journal Outstanding Payments"}, {"from_model": "res.company", "to_model": "account.account", "field": "account_journal_payment_debit_account_id", "type": "many2one", "label": "Journal Outstanding Receipts"}, {"from_model": "res.company", "to_model": "account.account", "field": "account_journal_suspense_account_id", "type": "many2one", "label": "Journal Suspense Account"}, {"from_model": "res.company", "to_model": "account.journal", "field": "account_opening_journal_id", "type": "many2one", "label": "Opening Journal"}, {"from_model": "res.company", "to_model": "account.move", "field": "account_opening_move_id", "type": "many2one", "label": "Opening Journal Entry"}, {"from_model": "res.company", "to_model": "account.tax", "field": "account_purchase_tax_id", "type": "many2one", "label": "Default Purchase Tax"}, {"from_model": "res.company", "to_model": "account.tax", "field": "account_sale_tax_id", "type": "many2one", "label": "Default Sale Tax"}, {"from_model": "res.company", "to_model": "mail.alias.domain", "field": "alias_domain_id", "type": "many2one", "label": "Email Domain"}, {"from_model": "res.company", "to_model": "account.journal", "field": "automatic_entry_default_journal_id", "type": "many2one", "label": "Automatic Entry Default Journal"}, {"from_model": "res.company", "to_model": "res.country", "field": "country_id", "type": "many2one", "label": "Country"}, {"from_model": "res.company", "to_model": "res.users", "field": "create_uid", "type": "many2one", "label": "Created by"}, {"from_model": "res.company", "to_model": "account.journal", "field": "currency_exchange_journal_id", "type": "many2one", "label": "Exchange Gain or Loss Journal"}, {"from_model": "res.company", "to_model": "res.currency", "field": "currency_id", "type": "many2one", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"from_model": "res.company", "to_model": "account.account", "field": "default_cash_difference_expense_account_id", "type": "many2one", "label": "Cash Difference Expense"}, {"from_model": "res.company", "to_model": "account.account", "field": "default_cash_difference_income_account_id", "type": "many2one", "label": "Cash Difference Income"}, {"from_model": "res.company", "to_model": "account.account", "field": "expense_accrual_account_id", "type": "many2one", "label": "Expense Accrual Account"}, {"from_model": "res.company", "to_model": "account.account", "field": "expense_currency_exchange_account_id", "type": "many2one", "label": "Loss Exchange Rate Account"}, {"from_model": "res.company", "to_model": "account.journal", "field": "expense_journal_id", "type": "many2one", "label": "Default Expense Journal"}, {"from_model": "res.company", "to_model": "product.product", "field": "expense_product_id", "type": "many2one", "label": "Default Expense Category"}, {"from_model": "res.company", "to_model": "ir.ui.view", "field": "external_report_layout_id", "type": "many2one", "label": "Document Template"}, {"from_model": "res.company", "to_model": "account.account", "field": "income_currency_exchange_account_id", "type": "many2one", "label": "Gain Exchange Rate Account"}, {"from_model": "res.company", "to_model": "account.incote<PERSON>", "field": "incoterm_id", "type": "many2one", "label": "De<PERSON>ult incoterm"}, {"from_model": "res.company", "to_model": "project.project", "field": "internal_project_id", "type": "many2one", "label": "Internal Project"}, {"from_model": "res.company", "to_model": "project.task", "field": "leave_timesheet_task_id", "type": "many2one", "label": "Time Off Task"}, {"from_model": "res.company", "to_model": "barcode.nomenclature", "field": "nomenclature_id", "type": "many2one", "label": "Nomenclature"}, {"from_model": "res.company", "to_model": "report.paperformat", "field": "paperformat_id", "type": "many2one", "label": "Paper format"}, {"from_model": "res.company", "to_model": "res.company", "field": "parent_id", "type": "many2one", "label": "Parent Company"}, {"from_model": "res.company", "to_model": "res.partner", "field": "partner_id", "type": "many2one", "label": "Partner"}, {"from_model": "res.company", "to_model": "uom.uom", "field": "project_time_mode_id", "type": "many2one", "label": "Project Time Unit"}, {"from_model": "res.company", "to_model": "resource.calendar", "field": "resource_calendar_id", "type": "many2one", "label": "Default Working Hours"}, {"from_model": "res.company", "to_model": "account.account", "field": "revenue_accrual_account_id", "type": "many2one", "label": "Revenue Accrual Account"}, {"from_model": "res.company", "to_model": "res.company", "field": "root_id", "type": "many2one", "label": "Root"}, {"from_model": "res.company", "to_model": "res.country.state", "field": "state_id", "type": "many2one", "label": "Fed. State"}, {"from_model": "res.company", "to_model": "account.journal", "field": "tax_cash_basis_journal_id", "type": "many2one", "label": "Cash Basis Journal"}, {"from_model": "res.company", "to_model": "uom.uom", "field": "timesheet_encode_uom_id", "type": "many2one", "label": "Timesheet Encoding Unit"}, {"from_model": "res.company", "to_model": "account.account", "field": "transfer_account_id", "type": "many2one", "label": "Inter-Banks Transfer Account"}, {"from_model": "res.company", "to_model": "website", "field": "website_id", "type": "many2one", "label": "Website"}, {"from_model": "res.company", "to_model": "res.users", "field": "write_uid", "type": "many2one", "label": "Last Updated by"}, {"from_model": "res.company", "to_model": "res.company", "field": "all_child_ids", "type": "one2many", "label": "All Child"}, {"from_model": "res.company", "to_model": "res.partner.bank", "field": "bank_ids", "type": "one2many", "label": "Banks"}, {"from_model": "res.company", "to_model": "account.journal", "field": "bank_journal_ids", "type": "one2many", "label": "Bank Journals"}, {"from_model": "res.company", "to_model": "res.company", "field": "child_ids", "type": "one2many", "label": "Branches"}, {"from_model": "res.company", "to_model": "account.fiscal.position", "field": "fiscal_position_ids", "type": "one2many", "label": "Fiscal Position"}, {"from_model": "res.company", "to_model": "mail.followers", "field": "message_follower_ids", "type": "one2many", "label": "Followers"}, {"from_model": "res.company", "to_model": "mail.message", "field": "message_ids", "type": "one2many", "label": "Messages"}, {"from_model": "res.company", "to_model": "rating.rating", "field": "rating_ids", "type": "one2many", "label": "Ratings"}, {"from_model": "res.company", "to_model": "resource.calendar", "field": "resource_calendar_ids", "type": "one2many", "label": "Working Hours"}, {"from_model": "res.company", "to_model": "mail.message", "field": "website_message_ids", "type": "one2many", "label": "Website Messages"}, {"from_model": "res.company", "to_model": "res.country", "field": "account_enabled_tax_country_ids", "type": "many2many", "label": "l10n-used countries"}, {"from_model": "res.company", "to_model": "account.payment.method.line", "field": "company_expense_allowed_payment_method_line_ids", "type": "many2many", "label": "Payment methods available for expenses paid by company"}, {"from_model": "res.company", "to_model": "res.partner", "field": "message_partner_ids", "type": "many2many", "label": "Followers (Partners)"}, {"from_model": "res.company", "to_model": "res.country", "field": "multi_vat_foreign_country_ids", "type": "many2many", "label": "Foreign VAT countries"}, {"from_model": "res.company", "to_model": "res.company", "field": "parent_ids", "type": "many2many", "label": "Parent"}, {"from_model": "res.company", "to_model": "res.users", "field": "user_ids", "type": "many2many", "label": "Accepted Users"}], "sample_record": {"account_cash_basis_base_account_id": false, "account_default_pos_receivable_account_id": [40, "101300 Account Receivable (PoS)"], "account_discount_expense_allocation_id": false, "account_discount_income_allocation_id": false, "account_enabled_tax_country_ids": [233], "account_fiscal_country_id": [233, "United States"], "account_journal_early_pay_discount_gain_account_id": [37, "643000 Cash Discount Gain"], "account_journal_early_pay_discount_loss_account_id": [27, "443000 Cash Discount Loss"], "account_journal_payment_credit_account_id": [45, "101404 Outstanding Payments"], "account_journal_payment_debit_account_id": [44, "101403 Outstanding Receipts"], "account_journal_suspense_account_id": [43, "101402 Bank Suspense Account"], "account_opening_date": "2025-01-01", "account_opening_journal_id": false, "account_opening_move_id": false, "account_purchase_tax_id": [2, "15%"], "account_sale_tax_id": [1, "15%"], "account_storno": false, "account_use_credit_limit": false, "active": true, "alias_domain_id": false, "alias_domain_name": false, "all_child_ids": [], "anglo_saxon_accounting": true, "appbar_image": "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", "attendance_barcode_source": "front", "attendance_from_systray": true, "attendance_kiosk_delay": 10, "attendance_kiosk_key": "0d42c393-2a7c-4aac-99a1-dadadd4e9678", "attendance_kiosk_mode": "barcode_manual", "attendance_kiosk_url": "http://localhost:8069/hr_attendance/0d42c393-2a7c-4aac-99a1-dadadd4e9678", "attendance_kiosk_use_pin": false, "automatic_entry_default_journal_id": false, "bank_account_code_prefix": "1014", "bank_ids": [], "bank_journal_ids": [6], "bounce_email": "", "bounce_formatted": "", "cash_account_code_prefix": "1015", "catchall_email": "", "catchall_formatted": "", "chart_template": "generic_coa", "child_ids": [], "city": "", "color": 1, "company_details": false, "company_expense_allowed_payment_method_line_ids": [3], "company_registry": false, "country_code": "US", "country_id": [233, "United States"], "create_date": "2025-03-06 10:20:53", "create_uid": false, "currency_exchange_journal_id": [4, "Exchange Difference"], "currency_id": [1, "USD"], "default_cash_difference_expense_account_id": [36, "642000 Cash Difference Loss"], "default_cash_difference_income_account_id": [26, "442000 Cash Difference Gain"], "default_from_email": false, "display_invoice_amount_total_words": false, "display_name": "My Company", "email": "<EMAIL>", "email_formatted": "\"My Company\" <<EMAIL>>", "email_primary_color": "#000000", "email_secondary_color": "#875A7B", "employee_properties_definition": [], "expects_chart_of_accounts": true, "expense_accrual_account_id": false, "expense_currency_exchange_account_id": [35, "641000 Foreign Exchange Loss"], "expense_journal_id": [2, "Vendor Bills"], "expense_product_id": [6, "[EXP_GEN] Others"], "external_report_layout_id": false, "fiscal_position_ids": [], "fiscalyear_last_day": 31, "fiscalyear_last_month": "12", "fiscalyear_lock_date": false, "font": "<PERSON><PERSON>", "has_message": true, "hr_attendance_display_overtime": true, "hr_attendance_overtime": true, "hr_presence_control_email_amount": 0, "hr_presence_control_ip_list": false, "hr_presence_last_compute_date": "2025-03-14 01:02:24", "iap_enrich_auto_done": true, "id": 1, "income_currency_exchange_account_id": [25, "441000 Foreign Exchange Gain"], "incoterm_id": false, "internal_project_id": [1, "Internal"], "invoice_is_download": true, "invoice_is_email": true, "invoice_is_snailmail": false, "invoice_is_ubl_cii": false, "invoice_terms": false, "invoice_terms_html": false, "is_company_details_empty": true, "layout_background": "Blank", "layout_background_image": false, "leave_timesheet_task_id": [3, "Time Off"], "logo": "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", "logo_web": "iVBORw0KGgoAAAANSUhEUgAAALQAAAAwCAYAAAC47FD8AAAhwUlEQVR42tV9fXQcV5Xnve+jqluybMlYHcdExjGZeGgRJ7bshI8wrRmctT0cDMykdFhw1sGRSv7AAxmyA8wwtHvnnNmdw2RDyGJHbZnFxIRdNUtgyIcTEpBYhsQDhomybjAQ24kSC9rBlq2P6qp6H/uHqzSVjmRXtRMn0+f08TlyV/d79/3efb/3u/fdh7lcLpXJZPxSqSQhxiuXyzEAYOl0Wjc1NYm4z9m2zY8cOULT6bR+5JFHPETUMR5Dy7J4pVIhJ0+eVIcPH/bjPKe1xvXr1xuO4yAAiKGhIRGnjZZl0UqlwgEA6rXJDTfc4BcKBRWnbx0dHWzOnDk0yXNaa+zq6uKVSoUsW7ZMFotFP04b8/k8OXjwIB8bGyOmafpJbDI+Ps4cx8F/Dzghl6KRHR0d04284YYb/LiNzGazfGRkhC5btkzGBTMAYFdXFx8bGyOdnZ0i6cABAGzfvj2xTQAAmpqaREwwQz1gBgDs7e1lIyMjNJPJqL6+vlh9AwBy8OBB7jgOrl271v/hD38Y97lLCuaLwUmlUiGZTEZhzI7V64Ugn8+zRx99lDc3N6sDBw54ABB7xo2MjNCzZ8/Kcrnsx3yOWJbFRkZGqO/74tChQ35SL1SPR0+n0/rAgQMCAGIPXAjmemzS1tYmS6VSLJuEntlxHJyYmJBJbBKuVkls8nrhJBhvQV5LL5TL5aYb+cgjj8RuZDjj2traYoNZa43r1q3jlUqFLF++/JKC+YYbbvCTgJlzzsIlNa5NbNtmoU2++c1vxn1ummYEYBYxwUUuBsyvB06CvmkSZ+DCRnZ2dnpxl498Ps9c151uZNzlw7Zt1traSk6ePKnieqGQM4cDl4RXVioVfrFgTjJwc+bMoW1tbfLAgQOxllREBMuy+PDwMMtkMqpUKvlaX9iU4QQPOXMwwWN59MHBQSPcRySxyRsBJ/RCAzA5OTm9Sdq1a1dcfshPnDjBFi1apL7zne8k4kJjY2Ns0aJFamhoKC7NwHK5zCuVCl27dq3/jW98I/YAHD582KjHC4U2ufHGG/0EnJkbhsHS6bResWKFGBoaisWZLcsyRkZG6PLly8XXvvY1USgU4npmw3EcrFar8sknn4zNmdPpNBdCJObMbxSc4KtN7C81Z456oXrUjEvFmTnnLLCJDwAqiU2WL18uisWiSLJavR6c+Y2AExKHCyXZpdbLhcLNTlLOPDY2RpYvXx57APL5PAnVjKRgHhkZMerlzBGbJAJzW1ubDNSMWBM8Sr0uNWd+I+CEvJpcaM6cOTQJF9Jao2VZvLW1lZw9e1Ym4cyhNGeapp+EMw8ODtatT5ummUgvDtWMgDMnskm4uS2VSrGW4nw+T9atW8cdx8E3Omd+LXFCzhdYSMIPBwcHWSaTSUTs29vbeaipJqAZuH79emNkZISuXbvWT+KZw4GrN9jS2dnpxbVJZ2cnDaW5bDYb18NiV1cXr9GZYz0XVTPi9i1UQUKbxAXlGxknJLp8hPwwwYYALctKz5kzxwwHLklkZ+7cubStrU0ODAwk4cxG6IUKhUJsUF4MmNPptE4C5o6ODl6HFou2bbOQMyfxzJZlpaSUbNmyZXrp0qUqbt9yuZwRBk2S0Iw6cAL5fJ6FcuXAwID/WuEEa7lQgoHD4K3CZSHu8tHe3s5bW1tJKEMlCRCEnLmvr08gvmJPq2cDpeu6GNlczfr5Ws5smmZiMEc5c5KBC/lh3AHP5/OkVCqxcrnsQYJXvatVnTipyyb14gTraWQAXrjlllvaGhoa8lrrXxaLxS8EANevtppR7849OgmeeuopL+5Grk6a8bLcjLg792huRtIJXiqV2FVXXWW2trZ2UEpp8H3O2NjYwdk8Zy1nrodmJAFzPp9ng4ODrJ7cjHpwgrlcLpVUctm0aVNqyZIl3vPPP39Nc3Pzv05MTDy9aNGileVyGUulkgo68jL3WS6XsVKpcMMwaHNzsx/XC0WDJoGH9Tdv3tyUyWTY6OiobmhoQAAAKeVEdHMYDpzrumiapp/JZPSCBQvmAgCcOXMG5syZQxsbGyfvuusu59UYuNAxZDIZlc1m40bJ6vLMgSMyh4aGxMaNG+c3NDT8wjCM+QAAnuf9tqmp6ao777xzstbBRMPg9YA5KU5yuRxzXZcn7Vs2m30ZzUiCE1ZPI/ft2+cCgN66despx3EEIp6sHcBZAgAyNGzQSLzA0k/Wr18/rWacPn1aAwCYpnn12NjYo6lUigghgDFGAeCQZVk3ZbNZXS6XcXBwkAMAhDSjp6fnnxAx5/u+mDt3LtFanxofH88BwItaa0BEXW8EMODMNJ1O61KpJOJKc7Zts+Hh4WmdOS7NOHjwIGeMEQCQTU1NvpRy0vf95uAjk7OB8uDBg3UlGlUqlbp15qRgjnrmuPsIACBdXV3McRxkSWQoRMSWlpZ5PT09ewghrVJKAxEZAKy2bfsHjLEGIcRXEfE5SmleCDGptaahp2CMGVLKrxUKhd35fJ6Ev5vP50kwCdRsnDnkvpZl0d27dx+ybft/ptPpOxzHAaUUmKb5x/PmzbulUCh89aabbmoEAJlOp3WxWPR7e3v/o2EY73ddFxAROOfguu6n+/v7X7AsiyKiDDlzc3OzSgrmYLOTOHQ7PDzM2traZNygCUSy5jKZTPQZgogkGCcyC81IrGbUqzOHuRlhElXSrLl6kq8qlQouW7ZMxs62syzLOHr0qH7b297W0tDQcNg0zQVCCJBSAiICIQTS6TRMTEzcCQC/mjt3bt/k5CQQQmonBkgpN/X19d2Xy+VoZ2enqgVPlGbMkDWH+Xwejx8/bpim+RNKabuUUlBKmZTyBcdxVjz55JMTK1as0NlsVoyNjc2tVqtPE0LahBC+YRiG53kP79mz532WZdFSqSTr5cy5XI6GeQhJOXPSrLnoc2FmWbBKtgghnqGUvhkAQCl1rKmp6ZqQcuTzebzUnDkaNHmtOXMtTmjcGTc5OUmvuOIKGB0dnWptbf2WEGKX1vq7hJCNSqmfCCFuqlarX+ecf09rvQIA1nie5yulQEqplFJaSqkBgCilPrBq1SprwYIFWyYnJ7def/3161evXn3wpz/96emBgQF6xx13cMdxcM2aNWKm3IzOzk68++67/Y6OjsOEkE0AgFJKlUqlWrTW8vHHH3/MsixaKBTEtdde+3emaa73PE8SQqhSalwpteFnP/vZmGVZmMlkSFRTPXnyJFqWRTOZDAbBA3LrrbfC0NCQrt0Aaq35ggUL8IorrpBLly4Fy7Kw5nNgWRZtb28n4btUKrEQzNlsVmUyGWxvbyfhs1prDLg4tre3k3K5rAOd2Thz5gypVqvhphgBAFavXp1WSm0jhMwNBnnMNM3dTz75pG9ZFj169Gg0N0Plcjl26623krB/mUwGy+XyTOPN0+m0juSroGVZNGITMjg4qAuFAuTzebJ9+3aycOFC4+c//7nR0NAgZwHz9HdkMhlsbW2la9eupa2traxSqdC2tjYZN4cnKhREcYJJZlztUmzb9mLTNJ9zXfcxAPgspfQbAWgzADAPADQiUkQEpRQAgNRaE0RExti0x06lUjA1NfWtvr6+P7csKz0yMgIXymcOvatt2180TfMT1WpVEEKI1npKSrli7969z3Z3d1/LGDuolGJaa2WaJnNdd/uePXt2BQMDIWdevHixOl/EMfg9BQA6pBltbW2xI2QRsKQCNcObafmcwRPiunXrZlqt8AIeeiqXy5mBAxDlclmfr60zrVahR5+lXS97Lo7HP8/nqGVZRjabdeOujrPYBFhcLhQl9vl8ngGAqlQqTAjxIgCMKKWa0+n01a7rglIKKKVACAHf96taa09rPcc0Ter7PiiltO/7GgAAEaXneURrvQQAwHEcXL58+QXD2QMDA2rnzp3k1KlTn3dd932MsauklL5pmnM8z8sDwC2EkDsZY4brup5pmobned8PwVypVDAS/JCFQkHk83kyOjp6IyKuUEplCCFTiPisEOJH/f39LwAArFu3zgwNOXfu3Hm2ba8AAIWIDBFH77333v8Xgs2yLNrS0vJOADCVUkwI4U9OTj45MDDg79y50/jd7353g1LKCLzyVKFQ+PH27dvfJKX8ECK+VQhxfGRk5KuBTWo19NmJNiGko6ODhdp7GIDatm3bQq31e5RSVyml5lBKTyLi01LKHxWLRd+yLKNSqZDoBjAEcy6XY9lsdr3W+lqtNdFaHwWAJ4rF4uiWLVuWBd8pOOdVxthT99xzj1szUWWtfRGxqpR6rlqt/t/9+/cfO8+knlGKXbt27SuCayyOZ67dpYZfsmPHjhcdx/kAY8wFgNWe52mllGCMcSHEUaXU3zc2Nj5w1113neru7r7a9/3NAHA7pdQIuHcoK1GttQIAEELEymdGRG3bNisWi2c3b978ScbYgwBAPM/TWus/s237LCL+ieu6ilJKhRATALAdAKClpYVUKhUKALB48WJVKBSEbdsfrFQqf0sIWck5h2jOsdZ6rLe392vVajW/b9++8Y0bN6b279/vr1q1ajml9HEhBJimCY7jPAAAf5bL5ejQ0JBYvHhxanx8/AHG2IIAaBVCyFWIqHt7e1u11g9RSpsopeC67tPd3d23Sim/xRi7klIKQogXDMO4v6mpaapYLKq4S/Hx48e54zju0qVLZbFY9G+//fa04zg7lVKbKaULGGMQKDsghABK6TO2bf+3YrF4/zve8Y50rWfu7u5+B2PsXkrptYg4bRul1Eu2bX9WCPGHjY2Nn3JdF6SUk47jvA0ARqKb/fPZl1J6euvWrfe5rvu5QqEwPls8I6AZPHQoM0WKyfli7rMR+7ChjuO8hRDyPaXUUwCwWwihGWNcSjkspXxXX1/f3snJySW9vb0fUkpN9PX1fUZr/QFEnCKE6JpGIwBAkMkWK2hy5MgRetNNNzV+5Stfecj3/aJpmlSd4zYNjLFtgdE055wqpXYWi8Vf7tixwzxy5AgFAAgPmfb09NzBOX8AEVdKKaXrulIIAZ7nged5PgA0m6b5F6Zpfn/Tpk2trutWgwHVSikNAL5SSiPiy9oeJEF54ecQ0UulUhoAgDGmw/8TQmhEbCKE3M8Yu9J1XRBCAACcPXPmjJ+U1qRSKbQsS2SzWWHb9rypqanHDMP4K631At/3vfD7q9WqEkIIRLzGNM2v9/T0/N1TTz3lZLNZHexBdG9v77Wc8wOU0ms9z5PVatX1PE/7vg9a6wWIuAcRP+w4jtLnDO4FfYPBwUESgPlTF7BvC+f8Lzjngx/72MdaZ8JmdAO4du1afzY6Ss4Xc7/QQcWAs84jhDQCgBlwZSGl3Lx3797f2bb9vxljhxhj3zJN81c9PT0fLxaLB4QQ/51zTkKvHHhcFXfAwmUHAODUqVNePp8nDQ0Nn/Y873nGGNVaK9/3ldZaGYZBXdf959OnT3/RsizjJz/5CUmn0zoE89atW9cYhvEFIYT0fV9wzikAaCnlUQB4iXPOtdbgOI6XSqVWGIZRDINHYdMDyhC+IZPJaACAyclJFshoGPlczUKDGOwvljLG3uZ5HiDiMaXUUUQ8m8lkFCR8NTc3+4VCQRcKBaW1/h+pVOpGx3E8rbXmnBsAcEop9SwiCs45831feJ4nTdP8XG9v74cicipqrXdRSue5rusDADFN0wSACaXUcSllSC3fHOn/dB+HhoZET0/PGs75P9bYF2ayr2maKznn/cFvv0LhiZPDQ2o58+DgIGtra5OPPPJILKkGEYU+9xKcc6KUOtTf33/Itu3tDQ0NXWFHAKAREe/++Mc/fiUh5F7f931CCE06WLXRrkOHDvnlchnvvvvuMUTcHvBZQEQkhKCUchIAtpZKJTU2Nsabm5tVU1NTyEdRSpkPvK3knDMp5ROIuIpzfi1j7Brf929HRBcRmeM4gnP+/u7u7j8ODD1j+7PZLFqWRc+cORO3f4oQoqWUzyul1k1MTHScPXt2hZTyg0GmHsTUqQERdbAKaNu2V1FKNwbBL0YIUb7vf0YIcc3U1NS1SqmVQoiHOOdMa62ClSSfz+eNUqkkf/vb3/4RY+xdgULEKKXo+/4XhRDXVKvVVQDwHinlwUCaVbPELvIBgBXnnAkhfuB53vUR+36CEDJtX8bYhttuuy1XKBSUZVk0n8+TMLsyzil+Foczx0xSggBIvw7I/3vFuXVTAwAXQgjTNKnnee8sFov327Y9RghpDZbsupJqwqW4VCrJgYEB2tXV9WBPT89Dpmm+33Vd1zRN03Xde4vF4jMbN25sfOmll0SE0mjbtt8KANf7vq8ppVxK+VshRNfevXtPBZ+ZAoAvdnd3L06lUre7ruvTc68PAsD3CSEzqkSHDh0yjh49Kq677joV146EEPQ875P9/f2PdnR08GBJPRtnA4g1WVpnzpwJHdX7OOdQrVZloPAU9+zZ8w+h6rB///7DlmV9tKWl5WlCyFt831eU0rePjo6+HQB+prX+D5RS7fu+MAzD9H2/VCwWbwcAetVVV7Hf/OY3P9qyZcsGpdTThJDLQl5MAoR3d3e/lVJ6ve/7mjHGhBCVqampD+/fv78StG8CAL5k23abYRh3hPYlhHwAAAbXrFlDHnjgAeI4Ds7GmWf00LUnCOIOghDiFZtKpdT8QqGgEHGEEMICWiEQUWutUWv94oYNG5q01g3hJHg1MsQOHz6MwRL+QjCxdOAlXgQAdF0XQ9mxXC5jsBm5khBiaK0l5xy11v+8d+/eU7Ztc8uyjA0bNjRalkW11v8UcFoa9OHKkEPXeiQAgHQ6Tdrb22Vzc/MF9wNaayCEUCHES57n/YtlWTRIAZ2JorziNTExwWv/Zppm2K4/CDZ/GPz7gGVZdNOmTamRkRFjw4YNTaVS6YzW+vuMMUBEQSmliPiWYGyWhhHioK37g3TV9Ec/+lHftm1+7733VrTWjzLGMAS0ECK079Ia+/5o//79lUAlw8DOFAC+K4QARCRBn68EAHj88cdZWF8lbkIasW2bhycI4hb3CAFRs+SSoFGrd+zYMVdr/SXf9yvpdNrknLOGhgbu+/5je/bsGZo/f/6fcs4bpZQqjhYe5CHEOSKkAYDXLMFm8PdXUCjf92kU/ADgAQCcPn2ajYyMUKWUDDyOF6oCWuvp8HKwsY3+FgsGRJZKJVWtVrG2PbPQBACAM2NjY2dLpZIM9e7zjcXAwAABABqCZzZ9N8LzgVIqAACOHz/OTNPUpmm6gRPwaiYZmymMHk6whQsX+nEO62qtX2ZfRHSDzSIAgD59+nSYeiyCyRBOCgoAEKY9xM17z+fzhISVag4cOBA75BsK75zz6KYOlVLSMIzWarX698Vi8VkAeKfnebu01t+uVqt/UywW133kIx9pMQzj8yHfi9PIsNTA9u3b4ySi13pNFfBaFeG4OvAgI1JKqbVmwWS83rKsdKlUqi5YsAAXLVrkl0olSQi5kXMOACAIIRoRXwwA7QSqClFKgVLqckTU2WzWD9SVNyHivJi0yjNNcyom+yJf/vKXOQBgY2Ojr2epa4CIz50zgVaEEBBCrCmVSrKpqUktW7ZMBsEdDQDvDjamNAiMvRB8xfPB8yEgNxYKBRVozLpYLPobN27MIOLaQKmBiIIDWuvRWvvats2HhoZEkCcCM9kXAEYBABYtWiTjau+h02OZTEYlzYZyHIcERF9QSqPPEc/zFOd8e29vr4+If7t79+7tkbTT60zT/DLnPOt5nkZECufJUY6qGQAgurq6EslXYeCm9m+Bd8FTp04daWlpOcw5vybYgb+1paVl1y233HLHfffd93sAgC1btqzXWn8mCARRpRQCwMPBV50OBsEQQijG2HVbt25dUygUHt+0aVNKa/3XjDEebIpfRs9c1yW1eS5JqFcmk0EAEJRSGdChmfr+mFLq0wBAfd/XhJDttm3/vK+vbyDQ8ech4n+hlL7d933BGCNCiGOc82eCr/iB1vpTAMACHnxzb2/vnYSQL7quO2YYRrtS6i5K6UIhhCSE0GAl0ACAzz333K+XLFnyC855e8DD3yql3H3bbbf9VbhPmcm+WuvHAACmpqaSJCidy7ZLmqc6MjJCTdNUUQ4YeR4BAH3fV6ZpftLzvA/btv2U1vokAPwBIr6LMWZ4nqfC7LDZdvD1JqJfyGOHf4uEzv+BUvp1KaX2fV8ZhnErAKy1bfs3ANCktb4OEUFK6Zmmabiu+y9jY2MPBxloJ86cOXMKADKBEzOllN/u6ek5hIgLCSFXSymjS3+U7rBzClhyMAche7+Gsvxbju45KgeXXXbZ4IkTJ55IpVLvrVarHiFkDiHkf/X29v61bdtnAGAJY6wtGA8VqDz/uGvXromBgQH60EMPPaG1HjYMY7nv+76UkhmG8Zeu63ZTSl9SSl3JGEPf9yE6nuPj4yybzfLvfe97k7Zt/9fQvp7nKcMwblNKrbdt+9lZ7PtT3/cfBACskUdj7a1I0rNdZ8+elZ2dnV6w+ZhCRCMAsgrkOx1swgQhZCHn/IOmafYYhtEJAIbv+yLYpISfVYQQ1FqL6PIxk5oRC73nOK4I37PlJoeRsGKxeH+1Wr03lUpxQgjxPM9DxMsZY++hlF4X6OoqlUoZQogTSqlbS6WSZ9s2/8IXvjCutX4klUoRAPCllAIAGhljf8QYu1opBVrrsUDaFAAgAl4NUkqMthMRRRy50nXd8AygjPRZRN/pdFqHK5HWusf3/eOpVMpQ514+pXQ5Y+w9iNjmeZ5HCCHpdNqYmpq67/LLL++zLIs+9NBDfN++fZ7WulcpdcYwDK611q7ruog4lxCylBASaui1baflctnP5/OsWCze7zjOrhr7LprFvqNa61v37dtXhQuffprxxDpJ4pnDeggB1yatra0jAPBVxhhQSiljDCNvFgwchGmmlFJgjDFK6fTnArmsioi7AABGR0dpPXUzjh8/Hj4zL51OM875nHQ6zbTWjbM9UygUtGVZxp49ez7uOM5nCCGVVCplBHwOCCGQSqUYY4x4nveo1rpz7969v7Asiz7//PMkm80aWuvPV6vVQ+l02jRNk3HOgVIKwWT9jNb6N01NTTyVSjFEbPV934h41tZ0Os2Cdi6Io73XJkSlUimMfg8itgYRSrAsi/f39x+fmppa4/v+tymlxDAMHlIdxhikUikDAE5Xq9X8m9/85lvD1IDjx4+DZVlmf3//U77vrxNCHGSMkVQqZRqGAYGNHKXUZwHgmYjKoZRSIVVQ+Xye9Pf3b69Wq//5QvZ1HOdP+vv7DweRaJVEKAhtgnFzcGfIU52eQVu2bHmHlLJFa60YY6CUwiDDDQFAcs7P52GJUupYsVj8Zb21iC3Loo7jmOPj497VV1/9btM0r3ZdVwa691PFYvGZ2qQXRISVK1dyzjkzTVMPDQ1Vbdu+HBH/FABWaa0vA4AqIv6SUvr9Xbt2/Sj8rZaWFnLkyBG6fPlyfc8997jbtm2bo7X+iNb6nVLKuYSQY0qpb+3Zs+fHPT09Gzjnl0kpiZTSrVQq/2fFihWTzz77bLqxsfHPAcAM8jbGisXiN2u90oWo144dO0zHcW6mlDYEaBpftGjRN8vlsq5UKrypqYk8+OCDU+E4aa3fq7XOAkAaAF5CxEOEkId37949EmRQvqw+c2dnJw2dytatW9dorVdord+klPqd1vqx/v7+wz09Pc9SSpcGDuxFz/Pevm/fvrEIRjDQ/Wvt6wLAr6SUP9i7d+9gnOSkcAxmwwleyDOHZ7viJqLX+4qejrgU593CIjDhGcDgPOT5nsV8Po/lcplFDrR65zvtPtPg5HI5FtCFxEVgElAvmsvlpo+S7dy5U19IVQpyp0nNIV8AAN3T07PacZzy/v37X3G8q7e3992IOKiUQsYY8X3/Z8VicXUoXcdNM9Va486dO/FCYL6QTfB8nDlu5fwwt7ilpYWcOHGCj4+PkyVLlohgl3rBV5jKmZRmzHRi3bIsms1mo/16xYmY6LH6aI53YFRaLpd1eDYxm81iuVzWAwMD6jwnTTCXy9FMJjP9XMjTbdvmx44dM9LpNDFNU5RKpWr4XBBggFCbjfY7esj3QnnX0e85deoUHR4expkmeGibaP8qlQp2dnaq0dFROjw8PG2TMEuuu7v7SkrpYaXUrxDxbyilP969e/fpbdu2zZFSrgKA3ZTSP/R930ulUobruncVi8W/nAnAQZ9IZ2cnhAU2Q5zEmaxxyi/g+WhG0iNC0WyoJEVgLqY+c1LvFV1SkxaBGR4eZkkKJ9ZbbLze09n1rnLRgothMckgfYFqrb/LOV8bSt1SymMAcAoAmgDg6oAuiUCDVISQ5bt37/7FbNThQvnMF4sTnMkz13O2K6wtUW/Fy4sdgLg0I1IF9DU9AxgFZZKJWi/NqLcIzExVQANaJGzb/khjY+PXHccBKaVARE0I4WHENFB1gJ17geu6nygWi1+aDcyXAiekljOHp24PHz6cyDNXKpVEMfdoxcuknDkcuGBpTFRsPLKkxpIre3t7pyvnJyhZhuVymYWeeXBwUL4RwRw9kRStAhpwfASAx6ampj4HAM8Fm2fOGANCCFBKIZVKMc45A4BfV6vV/3QhMF8KnODFeubznM6ONePgtS9FhR0dHSxpfeZXg3olsUl0KU5yVrHeO01s2+YhZ75QQtrmzZubGGPvBoB2AFiitZ6LiA4h5AUA+NeJiYkfBBvGGeW2S4kTvAg1Y/qgYhIu9GrUVUviveqtNRetm1GPTZIMHADQdevWsUvJmSPlF843wdGyLHKRh2AvKU4wPBh58uRJdSk5M1z626Zie+be3t7EYL6YDeClnODRikZJ+lYul03XdZlpmgKCrMRKpYKZTEaH2YFvBJyQUFNNSjPCEwT1cqF61IwklfNzuRyruW0q1lLc1dXFQzAnuW0q5MwAIJJUzg+jXUlL2kIdxcajee9J9gOlUolVKhW9cOHCaqlUqgYprnJoaCiUBfUbBScYnI5IdH/HpbrT5FJyZkSEm2++2Yjc0JrYJvVw5n8vN7QmXcFfD5yk02lN4oIZgsr5IRd6re80qV1Sk97QGlEzYk2Cm2++2QirxCeoNTddvScpZ76UYO7o6Ji+Fi7udXIQyeGp54aF1wMnTU1NItYhTq319NVpvu+LJFenHT16tK57AMNSVE888YQol8syjoe1bZuPj49Pc+Y4V6eFfatHZ85kMrxSqdBIea5YAwcARnDSRDz88MOxuW+YgDV//vxEVUCPHTvGkyo15XKZu65Lz549m6g81+uBk0Bq9MvlsiRxl49KpXLeegizLR9JrzyolzOvXLmSDw8P18WZk97QGql4SSKFE19Tzvzoo4/yS8GZw8r54aVFSWnG64GTIJ05XrbdxXKhehONkoSl67kIProDD2t0JNWLL7VNLgVntm2bHTlyhMbJ4Xkj4uR8lONlN7Tu27cvscR24403yl27diW+jXT+/PkiyW2kYdbcihUrYlfOD2nGokWLVHBDa5yfI+l0mjuOg+3t7fKBBx6IfUPr4sWLWb03tNZTlP73v/89y2QyiW9odRyHZjIZNTg4GPu5NxJO/j+BYL4pGGMslQAAAABJRU5ErkJggg==", "max_tax_lock_date": "1-01-01", "message_attachment_count": 0, "message_follower_ids": [], "message_has_error": false, "message_has_error_counter": 0, "message_has_sms_error": false, "message_ids": [1336], "message_is_follower": false, "message_needaction": false, "message_needaction_counter": 0, "message_partner_ids": [], "mobile": "False", "multi_vat_foreign_country_ids": [], "name": "My Company", "nomenclature_id": [1, "Default Nomenclature"], "overtime_company_threshold": 15, "overtime_employee_threshold": 15, "overtime_start_date": "2025-03-01", "paperformat_id": [1, "A4"], "parent_id": false, "parent_ids": [1], "parent_path": "1/", "partner_gid": 0, "partner_id": [1, "My Company"], "payment_onboarding_payment_method": false, "period_lock_date": false, "phone": "", "primary_color": false, "project_time_mode_id": [4, "Hours"], "qr_code": false, "quick_edit_mode": false, "rating_ids": [], "report_footer": false, "report_header": false, "resource_calendar_id": [1, "Standard 40 hours/week"], "resource_calendar_ids": [1], "revenue_accrual_account_id": false, "root_id": [1, "My Company"], "secondary_color": false, "sequence": 0, "snailmail_color": true, "snailmail_cover": false, "snailmail_duplex": false, "social_facebook": false, "social_github": false, "social_instagram": false, "social_linkedin": false, "social_tiktok": false, "social_twitter": false, "social_youtube": false, "state_id": false, "street": "45", "street2": false, "tax_calculation_rounding_method": "round_per_line", "tax_cash_basis_journal_id": [5, "Cash Basis Taxes"], "tax_exigibility": false, "tax_lock_date": false, "terms_type": "plain", "timesheet_encode_uom_id": [4, "Hours"], "transfer_account_code_prefix": "1017", "transfer_account_id": [46, "101701 Liquidity Transfer"], "user_ids": [2, 6], "uses_default_logo": true, "vat": false, "website": false, "website_id": [1, "My Website"], "website_message_ids": [], "write_date": "2025-03-14 01:02:24", "write_uid": [1, "OdooBot"], "zip": "", "account_default_pos_receivable_account_id_expanded": {"account_type": "asset_receivable", "code": "101300", "company_id": [1, "My Company"], "display_name": "101300 Account Receivable (PoS)", "id": 40, "name": "Account Receivable (PoS)"}, "account_enabled_tax_country_ids_expanded": [{"address_format": "%(street)s\n%(street2)s\n%(city)s %(state_code)s %(zip)s\n%(country_name)s", "address_view_id": false, "code": "US", "country_group_ids": [], "create_date": "2025-03-06 10:20:54", "create_uid": [1, "OdooBot"], "currency_id": [1, "USD"], "display_name": "United States", "id": 233, "image_url": "/base/static/img/country_flags/us.png", "is_stripe_supported_country": true, "name": "United States", "name_position": "before", "phone_code": 1, "state_ids": [68, 69, 10, 9, 70, 12, 60, 11, 13, 14, 15, 17, 16, 18, 61, 19, 62, 20, 24, 21, 22, 23, 25, 26, 27, 42, 41, 28, 63, 43, 44, 46, 64, 45, 29, 36, 37, 30, 32, 33, 34, 31, 35, 38, 39, 40, 47, 66, 65, 48, 49, 50, 51, 52, 53, 55, 67, 54, 56, 58, 57, 59], "state_required": true, "vat_label": "", "write_date": "2025-03-06 10:20:54", "write_uid": [1, "OdooBot"], "zip_required": true}], "account_fiscal_country_id_expanded": {"code": "US", "display_name": "United States", "id": 233, "name": "United States"}, "account_journal_early_pay_discount_gain_account_id_expanded": {"account_type": "income", "code": "643000", "company_id": [1, "My Company"], "display_name": "643000 Cash Discount Gain", "id": 37, "name": "Cash Discount Gain"}, "account_journal_early_pay_discount_loss_account_id_expanded": {"account_type": "expense", "code": "443000", "company_id": [1, "My Company"], "display_name": "443000 Cash Discount Loss", "id": 27, "name": "Cash Discount Loss"}, "account_journal_payment_credit_account_id_expanded": {"account_type": "asset_current", "code": "101404", "company_id": [1, "My Company"], "display_name": "101404 Outstanding Payments", "id": 45, "name": "Outstanding Payments"}, "account_journal_payment_debit_account_id_expanded": {"account_type": "asset_current", "code": "101403", "company_id": [1, "My Company"], "display_name": "101403 Outstanding Receipts", "id": 44, "name": "Outstanding Receipts"}, "account_journal_suspense_account_id_expanded": {"account_type": "asset_current", "code": "101402", "company_id": [1, "My Company"], "display_name": "101402 Bank Suspense Account", "id": 43, "name": "Bank Suspense Account"}, "account_purchase_tax_id_expanded": {"amount": 15.0, "amount_type": "percent", "company_id": [1, "My Company"], "country_id": [233, "United States"], "display_name": "15%", "id": 2, "name": "15%", "sequence": 1, "tax_group_id": [1, "Tax 15%"], "type_tax_use": "purchase"}, "account_sale_tax_id_expanded": {"amount": 15.0, "amount_type": "percent", "company_id": [1, "My Company"], "country_id": [233, "United States"], "display_name": "15%", "id": 1, "name": "15%", "sequence": 1, "tax_group_id": [1, "Tax 15%"], "type_tax_use": "sale"}, "bank_journal_ids_expanded": [{"access_token": false, "access_url": "#", "access_warning": "", "account_control_ids": [], "accounting_date": "2025-03-14", "active": true, "activity_calendar_event_id": false, "activity_date_deadline": false, "activity_exception_decoration": false, "activity_exception_icon": false, "activity_ids": [], "activity_state": false, "activity_summary": false, "activity_type_icon": false, "activity_type_id": false, "activity_user_id": false, "alias_defaults": false, "alias_domain": false, "alias_domain_id": false, "alias_email": false, "alias_id": false, "alias_name": false, "available_payment_method_ids": [1, 2], "bank_acc_number": false, "bank_account_id": false, "bank_id": false, "bank_statements_source": "undefined", "code": "BNK1", "color": 0, "company_id": [1, "My Company"], "company_partner_id": [1, "My Company"], "country_code": "US", "create_date": "2025-03-06 10:33:49", "create_uid": [1, "OdooBot"], "currency_id": false, "current_statement_balance": 0.0, "default_account_id": [41, "101401 Bank"], "default_account_type": "asset_cash", "display_name": "Bank", "entries_count": 0, "has_message": false, "has_sequence_holes": false, "has_statement_lines": false, "id": 6, "inbound_payment_method_line_ids": [1], "invoice_reference_model": "odoo", "invoice_reference_type": "invoice", "journal_group_ids": [], "json_activity_data": "{\"activities\": []}", "kanban_dashboard": "{\"currency_id\": 1, \"show_company\": false, \"number_to_check\": 0, \"to_check_balance\": \"$\\u00a00.00\", \"number_to_reconcile\": 0, \"account_balance\": \"$\\u00a00.00\", \"has_at_least_one_statement\": false, \"nb_lines_bank_account_balance\": false, \"outstanding_pay_account_balance\": \"$\\u00a00.00\", \"nb_lines_outstanding_pay_account_balance\": 0, \"last_balance\": \"$\\u00a00.00\", \"last_statement_id\": false, \"bank_statements_source\": \"undefined\", \"is_sample_data\": false, \"nb_misc_operations\": 0, \"misc_class\": \"\", \"misc_operations_balance\": \"$\\u00a00.00\"}", "kanban_dashboard_graph": "[{\"values\": [{\"x\": \"12 Feb\", \"y\": -1.0, \"name\": \"12 February 2025\"}, {\"x\": \"17 Feb\", \"y\": 7.0, \"name\": \"17 February 2025\"}, {\"x\": \"22 Feb\", \"y\": 6.0, \"name\": \"22 February 2025\"}, {\"x\": \"27 Feb\", \"y\": 6.0, \"name\": \"27 February 2025\"}, {\"x\": \"4 Mar\", \"y\": 8.0, \"name\": \"4 March 2025\"}, {\"x\": \"9 Mar\", \"y\": 6.0, \"name\": \"9 March 2025\"}], \"title\": \"\", \"key\": \"Sample data\", \"area\": true, \"color\": \"#7c7bad\", \"is_sample_data\": true}]", "last_statement_id": false, "loss_account_id": [36, "642000 Cash Difference Loss"], "message_attachment_count": 0, "message_follower_ids": [], "message_has_error": false, "message_has_error_counter": 0, "message_has_sms_error": false, "message_ids": [], "message_is_follower": false, "message_needaction": false, "message_needaction_counter": 0, "message_partner_ids": [], "my_activity_date_deadline": false, "name": "Bank", "outbound_payment_method_line_ids": [2], "payment_sequence": true, "profit_account_id": [26, "442000 Cash Difference Gain"], "rating_ids": [], "refund_sequence": false, "restrict_mode_hash_table": false, "sale_activity_note": false, "sale_activity_type_id": false, "sale_activity_user_id": false, "secure_sequence_id": false, "selected_payment_method_codes": ",manual,manual,", "sequence": 10, "sequence_override_regex": false, "show_on_dashboard": true, "suspense_account_id": [43, "101402 Bank Suspense Account"], "type": "bank", "website_message_ids": [], "write_date": "2025-03-06 10:33:49", "write_uid": [1, "OdooBot"]}], "company_expense_allowed_payment_method_line_ids_expanded": [{"available_payment_method_ids": [1, 2], "code": "manual", "company_id": [1, "My Company"], "create_date": "2025-03-06 10:33:49", "create_uid": [1, "OdooBot"], "display_name": "Manual", "id": 3, "journal_id": [7, "Cash"], "name": "Manual", "payment_account_id": false, "payment_method_id": [2, "Manual"], "payment_provider_id": false, "payment_provider_state": false, "payment_type": "outbound", "sequence": 10, "write_date": "2025-03-06 10:33:49", "write_uid": [1, "OdooBot"]}], "country_id_expanded": {"code": "US", "display_name": "United States", "id": 233, "name": "United States"}, "currency_exchange_journal_id_expanded": {"code": "EXCH", "company_id": [1, "My Company"], "display_name": "Exchange Difference", "id": 4, "invoice_reference_model": "odoo", "invoice_reference_type": "invoice", "name": "Exchange Difference", "type": "general"}, "currency_id_expanded": {"display_name": "USD", "id": 1, "name": "USD", "symbol": "$"}, "default_cash_difference_expense_account_id_expanded": {"account_type": "expense", "code": "642000", "company_id": [1, "My Company"], "display_name": "642000 Cash Difference Loss", "id": 36, "name": "Cash Difference Loss"}, "default_cash_difference_income_account_id_expanded": {"account_type": "income", "code": "442000", "company_id": [1, "My Company"], "display_name": "442000 Cash Difference Gain", "id": 26, "name": "Cash Difference Gain"}, "expense_currency_exchange_account_id_expanded": {"account_type": "expense", "code": "641000", "company_id": [1, "My Company"], "display_name": "641000 Foreign Exchange Loss", "id": 35, "name": "Foreign Exchange Loss"}, "expense_journal_id_expanded": {"code": "BILL", "company_id": [1, "My Company"], "display_name": "Vendor Bills", "id": 2, "invoice_reference_model": "odoo", "invoice_reference_type": "invoice", "name": "Vendor Bills", "type": "purchase"}, "expense_product_id_expanded": {"categ_id": [3, "All / Expenses"], "detailed_type": "service", "display_name": "[EXP_GEN] Others", "id": 6, "name": "Others", "product_tmpl_id": [6, "[EXP_GEN] Others"], "product_variant_ids": [6], "uom_id": [1, "Units"], "uom_po_id": [1, "Units"]}, "income_currency_exchange_account_id_expanded": {"account_type": "income", "code": "441000", "company_id": [1, "My Company"], "display_name": "441000 Foreign Exchange Gain", "id": 25, "name": "Foreign Exchange Gain"}, "internal_project_id_expanded": {"alias_contact": "everyone", "alias_defaults": "{'project_id': 1}", "alias_id": [1, "Inactive Alias"], "alias_model_id": [399, "Task"], "display_name": "Internal", "id": 1, "last_update_status": "to_define", "name": "Internal", "privacy_visibility": "portal", "rating_status": "stage", "rating_status_period": "monthly"}, "leave_timesheet_task_id_expanded": {"display_name": "Time Off", "id": 3, "name": "Time Off", "state": "01_in_progress"}, "message_ids_expanded": [{"attachment_ids": [], "author_avatar": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDI5OCwgNjMlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5VPC90ZXh0Pjwvc3ZnPg==", "author_guest_id": false, "author_id": [3, "Updated Partner via API Test **********"], "body": "", "child_ids": [], "create_date": "2025-03-13 23:58:28", "create_uid": [2, "Updated Partner via API Test **********"], "date": "2025-03-13 23:58:28", "description": "", "display_name": false, "email_add_signature": false, "email_from": "\"Updated Partner via API Test **********\" <<EMAIL>>", "email_layout_xmlid": false, "has_error": false, "has_sms_error": false, "id": 1336, "is_current_user_or_guest_author": true, "is_internal": true, "letter_ids": [], "link_preview_ids": [], "mail_activity_type_id": false, "mail_ids": [], "mail_server_id": false, "message_id": "<895058085430815.1741910308.971532583236694-openerp-message-notify@4b9448334aa5>", "message_type": "notification", "model": "res.company", "needaction": false, "notification_ids": [], "notified_partner_ids": [], "parent_id": false, "partner_ids": [], "pinned_at": false, "preview": "", "rating_ids": [], "rating_value": 0.0, "reaction_ids": [], "record_alias_domain_id": false, "record_company_id": false, "record_name": false, "reply_to": "\"Updated Partner via API Test **********\" <<EMAIL>>", "reply_to_force_new": false, "res_id": 1, "snailmail_error": false, "starred": false, "starred_partner_ids": [], "subject": false, "subtype_id": [2, "Note"], "tracking_value_ids": [156], "write_date": "2025-03-13 23:58:28", "write_uid": [2, "Updated Partner via API Test **********"]}], "nomenclature_id_expanded": {"display_name": "Default Nomenclature", "id": 1, "name": "Default Nomenclature", "upc_ean_conv": "always"}, "paperformat_id_expanded": {"display_name": "A4", "dpi": 90, "id": 1, "name": "A4"}, "parent_ids_expanded": [{"account_cash_basis_base_account_id": false, "account_default_pos_receivable_account_id": [40, "101300 Account Receivable (PoS)"], "account_discount_expense_allocation_id": false, "account_discount_income_allocation_id": false, "account_enabled_tax_country_ids": [233], "account_fiscal_country_id": [233, "United States"], "account_journal_early_pay_discount_gain_account_id": [37, "643000 Cash Discount Gain"], "account_journal_early_pay_discount_loss_account_id": [27, "443000 Cash Discount Loss"], "account_journal_payment_credit_account_id": [45, "101404 Outstanding Payments"], "account_journal_payment_debit_account_id": [44, "101403 Outstanding Receipts"], "account_journal_suspense_account_id": [43, "101402 Bank Suspense Account"], "account_opening_date": "2025-01-01", "account_opening_journal_id": false, "account_opening_move_id": false, "account_purchase_tax_id": [2, "15%"], "account_sale_tax_id": [1, "15%"], "account_storno": false, "account_use_credit_limit": false, "active": true, "alias_domain_id": false, "alias_domain_name": false, "all_child_ids": [], "anglo_saxon_accounting": true, "appbar_image": "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", "attendance_barcode_source": "front", "attendance_from_systray": true, "attendance_kiosk_delay": 10, "attendance_kiosk_key": "0d42c393-2a7c-4aac-99a1-dadadd4e9678", "attendance_kiosk_mode": "barcode_manual", "attendance_kiosk_url": "http://localhost:8069/hr_attendance/0d42c393-2a7c-4aac-99a1-dadadd4e9678", "attendance_kiosk_use_pin": false, "automatic_entry_default_journal_id": false, "bank_account_code_prefix": "1014", "bank_ids": [], "bank_journal_ids": [6], "bounce_email": "", "bounce_formatted": "", "cash_account_code_prefix": "1015", "catchall_email": "", "catchall_formatted": "", "chart_template": "generic_coa", "child_ids": [], "city": "", "color": 1, "company_details": false, "company_expense_allowed_payment_method_line_ids": [3], "company_registry": false, "country_code": "US", "country_id": [233, "United States"], "create_date": "2025-03-06 10:20:53", "create_uid": false, "currency_exchange_journal_id": [4, "Exchange Difference"], "currency_id": [1, "USD"], "default_cash_difference_expense_account_id": [36, "642000 Cash Difference Loss"], "default_cash_difference_income_account_id": [26, "442000 Cash Difference Gain"], "default_from_email": false, "display_invoice_amount_total_words": false, "display_name": "My Company", "email": "<EMAIL>", "email_formatted": "\"My Company\" <<EMAIL>>", "email_primary_color": "#000000", "email_secondary_color": "#875A7B", "employee_properties_definition": [], "expects_chart_of_accounts": true, "expense_accrual_account_id": false, "expense_currency_exchange_account_id": [35, "641000 Foreign Exchange Loss"], "expense_journal_id": [2, "Vendor Bills"], "expense_product_id": [6, "[EXP_GEN] Others"], "external_report_layout_id": false, "fiscal_position_ids": [], "fiscalyear_last_day": 31, "fiscalyear_last_month": "12", "fiscalyear_lock_date": false, "font": "<PERSON><PERSON>", "has_message": true, "hr_attendance_display_overtime": true, "hr_attendance_overtime": true, "hr_presence_control_email_amount": 0, "hr_presence_control_ip_list": false, "hr_presence_last_compute_date": "2025-03-14 01:02:24", "iap_enrich_auto_done": true, "id": 1, "income_currency_exchange_account_id": [25, "441000 Foreign Exchange Gain"], "incoterm_id": false, "internal_project_id": [1, "Internal"], "invoice_is_download": true, "invoice_is_email": true, "invoice_is_snailmail": false, "invoice_is_ubl_cii": false, "invoice_terms": false, "invoice_terms_html": false, "is_company_details_empty": true, "layout_background": "Blank", "layout_background_image": false, "leave_timesheet_task_id": [3, "Time Off"], "logo": "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", "logo_web": "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", "max_tax_lock_date": "1-01-01", "message_attachment_count": 0, "message_follower_ids": [], "message_has_error": false, "message_has_error_counter": 0, "message_has_sms_error": false, "message_ids": [1336], "message_is_follower": false, "message_needaction": false, "message_needaction_counter": 0, "message_partner_ids": [], "mobile": "False", "multi_vat_foreign_country_ids": [], "name": "My Company", "nomenclature_id": [1, "Default Nomenclature"], "overtime_company_threshold": 15, "overtime_employee_threshold": 15, "overtime_start_date": "2025-03-01", "paperformat_id": [1, "A4"], "parent_id": false, "parent_ids": [1], "parent_path": "1/", "partner_gid": 0, "partner_id": [1, "My Company"], "payment_onboarding_payment_method": false, "period_lock_date": false, "phone": "", "primary_color": false, "project_time_mode_id": [4, "Hours"], "qr_code": false, "quick_edit_mode": false, "rating_ids": [], "report_footer": false, "report_header": false, "resource_calendar_id": [1, "Standard 40 hours/week"], "resource_calendar_ids": [1], "revenue_accrual_account_id": false, "root_id": [1, "My Company"], "secondary_color": false, "sequence": 0, "snailmail_color": true, "snailmail_cover": false, "snailmail_duplex": false, "social_facebook": false, "social_github": false, "social_instagram": false, "social_linkedin": false, "social_tiktok": false, "social_twitter": false, "social_youtube": false, "state_id": false, "street": "45", "street2": false, "tax_calculation_rounding_method": "round_per_line", "tax_cash_basis_journal_id": [5, "Cash Basis Taxes"], "tax_exigibility": false, "tax_lock_date": false, "terms_type": "plain", "timesheet_encode_uom_id": [4, "Hours"], "transfer_account_code_prefix": "1017", "transfer_account_id": [46, "101701 Liquidity Transfer"], "user_ids": [2, 6], "uses_default_logo": true, "vat": false, "website": false, "website_id": [1, "My Website"], "website_message_ids": [], "write_date": "2025-03-14 01:02:24", "write_uid": [1, "OdooBot"], "zip": ""}], "partner_id_expanded": {"display_name": "My Company", "id": 1, "name": "My Company", "property_account_payable_id": [14, "211000 Account Payable"], "property_account_receivable_id": [6, "121000 Account Receivable"]}, "project_time_mode_id_expanded": {"category_id": [3, "Working Time"], "display_name": "Hours", "factor": 8.0, "factor_inv": 0.125, "id": 4, "name": "Hours", "rounding": 0.01, "uom_type": "smaller"}, "resource_calendar_id_expanded": {"display_name": "Standard 40 hours/week", "id": 1, "name": "Standard 40 hours/week", "tz": "Asia/Bangkok"}, "resource_calendar_ids_expanded": [{"active": true, "associated_leaves_count": 0, "attendance_ids": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "company_id": [1, "My Company"], "create_date": "2025-03-06 10:21:21", "create_uid": [1, "OdooBot"], "display_name": "Standard 40 hours/week", "global_leave_ids": [], "hours_per_day": 8.0, "id": 1, "leave_ids": [], "name": "Standard 40 hours/week", "two_weeks_calendar": false, "two_weeks_explanation": "The current week (from 2025-03-10 to 2025-03-16) correspond to the  second one.", "tz": "Asia/Bangkok", "tz_offset": "+0700", "write_date": "2025-03-06 10:21:21", "write_uid": [1, "OdooBot"]}], "root_id_expanded": {"account_opening_date": "2025-01-01", "currency_id": [1, "USD"], "display_name": "My Company", "fiscalyear_last_day": 31, "fiscalyear_last_month": "12", "id": 1, "layout_background": "Blank", "name": "My Company", "partner_id": [1, "My Company"]}, "tax_cash_basis_journal_id_expanded": {"code": "CABA", "company_id": [1, "My Company"], "display_name": "Cash Basis Taxes", "id": 5, "invoice_reference_model": "odoo", "invoice_reference_type": "invoice", "name": "Cash Basis Taxes", "type": "general"}, "timesheet_encode_uom_id_expanded": {"category_id": [3, "Working Time"], "display_name": "Hours", "factor": 8.0, "factor_inv": 0.125, "id": 4, "name": "Hours", "rounding": 0.01, "uom_type": "smaller"}, "transfer_account_id_expanded": {"account_type": "asset_current", "code": "101701", "company_id": [1, "My Company"], "display_name": "101701 Liquidity Transfer", "id": 46, "name": "Liquidity Transfer"}, "user_ids_expanded": [{"accesses_count": 602, "action_id": false, "active": true, "active_lang_count": 1, "active_partner": true, "activity_calendar_event_id": false, "activity_date_deadline": false, "activity_exception_decoration": false, "activity_exception_icon": false, "activity_ids": [], "activity_state": false, "activity_summary": false, "activity_type_icon": false, "activity_type_id": false, "activity_user_id": false, "additional_info": false, "additional_note": false, "address_id": [1, "My Company"], "allocation_count": 0.0, "allocation_display": "0", "allocation_remaining_display": "0", "api_key_ids": [], "attendance_manager_id": false, "attendance_state": "checked_in", "avatar_1024": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDI5OCwgNjMlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5VPC90ZXh0Pjwvc3ZnPg==", "avatar_128": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDI5OCwgNjMlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5VPC90ZXh0Pjwvc3ZnPg==", "avatar_1920": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDI5OCwgNjMlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5VPC90ZXh0Pjwvc3ZnPg==", "avatar_256": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDI5OCwgNjMlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5VPC90ZXh0Pjwvc3ZnPg==", "avatar_512": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDI5OCwgNjMlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5VPC90ZXh0Pjwvc3ZnPg==", "bank_account_count": 0, "bank_ids": [], "barcode": false, "birthday": false, "calendar_last_notif_ack": "2025-03-06 11:01:40", "can_edit": true, "can_publish": true, "category_id": [1], "category_ids": [], "certificate": "bachelor", "channel_ids": [1, 2, 4, 5], "child_ids": [], "children": 0, "city": "K<PERSON><PERSON> Tan", "coach_id": [5, "mark"], "color": 0, "comment": "<p>abc123</p>", "commercial_company_name": false, "commercial_partner_id": [3, "Updated Partner via API Test **********"], "companies_count": 1, "company_id": [1, "My Company"], "company_ids": [1], "company_name": false, "company_registry": false, "company_type": "person", "complete_name": "Updated Partner via API Test **********", "contact_address": "55 Soi Sukhumvit 26\n\nKhlong Tan\nBangkok 10110\nThailand", "contact_address_inline": "55 <PERSON><PERSON> 26, <PERSON><PERSON><PERSON>, Bangkok 10110, Thailand", "contract_ids": [], "country_code": "TH", "country_id": [217, "Thailand"], "country_of_birth": false, "create_date": "2025-03-06 10:20:54", "create_employee": false, "create_employee_id": false, "create_uid": [1, "OdooBot"], "credit": 0.0, "credit_limit": 0.0, "credit_to_invoice": 0.0, "currency_id": [1, "USD"], "current_leave_state": false, "customer_rank": 0, "date": false, "days_sales_outstanding": 0.0, "debit": 0.0, "debit_limit": 0.0, "department_id": [1, "Administration"], "display_extra_hours": true, "display_name": "Updated Partner via API Test **********", "duplicated_bank_account_partners_count": 0, "email": "<EMAIL>", "email_formatted": "\"Updated Partner via API Test **********\" <<EMAIL>>", "email_normalized": "<EMAIL>", "emergency_contact": "", "emergency_phone": "", "employee": false, "employee_bank_account_id": false, "employee_cars_count": 1, "employee_count": 1, "employee_country_id": false, "employee_id": [1, "Administrator"], "employee_ids": [1], "employee_parent_id": [5, "mark"], "employee_resource_calendar_id": [1, "Standard 40 hours/week"], "employee_skill_ids": [], "employee_type": "employee", "employees_count": 1, "expense_manager_id": [6, "mark"], "fiscal_country_codes": "US", "function": "CEO <PERSON><PERSON>", "gender": "male", "groups_count": 41, "groups_id": [2, 8, 25, 34, 17, 61, 52, 28, 19, 31, 51, 42, 44, 3, 9, 125, 124, 56, 1, 101, 100, 13, 16, 60, 30, 33, 29, 55, 4, 50, 7, 59, 24, 22, 21, 23, 18, 32, 27, 26, 49], "has_message": true, "has_unreconciled_entries": false, "hours_last_month": 90.94, "hours_last_month_display": "90.94", "hr_icon_display": "presence_present", "hr_presence_state": "present", "id": 2, "identification_id": "", "im_status": "offline", "image_1024": false, "image_128": false, "image_1920": false, "image_256": false, "image_512": false, "industry_id": false, "invoice_ids": [], "invoice_warn": "no-message", "invoice_warn_msg": false, "is_absent": false, "is_blacklisted": false, "is_company": false, "is_public": false, "is_published": false, "is_system": true, "job_title": "Field Manager", "journal_item_count": 0, "km_home_work": 0, "lang": "en_US", "last_activity": false, "last_activity_time": false, "last_check_in": "2025-03-11 06:03:53", "last_check_out": false, "last_time_entries_checked": false, "leave_date_to": false, "leave_manager_id": [6, "mark"], "log_ids": [22], "login": "mark", "login_date": "2025-03-11 05:17:31", "marital": "single", "meeting_count": 4, "meeting_ids": [6, 12, 7, 1], "message_attachment_count": 0, "message_bounce": 0, "message_follower_ids": [], "message_has_error": false, "message_has_error_counter": 0, "message_has_sms_error": false, "message_ids": [1324], "message_is_follower": false, "message_needaction": false, "message_needaction_counter": 0, "message_partner_ids": [], "mobile": "0402851235", "mobile_blacklisted": false, "mobile_phone": "0402851235", "my_activity_date_deadline": false, "name": "Updated Partner via API Test **********", "new_password": "", "notification_type": "email", "oauth1_session_ids": [], "oauth2_session_ids": [], "odoobot_failed": false, "odoobot_state": "onboarding_emoji", "parent_id": false, "parent_name": false, "partner_gid": 0, "partner_id": [3, "Updated Partner via API Test **********"], "partner_latitude": 0.0, "partner_longitude": 0.0, "partner_share": false, "passport_id": "", "password": "", "payment_token_count": 0, "payment_token_ids": [], "peppol_eas": false, "peppol_endpoint": false, "permit_no": false, "phone": "******-272-6209", "phone_blacklisted": false, "phone_mobile_search": false, "phone_sanitized": "+***********", "phone_sanitized_blacklisted": false, "pin": "111", "pinned_apps": [], "place_of_birth": "", "plan_to_change_bike": false, "plan_to_change_car": false, "private_city": "K<PERSON><PERSON> Tan", "private_country_id": [217, "Thailand"], "private_email": "", "private_lang": false, "private_phone": "", "private_state_id": false, "private_street": "55 Soi Sukhumvit 26", "private_street2": "", "private_zip": "", "project_ids": [], "property_account_payable_id": [14, "211000 Account Payable"], "property_account_position_id": false, "property_account_receivable_id": [6, "121000 Account Receivable"], "property_payment_term_id": false, "property_product_pricelist": false, "property_supplier_payment_term_id": false, "rating_ids": [], "ref": false, "ref_company_ids": [], "request_overtime": true, "res_users_settings_id": [1, "Updated Partner via API Test **********"], "res_users_settings_ids": [1], "resource_calendar_id": [1, "Standard 40 hours/week"], "resource_ids": [1], "resume_line_ids": [], "rules_count": 143, "same_company_registry_partner_id": false, "same_vat_partner_id": false, "self": [3, "Updated Partner via API Test **********"], "share": false, "show_credit_limit": false, "show_leaves": true, "sidebar_type": "large", "signature": "<p style=\"margin-bottom: 0px;\"><span data-o-mail-quote=\"1\">-- <br data-o-mail-quote=\"1\">\nAdministrator</span></p>", "signup_expiration": false, "signup_token": false, "signup_type": false, "signup_url": "http://localhost:8069/web/login?db=loneworker&login=mark", "signup_valid": false, "spouse_birthdate": false, "spouse_complete_name": false, "ssnid": false, "starred_message_ids": [], "state": "active", "state_id": [1498, "Bangkok (TH)"], "street": "55 Soi Sukhumvit 26", "street2": "", "study_field": "", "study_school": "", "supplier_rank": 0, "task_count": 0, "task_ids": [], "title": [3, "Mister"], "total_invoiced": 0.0, "total_overtime": 6.74, "totp_enabled": false, "totp_trusted_device_ids": [], "trust": "normal", "type": "contact", "tz": "Asia/Bangkok", "tz_offset": "+0700", "ubl_cii_format": false, "use_partner_credit_limit": false, "user_group_warning": false, "user_id": false, "user_ids": [2], "vat": "", "visa_expire": false, "visa_no": false, "visitor_ids": [1], "website": "http://markshaw.com", "website_id": false, "website_message_ids": [], "website_published": false, "website_url": "#", "work_contact_id": [3, "Updated Partner via API Test **********"], "work_email": "<EMAIL>", "work_location_id": false, "work_phone": "", "write_date": "2025-03-06 11:01:41", "write_uid": [1, "OdooBot"], "zip": "10110"}, {"accesses_count": 420, "action_id": false, "active": true, "active_lang_count": 1, "active_partner": true, "activity_calendar_event_id": false, "activity_date_deadline": false, "activity_exception_decoration": false, "activity_exception_icon": false, "activity_ids": [], "activity_state": false, "activity_summary": false, "activity_type_icon": false, "activity_type_id": false, "activity_user_id": false, "additional_info": false, "additional_note": false, "address_id": [1, "My Company"], "allocation_count": 0.0, "allocation_display": "0", "allocation_remaining_display": "0", "api_key_ids": [], "attendance_manager_id": false, "attendance_state": "checked_in", "avatar_1024": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==", "avatar_128": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==", "avatar_1920": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==", "avatar_256": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==", "avatar_512": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==", "bank_account_count": 0, "bank_ids": [], "barcode": false, "birthday": false, "calendar_last_notif_ack": "2025-03-07 06:11:58", "can_edit": true, "can_publish": true, "category_id": [], "category_ids": [], "certificate": "other", "channel_ids": [1, 3, 4, 5], "child_ids": [], "children": 0, "city": false, "coach_id": [1, "Administrator"], "color": 0, "comment": false, "commercial_company_name": false, "commercial_partner_id": [10, "mark"], "companies_count": 1, "company_id": [1, "My Company"], "company_ids": [1], "company_name": false, "company_registry": false, "company_type": "person", "complete_name": "mark", "contact_address": "\n\n  \n", "contact_address_inline": "", "contract_ids": [], "country_code": false, "country_id": false, "country_of_birth": false, "create_date": "2025-03-07 06:11:58", "create_employee": false, "create_employee_id": false, "create_uid": [2, "Updated Partner via API Test **********"], "credit": 0.0, "credit_limit": 0.0, "credit_to_invoice": 0.0, "currency_id": [1, "USD"], "current_leave_state": false, "customer_rank": 0, "date": false, "days_sales_outstanding": 0.0, "debit": 0.0, "debit_limit": 0.0, "department_id": false, "display_extra_hours": true, "display_name": "mark", "duplicated_bank_account_partners_count": 0, "email": "<EMAIL>", "email_formatted": "\"mark\" <<EMAIL>>", "email_normalized": "<EMAIL>", "emergency_contact": false, "emergency_phone": false, "employee": false, "employee_bank_account_id": false, "employee_cars_count": 0, "employee_count": 1, "employee_country_id": false, "employee_id": [5, "mark"], "employee_ids": [5], "employee_parent_id": [1, "Administrator"], "employee_resource_calendar_id": [1, "Standard 40 hours/week"], "employee_skill_ids": [], "employee_type": "employee", "employees_count": 1, "expense_manager_id": [2, "Updated Partner via API Test **********"], "fiscal_country_codes": "US", "function": false, "gender": false, "groups_count": 33, "groups_id": [8, 17, 31, 28, 34, 52, 61, 19, 51, 42, 44, 3, 9, 56, 1, 100, 13, 16, 60, 30, 33, 55, 50, 7, 59, 24, 22, 21, 23, 18, 32, 27, 26], "has_message": true, "has_unreconciled_entries": false, "hours_last_month": 0.0, "hours_last_month_display": "0", "hr_icon_display": "presence_present", "hr_presence_state": "present", "id": 6, "identification_id": false, "im_status": "offline", "image_1024": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==", "image_128": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==", "image_1920": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==", "image_256": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==", "image_512": "PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==", "industry_id": false, "invoice_ids": [], "invoice_warn": "no-message", "invoice_warn_msg": false, "is_absent": false, "is_blacklisted": false, "is_company": false, "is_public": false, "is_published": false, "is_system": true, "job_title": false, "journal_item_count": 0, "km_home_work": 0, "lang": "en_US", "last_activity": false, "last_activity_time": false, "last_check_in": "2025-03-11 03:37:09", "last_check_out": false, "last_time_entries_checked": false, "leave_date_to": false, "leave_manager_id": [2, "Updated Partner via API Test **********"], "log_ids": [23], "login": "<EMAIL>", "login_date": "2025-03-11 05:37:06", "marital": "single", "meeting_count": 5, "meeting_ids": [11, 5, 4, 3, 2], "message_attachment_count": 0, "message_bounce": 0, "message_follower_ids": [43], "message_has_error": false, "message_has_error_counter": 0, "message_has_sms_error": false, "message_ids": [122], "message_is_follower": true, "message_needaction": false, "message_needaction_counter": 0, "message_partner_ids": [3], "mobile": false, "mobile_blacklisted": false, "mobile_phone": false, "my_activity_date_deadline": false, "name": "mark", "new_password": "", "notification_type": "email", "oauth1_session_ids": [], "oauth2_session_ids": [], "odoobot_failed": false, "odoobot_state": "onboarding_emoji", "parent_id": false, "parent_name": false, "partner_gid": 0, "partner_id": [10, "mark"], "partner_latitude": 0.0, "partner_longitude": 0.0, "partner_share": false, "passport_id": false, "password": "", "payment_token_count": 0, "payment_token_ids": [], "peppol_eas": false, "peppol_endpoint": false, "permit_no": false, "phone": false, "phone_blacklisted": false, "phone_mobile_search": false, "phone_sanitized": false, "phone_sanitized_blacklisted": false, "pin": false, "pinned_apps": [], "place_of_birth": false, "plan_to_change_bike": false, "plan_to_change_car": false, "private_city": false, "private_country_id": false, "private_email": false, "private_lang": false, "private_phone": false, "private_state_id": false, "private_street": false, "private_street2": false, "private_zip": false, "project_ids": [], "property_account_payable_id": [14, "211000 Account Payable"], "property_account_position_id": false, "property_account_receivable_id": [6, "121000 Account Receivable"], "property_payment_term_id": false, "property_product_pricelist": false, "property_supplier_payment_term_id": false, "rating_ids": [], "ref": false, "ref_company_ids": [], "request_overtime": false, "res_users_settings_id": [2, "mark"], "res_users_settings_ids": [2], "resource_calendar_id": [1, "Standard 40 hours/week"], "resource_ids": [16], "resume_line_ids": [4], "rules_count": 124, "same_company_registry_partner_id": false, "same_vat_partner_id": false, "self": [10, "mark"], "share": false, "show_credit_limit": false, "show_leaves": true, "sidebar_type": "large", "signature": "<p data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\">mark</p>", "signup_expiration": false, "signup_token": false, "signup_type": false, "signup_url": "http://localhost:8069/web/login?db=loneworker&login=mark%40markshaw.com", "signup_valid": false, "spouse_birthdate": false, "spouse_complete_name": false, "ssnid": false, "starred_message_ids": [], "state": "active", "state_id": false, "street": false, "street2": false, "study_field": false, "study_school": false, "supplier_rank": 0, "task_count": 0, "task_ids": [], "title": false, "total_invoiced": 0.0, "total_overtime": 0.0, "totp_enabled": false, "totp_trusted_device_ids": [], "trust": "normal", "type": "contact", "tz": "Asia/Bangkok", "tz_offset": "+0700", "ubl_cii_format": false, "use_partner_credit_limit": false, "user_group_warning": false, "user_id": false, "user_ids": [6], "vat": false, "visa_expire": false, "visa_no": false, "visitor_ids": [9], "website": false, "website_id": false, "website_message_ids": [], "website_published": false, "website_url": "#", "work_contact_id": [10, "mark"], "work_email": "<EMAIL>", "work_location_id": false, "work_phone": false, "write_date": "2025-03-07 09:56:38", "write_uid": [6, "mark"], "zip": false}], "website_id_expanded": {"company_id": [1, "My Company"], "default_lang_id": [1, "English (US)"], "display_name": "My Website", "id": 1, "language_ids": [1], "name": "My Website", "user_id": [4, "Public user"]}}, "react_native_component": "// screens/res_company/ResCompanyDetailsScreen.js\n\nimport React, { useState, useEffect, useContext } from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  ScrollView,\n  TouchableOpacity,\n  Alert,\n  ActivityIndicator,\n  Linking\n} from 'react-native';\nimport { Ionicons } from '@expo/vector-icons';\nimport { AuthContext } from '../../context/AuthContext';\nimport { apiRequest } from '../../api/odooApi';\n\nconst ResCompanyDetailsScreen = ({ route, navigation }) => {\n  const { recordId } = route.params;\n  const { authToken, serverConfig } = useContext(AuthContext);\n  const [record, setRecord] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchRecordDetails();\n  }, [recordId]);\n\n  const fetchRecordDetails = async () => {\n    try {\n      setLoading(true);\n      \n      // Comprehensive field list to match the form\n      const fields = [\n        'is_company_details_empty',\n        'snailmail_duplex',\n        'report_footer',\n        'account_journal_early_pay_discount_gain_account_id',\n        'invoice_is_ubl_cii',\n        'social_twitter',\n        'expense_product_id',\n        'message_has_sms_error',\n        'account_storno',\n        'currency_exchange_journal_id',\n        'expense_currency_exchange_account_id',\n        'sequence',\n        'website_id',\n        'bank_ids',\n        'account_enabled_tax_country_ids',\n        'color',\n        'overtime_company_threshold',\n        'account_discount_expense_allocation_id',\n        'attendance_kiosk_key',\n        'expense_journal_id',\n        'default_cash_difference_income_account_id',\n        'all_child_ids',\n        'email_primary_color',\n        'income_currency_exchange_account_id',\n        'snailmail_cover',\n        'message_has_error',\n        'logo',\n        'account_use_credit_limit',\n        'attendance_kiosk_mode',\n        'website',\n        'invoice_is_email',\n        'leave_timesheet_task_id',\n        'social_instagram',\n        'account_sale_tax_id',\n        'project_time_mode_id',\n        'mobile',\n        'chart_template',\n        'alias_domain_name',\n        'tax_calculation_rounding_method',\n        'account_journal_payment_debit_account_id',\n        'country_id',\n        'account_discount_income_allocation_id',\n        'resource_calendar_id',\n        'uses_default_logo',\n        'iap_enrich_auto_done',\n        'account_purchase_tax_id',\n        'social_tiktok',\n        'company_details',\n        'active',\n        'alias_domain_id',\n        'message_is_follower',\n        'invoice_is_snailmail',\n        'quick_edit_mode',\n        'website_message_ids',\n        'email_secondary_color',\n        'account_cash_basis_base_account_id',\n        'hr_presence_control_ip_list',\n        'write_uid',\n        'fiscalyear_last_day',\n        'account_journal_suspense_account_id',\n        'bounce_email',\n        'invoice_terms_html',\n        'layout_background_image',\n        'bounce_formatted',\n        'timesheet_encode_uom_id',\n        'parent_ids',\n        'email',\n        'catchall_email',\n        'company_expense_allowed_payment_method_line_ids',\n        'expects_chart_of_accounts',\n        'attendance_kiosk_url',\n        'hr_presence_last_compute_date',\n        'tax_cash_basis_journal_id',\n        'attendance_barcode_source',\n        'snailmail_color',\n        'create_date',\n        'account_fiscal_country_id',\n        'hr_attendance_overtime',\n        'display_name',\n        'account_default_pos_receivable_account_id',\n        'message_needaction_counter',\n        'default_from_email',\n        'catchall_formatted',\n        'nomenclature_id',\n        'root_id',\n        'transfer_account_id',\n        'message_needaction',\n        'social_linkedin',\n        'fiscalyear_last_month',\n        'country_code',\n        'city',\n        'currency_id',\n        'attendance_from_systray',\n        'overtime_employee_threshold',\n        'fiscal_position_ids',\n        'invoice_terms',\n        'vat',\n        'max_tax_lock_date',\n        'message_attachment_count',\n        'message_has_error_counter',\n        'street2',\n        'multi_vat_foreign_country_ids',\n        'logo_web',\n        'message_ids',\n        'revenue_accrual_account_id',\n        'user_ids',\n        'partner_id',\n        'qr_code',\n        'bank_account_code_prefix',\n        'account_opening_journal_id',\n        'message_partner_ids',\n        'write_date',\n        'hr_attendance_display_overtime',\n        'state_id',\n        'email_formatted',\n        'automatic_entry_default_journal_id',\n        'cash_account_code_prefix',\n        'social_youtube',\n        'external_report_layout_id',\n        'display_invoice_amount_total_words',\n        'id',\n        'account_opening_date',\n        'attendance_kiosk_use_pin',\n        'layout_background',\n        'account_journal_early_pay_discount_loss_account_id',\n        'parent_id',\n        'parent_path',\n        'primary_color',\n        'terms_type',\n        'payment_onboarding_payment_method',\n        'transfer_account_code_prefix',\n        'attendance_kiosk_delay',\n        'account_journal_payment_credit_account_id',\n        'phone',\n        'company_registry',\n        'child_ids',\n        'social_facebook',\n        'rating_ids',\n        'partner_gid',\n        'secondary_color',\n        'paperformat_id',\n        'appbar_image',\n        'resource_calendar_ids',\n        'create_uid',\n        'report_header',\n        'period_lock_date',\n        'has_message',\n        'bank_journal_ids',\n        'anglo_saxon_accounting',\n        'zip',\n        'invoice_is_download',\n        'tax_exigibility',\n        'account_opening_move_id',\n        'overtime_start_date',\n        'hr_presence_control_email_amount',\n        'street',\n        'expense_accrual_account_id',\n        'font',\n        'tax_lock_date',\n        'internal_project_id',\n        'employee_properties_definition',\n        'incoterm_id',\n        'message_follower_ids',\n        'social_github',\n        'fiscalyear_lock_date',\n        'name',\n        'default_cash_difference_expense_account_id'\n      ];\n      \n      // Use search_read instead of read for better compatibility\n      const response = await apiRequest(\n        serverConfig.baseUrl,\n        serverConfig.database,\n        authToken,\n        `/api/v2/search_read/res.company`,\n        'POST',\n        {\n          domain: [['id', '=', recordId]],\n          fields,\n          limit: 1\n        }\n      );\n      \n      console.log('Record details response:', response);\n      \n      if (response && response.length > 0) {\n        setRecord(response[0]);\n      } else {\n        Alert.alert('Error', 'Could not find record details');\n        navigation.goBack();\n      }\n    } catch (error) {\n      console.error('Error fetching record details:', error);\n      Alert.alert('Error', 'Failed to load record details');\n      navigation.goBack();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleEdit = () => {\n    navigation.navigate('ResCompanyForm', { recordId, record });\n  };\n\n  const handleDelete = () => {\n    Alert.alert(\n      'Delete Record',\n      'Are you sure you want to delete this record? This action cannot be undone.',\n      [\n        { text: 'Cancel', style: 'cancel' },\n        { \n          text: 'Delete', \n          style: 'destructive',\n          onPress: async () => {\n            try {\n              await apiRequest(\n                serverConfig.baseUrl,\n                serverConfig.database,\n                authToken,\n                `/api/v2/unlink/res.company`,\n                'DELETE',\n                { ids: [recordId] }\n              );\n              navigation.goBack();\n            } catch (error) {\n              console.error('Error deleting record:', error);\n              Alert.alert('Error', 'Failed to delete record');\n            }\n          }\n        },\n      ]\n    );\n  };\n\n  // Generate initials for avatar\n  const getInitials = (name) => {\n    if (!name) return '?';\n    return name\n      .split(' ')\n      .map(part => part[0])\n      .join('')\n      .toUpperCase()\n      .substring(0, 2);\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'Not specified';\n    try {\n      return new Date(dateString).toLocaleDateString();\n    } catch (e) {\n      return dateString;\n    }\n  };\n\n  if (loading || !record) {\n    return (\n      <View style={styles.loadingContainer}>\n        <ActivityIndicator size=\"large\" color=\"#3498db\" />\n        <Text style={styles.loadingText}>Loading record details...</Text>\n      </View>\n    );\n  }\n\n  return (\n    <ScrollView style={styles.container}>\n      // Header with basic info\n      <View style={styles.header}>\n        <View style={styles.avatar}>\n          <Text style={styles.avatarText}>{getInitials(record.name || record.display_name || \"\")}</Text>\n        </View>\n        \n        <Text style={styles.name}>{record.name || record.display_name || `Record #${record.id}`}</Text>\n      </View>\n      \n      // Action buttons\n      <View style={styles.actions}>\n        <TouchableOpacity style={styles.actionButton} onPress={handleEdit}>\n          <Ionicons name=\"create-outline\" size={22} color=\"#3498db\" />\n          <Text style={styles.actionText}>Edit</Text>\n        </TouchableOpacity>\n        \n        <TouchableOpacity style={styles.actionButton} onPress={handleDelete}>\n          <Ionicons name=\"trash-outline\" size={22} color=\"#e74c3c\" />\n          <Text style={styles.actionText}>Delete</Text>\n        </TouchableOpacity>\n      </View>\n      \n      // Record Information Section\n      <View style={styles.section}>\n        <Text style={styles.sectionTitle}>Record Information</Text>\n        \n        // Generate field rows dynamically based on field type\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Is Company Details Empty</Text>\n          <Text style={styles.infoValue}>{record.is_company_details_empty ? 'Yes' : 'No'}</Text>\n        </View>\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Both sides</Text>\n          <Text style={styles.infoValue}>{record.snailmail_duplex ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.report_footer && (\n          <View style={styles.infoBlock}>\n            <View style={styles.infoBlockHeader}>\n              <Ionicons name=\"code-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n              <Text style={styles.infoBlockLabel}>Report Footer</Text>\n            </View>\n            <Text style={styles.infoBlockText}>[HTML content]</Text>\n          </View>\n        )}\n        {record.account_journal_early_pay_discount_gain_account_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Cash Discount Write-Off Gain Account</Text>\n            <Text style={styles.infoValue}>{'account_journal_early_pay_discount_gain_account_id' === 'display_name' ? record.account_journal_early_pay_discount_gain_account_id : (record.account_journal_early_pay_discount_gain_account_id ? record.account_journal_early_pay_discount_gain_account_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Generate Peppol format by default</Text>\n          <Text style={styles.infoValue}>{record.invoice_is_ubl_cii ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.social_twitter !== undefined && record.social_twitter !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Twitter Account</Text>\n            <Text style={styles.infoValue}>{record.social_twitter}</Text>\n          </View>\n        )}\n        {record.expense_product_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Default Expense Category</Text>\n            <Text style={styles.infoValue}>{'expense_product_id' === 'display_name' ? record.expense_product_id : (record.expense_product_id ? record.expense_product_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>SMS Delivery error</Text>\n          <Text style={styles.infoValue}>{record.message_has_sms_error ? 'Yes' : 'No'}</Text>\n        </View>\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Storno accounting</Text>\n          <Text style={styles.infoValue}>{record.account_storno ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.currency_exchange_journal_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Exchange Gain or Loss Journal</Text>\n            <Text style={styles.infoValue}>{'currency_exchange_journal_id' === 'display_name' ? record.currency_exchange_journal_id : (record.currency_exchange_journal_id ? record.currency_exchange_journal_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.expense_currency_exchange_account_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Loss Exchange Rate Account</Text>\n            <Text style={styles.infoValue}>{'expense_currency_exchange_account_id' === 'display_name' ? record.expense_currency_exchange_account_id : (record.expense_currency_exchange_account_id ? record.expense_currency_exchange_account_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.sequence !== undefined && record.sequence !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calculator-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Sequence</Text>\n            <Text style={styles.infoValue}>{record.sequence}</Text>\n          </View>\n        )}\n        {record.website_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Website</Text>\n            <Text style={styles.infoValue}>{'website_id' === 'display_name' ? record.website_id : (record.website_id ? record.website_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.bank_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"list-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Banks</Text>\n            <Text style={styles.infoValue}>{`record.$bank_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        {record.account_enabled_tax_country_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"flag-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>l10n-used countries</Text>\n            <Text style={styles.infoValue}>{`record.$account_enabled_tax_country_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        {record.color !== undefined && record.color !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calculator-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Color</Text>\n            <Text style={styles.infoValue}>{record.color}</Text>\n          </View>\n        )}\n        {record.overtime_company_threshold !== undefined && record.overtime_company_threshold !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Tolerance Time In Favor Of Company</Text>\n            <Text style={styles.infoValue}>{record.overtime_company_threshold}</Text>\n          </View>\n        )}\n        {record.account_discount_expense_allocation_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Separate account for expense discount</Text>\n            <Text style={styles.infoValue}>{'account_discount_expense_allocation_id' === 'display_name' ? record.account_discount_expense_allocation_id : (record.account_discount_expense_allocation_id ? record.account_discount_expense_allocation_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.attendance_kiosk_key !== undefined && record.attendance_kiosk_key !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Attendance Kiosk Key</Text>\n            <Text style={styles.infoValue}>{record.attendance_kiosk_key}</Text>\n          </View>\n        )}\n        {record.expense_journal_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Default Expense Journal</Text>\n            <Text style={styles.infoValue}>{'expense_journal_id' === 'display_name' ? record.expense_journal_id : (record.expense_journal_id ? record.expense_journal_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.default_cash_difference_income_account_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Cash Difference Income</Text>\n            <Text style={styles.infoValue}>{'default_cash_difference_income_account_id' === 'display_name' ? record.default_cash_difference_income_account_id : (record.default_cash_difference_income_account_id ? record.default_cash_difference_income_account_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.all_child_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"list-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>All Child</Text>\n            <Text style={styles.infoValue}>{`record.$all_child_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        {record.email_primary_color !== undefined && record.email_primary_color !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"mail-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Email Header Color</Text>\n            <Text style={styles.infoValue}>{record.email_primary_color}</Text>\n          </View>\n        )}\n        {record.income_currency_exchange_account_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Gain Exchange Rate Account</Text>\n            <Text style={styles.infoValue}>{'income_currency_exchange_account_id' === 'display_name' ? record.income_currency_exchange_account_id : (record.income_currency_exchange_account_id ? record.income_currency_exchange_account_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Add a Cover Page</Text>\n          <Text style={styles.infoValue}>{record.snailmail_cover ? 'Yes' : 'No'}</Text>\n        </View>\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Message Delivery error</Text>\n          <Text style={styles.infoValue}>{record.message_has_error ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.logo && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"document-attach-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Company Logo</Text>\n            <Text style={styles.infoValue}>[Binary data]</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Sales Credit Limit</Text>\n          <Text style={styles.infoValue}>{record.account_use_credit_limit ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.attendance_kiosk_mode && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"options-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Attendance Mode</Text>\n            <Text style={styles.infoValue}>{record.attendance_kiosk_mode.replace(/_/g, ' ').charAt(0).toUpperCase() + \n              record.attendance_kiosk_mode.replace(/_/g, ' ').slice(1)}</Text>\n          </View>\n        )}\n        {record.website !== undefined && record.website !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Website Link</Text>\n            <Text style={styles.infoValue}>{record.website}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"mail-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Email by default</Text>\n          <Text style={styles.infoValue}>{record.invoice_is_email ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.leave_timesheet_task_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Time Off Task</Text>\n            <Text style={styles.infoValue}>{'leave_timesheet_task_id' === 'display_name' ? record.leave_timesheet_task_id : (record.leave_timesheet_task_id ? record.leave_timesheet_task_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.social_instagram !== undefined && record.social_instagram !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Instagram Account</Text>\n            <Text style={styles.infoValue}>{record.social_instagram}</Text>\n          </View>\n        )}\n        {record.account_sale_tax_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Default Sale Tax</Text>\n            <Text style={styles.infoValue}>{'account_sale_tax_id' === 'display_name' ? record.account_sale_tax_id : (record.account_sale_tax_id ? record.account_sale_tax_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.project_time_mode_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Project Time Unit</Text>\n            <Text style={styles.infoValue}>{'project_time_mode_id' === 'display_name' ? record.project_time_mode_id : (record.project_time_mode_id ? record.project_time_mode_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.mobile !== undefined && record.mobile !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"call-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Mobile</Text>\n            <Text style={styles.infoValue}>{record.mobile}</Text>\n          </View>\n        )}\n        {record.chart_template && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"options-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Chart Template</Text>\n            <Text style={styles.infoValue}>{record.chart_template.replace(/_/g, ' ').charAt(0).toUpperCase() + \n              record.chart_template.replace(/_/g, ' ').slice(1)}</Text>\n          </View>\n        )}\n        {record.alias_domain_name !== undefined && record.alias_domain_name !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"person-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Alias Domain Name</Text>\n            <Text style={styles.infoValue}>{record.alias_domain_name}</Text>\n          </View>\n        )}\n        {record.tax_calculation_rounding_method && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"options-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Tax Calculation Rounding Method</Text>\n            <Text style={styles.infoValue}>{record.tax_calculation_rounding_method.replace(/_/g, ' ').charAt(0).toUpperCase() + \n              record.tax_calculation_rounding_method.replace(/_/g, ' ').slice(1)}</Text>\n          </View>\n        )}\n        {record.account_journal_payment_debit_account_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Journal Outstanding Receipts</Text>\n            <Text style={styles.infoValue}>{'account_journal_payment_debit_account_id' === 'display_name' ? record.account_journal_payment_debit_account_id : (record.account_journal_payment_debit_account_id ? record.account_journal_payment_debit_account_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.country_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"flag-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Country</Text>\n            <Text style={styles.infoValue}>{'country_id' === 'display_name' ? record.country_id : (record.country_id ? record.country_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.account_discount_income_allocation_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Separate account for income discount</Text>\n            <Text style={styles.infoValue}>{'account_discount_income_allocation_id' === 'display_name' ? record.account_discount_income_allocation_id : (record.account_discount_income_allocation_id ? record.account_discount_income_allocation_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.resource_calendar_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Default Working Hours</Text>\n            <Text style={styles.infoValue}>{'resource_calendar_id' === 'display_name' ? record.resource_calendar_id : (record.resource_calendar_id ? record.resource_calendar_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Uses Default Logo</Text>\n          <Text style={styles.infoValue}>{record.uses_default_logo ? 'Yes' : 'No'}</Text>\n        </View>\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Enrich Done</Text>\n          <Text style={styles.infoValue}>{record.iap_enrich_auto_done ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.account_purchase_tax_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Default Purchase Tax</Text>\n            <Text style={styles.infoValue}>{'account_purchase_tax_id' === 'display_name' ? record.account_purchase_tax_id : (record.account_purchase_tax_id ? record.account_purchase_tax_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.social_tiktok !== undefined && record.social_tiktok !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>TikTok Account</Text>\n            <Text style={styles.infoValue}>{record.social_tiktok}</Text>\n          </View>\n        )}\n        {record.company_details && (\n          <View style={styles.infoBlock}>\n            <View style={styles.infoBlockHeader}>\n              <Ionicons name=\"code-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n              <Text style={styles.infoBlockLabel}>Company Details</Text>\n            </View>\n            <Text style={styles.infoBlockText}>[HTML content]</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Active</Text>\n          <Text style={styles.infoValue}>{record.active ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.alias_domain_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Email Domain</Text>\n            <Text style={styles.infoValue}>{'alias_domain_id' === 'display_name' ? record.alias_domain_id : (record.alias_domain_id ? record.alias_domain_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Is Follower</Text>\n          <Text style={styles.infoValue}>{record.message_is_follower ? 'Yes' : 'No'}</Text>\n        </View>\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Send by Post</Text>\n          <Text style={styles.infoValue}>{record.invoice_is_snailmail ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.quick_edit_mode && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"options-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Quick encoding</Text>\n            <Text style={styles.infoValue}>{record.quick_edit_mode.replace(/_/g, ' ').charAt(0).toUpperCase() + \n              record.quick_edit_mode.replace(/_/g, ' ').slice(1)}</Text>\n          </View>\n        )}\n        {record.website_message_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"list-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Website Messages</Text>\n            <Text style={styles.infoValue}>{`record.$website_message_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        {record.email_secondary_color !== undefined && record.email_secondary_color !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"mail-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Email Button Color</Text>\n            <Text style={styles.infoValue}>{record.email_secondary_color}</Text>\n          </View>\n        )}\n        {record.account_cash_basis_base_account_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Base Tax Received Account</Text>\n            <Text style={styles.infoValue}>{'account_cash_basis_base_account_id' === 'display_name' ? record.account_cash_basis_base_account_id : (record.account_cash_basis_base_account_id ? record.account_cash_basis_base_account_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.hr_presence_control_ip_list !== undefined && record.hr_presence_control_ip_list !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Valid IP addresses</Text>\n            <Text style={styles.infoValue}>{record.hr_presence_control_ip_list}</Text>\n          </View>\n        )}\n        {record.write_uid && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Last Updated by</Text>\n            <Text style={styles.infoValue}>{'write_uid' === 'display_name' ? record.write_uid : (record.write_uid ? record.write_uid[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.fiscalyear_last_day !== undefined && record.fiscalyear_last_day !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calculator-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Fiscalyear Last Day</Text>\n            <Text style={styles.infoValue}>{record.fiscalyear_last_day}</Text>\n          </View>\n        )}\n        {record.account_journal_suspense_account_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Journal Suspense Account</Text>\n            <Text style={styles.infoValue}>{'account_journal_suspense_account_id' === 'display_name' ? record.account_journal_suspense_account_id : (record.account_journal_suspense_account_id ? record.account_journal_suspense_account_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.bounce_email !== undefined && record.bounce_email !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"mail-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Bounce Email</Text>\n            <Text style={styles.infoValue}>{record.bounce_email}</Text>\n          </View>\n        )}\n        {record.invoice_terms_html && (\n          <View style={styles.infoBlock}>\n            <View style={styles.infoBlockHeader}>\n              <Ionicons name=\"code-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n              <Text style={styles.infoBlockLabel}>Default Terms and Conditions as a Web page</Text>\n            </View>\n            <Text style={styles.infoBlockText}>[HTML content]</Text>\n          </View>\n        )}\n        {record.layout_background_image && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"image-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Background Image</Text>\n            <Text style={styles.infoValue}>[Binary data]</Text>\n          </View>\n        )}\n        {record.bounce_formatted !== undefined && record.bounce_formatted !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Bounce</Text>\n            <Text style={styles.infoValue}>{record.bounce_formatted}</Text>\n          </View>\n        )}\n        {record.timesheet_encode_uom_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Timesheet Encoding Unit</Text>\n            <Text style={styles.infoValue}>{'timesheet_encode_uom_id' === 'display_name' ? record.timesheet_encode_uom_id : (record.timesheet_encode_uom_id ? record.timesheet_encode_uom_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.parent_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"list-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Parent</Text>\n            <Text style={styles.infoValue}>{`record.$parent_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        {record.email !== undefined && record.email !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"mail-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Email</Text>\n            <Text style={styles.infoValue}>{record.email}</Text>\n          </View>\n        )}\n        {record.catchall_email !== undefined && record.catchall_email !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"mail-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Catchall Email</Text>\n            <Text style={styles.infoValue}>{record.catchall_email}</Text>\n          </View>\n        )}\n        {record.company_expense_allowed_payment_method_line_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"list-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Payment methods available for expenses paid by company</Text>\n            <Text style={styles.infoValue}>{`record.$company_expense_allowed_payment_method_line_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Expects a Chart of Accounts</Text>\n          <Text style={styles.infoValue}>{record.expects_chart_of_accounts ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.attendance_kiosk_url !== undefined && record.attendance_kiosk_url !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Attendance Kiosk Url</Text>\n            <Text style={styles.infoValue}>{record.attendance_kiosk_url}</Text>\n          </View>\n        )}\n        {record.hr_presence_last_compute_date && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Hr Presence Last Compute Date</Text>\n            <Text style={styles.infoValue}>{formatDate(record.hr_presence_last_compute_date)}</Text>\n          </View>\n        )}\n        {record.tax_cash_basis_journal_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Cash Basis Journal</Text>\n            <Text style={styles.infoValue}>{'tax_cash_basis_journal_id' === 'display_name' ? record.tax_cash_basis_journal_id : (record.tax_cash_basis_journal_id ? record.tax_cash_basis_journal_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.attendance_barcode_source && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"options-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Barcode Source</Text>\n            <Text style={styles.infoValue}>{record.attendance_barcode_source.replace(/_/g, ' ').charAt(0).toUpperCase() + \n              record.attendance_barcode_source.replace(/_/g, ' ').slice(1)}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Snailmail Color</Text>\n          <Text style={styles.infoValue}>{record.snailmail_color ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.create_date && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Created on</Text>\n            <Text style={styles.infoValue}>{formatDate(record.create_date)}</Text>\n          </View>\n        )}\n        {record.account_fiscal_country_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"flag-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Fiscal Country</Text>\n            <Text style={styles.infoValue}>{'account_fiscal_country_id' === 'display_name' ? record.account_fiscal_country_id : (record.account_fiscal_country_id ? record.account_fiscal_country_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Count Extra Hours</Text>\n          <Text style={styles.infoValue}>{record.hr_attendance_overtime ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.display_name !== undefined && record.display_name !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"person-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Display Name</Text>\n            <Text style={styles.infoValue}>{record.display_name}</Text>\n          </View>\n        )}\n        {record.account_default_pos_receivable_account_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Default PoS Receivable Account</Text>\n            <Text style={styles.infoValue}>{'account_default_pos_receivable_account_id' === 'display_name' ? record.account_default_pos_receivable_account_id : (record.account_default_pos_receivable_account_id ? record.account_default_pos_receivable_account_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.message_needaction_counter !== undefined && record.message_needaction_counter !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calculator-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Number of Actions</Text>\n            <Text style={styles.infoValue}>{record.message_needaction_counter}</Text>\n          </View>\n        )}\n        {record.default_from_email !== undefined && record.default_from_email !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"mail-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Default From</Text>\n            <Text style={styles.infoValue}>{record.default_from_email}</Text>\n          </View>\n        )}\n        {record.catchall_formatted !== undefined && record.catchall_formatted !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Catchall</Text>\n            <Text style={styles.infoValue}>{record.catchall_formatted}</Text>\n          </View>\n        )}\n        {record.nomenclature_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Nomenclature</Text>\n            <Text style={styles.infoValue}>{'nomenclature_id' === 'display_name' ? record.nomenclature_id : (record.nomenclature_id ? record.nomenclature_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.root_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Root</Text>\n            <Text style={styles.infoValue}>{'root_id' === 'display_name' ? record.root_id : (record.root_id ? record.root_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.transfer_account_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Inter-Banks Transfer Account</Text>\n            <Text style={styles.infoValue}>{'transfer_account_id' === 'display_name' ? record.transfer_account_id : (record.transfer_account_id ? record.transfer_account_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Action Needed</Text>\n          <Text style={styles.infoValue}>{record.message_needaction ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.social_linkedin !== undefined && record.social_linkedin !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>LinkedIn Account</Text>\n            <Text style={styles.infoValue}>{record.social_linkedin}</Text>\n          </View>\n        )}\n        {record.fiscalyear_last_month && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"options-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Fiscalyear Last Month</Text>\n            <Text style={styles.infoValue}>{record.fiscalyear_last_month.replace(/_/g, ' ').charAt(0).toUpperCase() + \n              record.fiscalyear_last_month.replace(/_/g, ' ').slice(1)}</Text>\n          </View>\n        )}\n        {record.country_code !== undefined && record.country_code !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"flag-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Country Code</Text>\n            <Text style={styles.infoValue}>{record.country_code}</Text>\n          </View>\n        )}\n        {record.city !== undefined && record.city !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"business-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>City</Text>\n            <Text style={styles.infoValue}>{record.city}</Text>\n          </View>\n        )}\n        {record.currency_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Currency</Text>\n            <Text style={styles.infoValue}>{'currency_id' === 'display_name' ? record.currency_id : (record.currency_id ? record.currency_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Attendance From Systray</Text>\n          <Text style={styles.infoValue}>{record.attendance_from_systray ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.overtime_employee_threshold !== undefined && record.overtime_employee_threshold !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Tolerance Time In Favor Of Employee</Text>\n            <Text style={styles.infoValue}>{record.overtime_employee_threshold}</Text>\n          </View>\n        )}\n        {record.fiscal_position_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"list-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Fiscal Position</Text>\n            <Text style={styles.infoValue}>{`record.$fiscal_position_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        {record.invoice_terms && (\n          <View style={styles.infoBlock}>\n            <View style={styles.infoBlockHeader}>\n              <Ionicons name=\"code-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n              <Text style={styles.infoBlockLabel}>Default Terms and Conditions</Text>\n            </View>\n            <Text style={styles.infoBlockText}>[HTML content]</Text>\n          </View>\n        )}\n        {record.vat !== undefined && record.vat !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Tax ID</Text>\n            <Text style={styles.infoValue}>{record.vat}</Text>\n          </View>\n        )}\n        {record.max_tax_lock_date && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Max Tax Lock Date</Text>\n            <Text style={styles.infoValue}>{formatDate(record.max_tax_lock_date)}</Text>\n          </View>\n        )}\n        {record.message_attachment_count !== undefined && record.message_attachment_count !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calculator-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Attachment Count</Text>\n            <Text style={styles.infoValue}>{record.message_attachment_count}</Text>\n          </View>\n        )}\n        {record.message_has_error_counter !== undefined && record.message_has_error_counter !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calculator-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Number of errors</Text>\n            <Text style={styles.infoValue}>{record.message_has_error_counter}</Text>\n          </View>\n        )}\n        {record.street2 !== undefined && record.street2 !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"home-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Street2</Text>\n            <Text style={styles.infoValue}>{record.street2}</Text>\n          </View>\n        )}\n        {record.multi_vat_foreign_country_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"flag-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Foreign VAT countries</Text>\n            <Text style={styles.infoValue}>{`record.$multi_vat_foreign_country_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        {record.logo_web && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"document-attach-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Logo Web</Text>\n            <Text style={styles.infoValue}>[Binary data]</Text>\n          </View>\n        )}\n        {record.message_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"list-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Messages</Text>\n            <Text style={styles.infoValue}>{`record.$message_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        {record.revenue_accrual_account_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Revenue Accrual Account</Text>\n            <Text style={styles.infoValue}>{'revenue_accrual_account_id' === 'display_name' ? record.revenue_accrual_account_id : (record.revenue_accrual_account_id ? record.revenue_accrual_account_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.user_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"list-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Accepted Users</Text>\n            <Text style={styles.infoValue}>{`record.$user_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        {record.partner_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Partner</Text>\n            <Text style={styles.infoValue}>{'partner_id' === 'display_name' ? record.partner_id : (record.partner_id ? record.partner_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Display QR-code on invoices</Text>\n          <Text style={styles.infoValue}>{record.qr_code ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.bank_account_code_prefix !== undefined && record.bank_account_code_prefix !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Prefix of the bank accounts</Text>\n            <Text style={styles.infoValue}>{record.bank_account_code_prefix}</Text>\n          </View>\n        )}\n        {record.account_opening_journal_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Opening Journal</Text>\n            <Text style={styles.infoValue}>{'account_opening_journal_id' === 'display_name' ? record.account_opening_journal_id : (record.account_opening_journal_id ? record.account_opening_journal_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.message_partner_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"list-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Followers (Partners)</Text>\n            <Text style={styles.infoValue}>{`record.$message_partner_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        {record.write_date && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Last Updated on</Text>\n            <Text style={styles.infoValue}>{formatDate(record.write_date)}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Display Extra Hours</Text>\n          <Text style={styles.infoValue}>{record.hr_attendance_display_overtime ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.state_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"flag-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Fed. State</Text>\n            <Text style={styles.infoValue}>{'state_id' === 'display_name' ? record.state_id : (record.state_id ? record.state_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.email_formatted !== undefined && record.email_formatted !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"mail-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Formatted Email</Text>\n            <Text style={styles.infoValue}>{record.email_formatted}</Text>\n          </View>\n        )}\n        {record.automatic_entry_default_journal_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Automatic Entry Default Journal</Text>\n            <Text style={styles.infoValue}>{'automatic_entry_default_journal_id' === 'display_name' ? record.automatic_entry_default_journal_id : (record.automatic_entry_default_journal_id ? record.automatic_entry_default_journal_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.cash_account_code_prefix !== undefined && record.cash_account_code_prefix !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Prefix of the cash accounts</Text>\n            <Text style={styles.infoValue}>{record.cash_account_code_prefix}</Text>\n          </View>\n        )}\n        {record.social_youtube !== undefined && record.social_youtube !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Youtube Account</Text>\n            <Text style={styles.infoValue}>{record.social_youtube}</Text>\n          </View>\n        )}\n        {record.external_report_layout_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Document Template</Text>\n            <Text style={styles.infoValue}>{'external_report_layout_id' === 'display_name' ? record.external_report_layout_id : (record.external_report_layout_id ? record.external_report_layout_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Total amount of invoice in letters</Text>\n          <Text style={styles.infoValue}>{record.display_invoice_amount_total_words ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.account_opening_date && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Opening Entry</Text>\n            <Text style={styles.infoValue}>{formatDate(record.account_opening_date)}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Employee PIN Identification</Text>\n          <Text style={styles.infoValue}>{record.attendance_kiosk_use_pin ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.layout_background && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"options-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Layout Background</Text>\n            <Text style={styles.infoValue}>{record.layout_background.replace(/_/g, ' ').charAt(0).toUpperCase() + \n              record.layout_background.replace(/_/g, ' ').slice(1)}</Text>\n          </View>\n        )}\n        {record.account_journal_early_pay_discount_loss_account_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Cash Discount Write-Off Loss Account</Text>\n            <Text style={styles.infoValue}>{'account_journal_early_pay_discount_loss_account_id' === 'display_name' ? record.account_journal_early_pay_discount_loss_account_id : (record.account_journal_early_pay_discount_loss_account_id ? record.account_journal_early_pay_discount_loss_account_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.parent_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Parent Company</Text>\n            <Text style={styles.infoValue}>{'parent_id' === 'display_name' ? record.parent_id : (record.parent_id ? record.parent_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.parent_path !== undefined && record.parent_path !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Parent Path</Text>\n            <Text style={styles.infoValue}>{record.parent_path}</Text>\n          </View>\n        )}\n        {record.primary_color !== undefined && record.primary_color !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Primary Color</Text>\n            <Text style={styles.infoValue}>{record.primary_color}</Text>\n          </View>\n        )}\n        {record.terms_type && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"options-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Terms & Conditions format</Text>\n            <Text style={styles.infoValue}>{record.terms_type.replace(/_/g, ' ').charAt(0).toUpperCase() + \n              record.terms_type.replace(/_/g, ' ').slice(1)}</Text>\n          </View>\n        )}\n        {record.payment_onboarding_payment_method && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"options-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Selected onboarding payment method</Text>\n            <Text style={styles.infoValue}>{record.payment_onboarding_payment_method.replace(/_/g, ' ').charAt(0).toUpperCase() + \n              record.payment_onboarding_payment_method.replace(/_/g, ' ').slice(1)}</Text>\n          </View>\n        )}\n        {record.transfer_account_code_prefix !== undefined && record.transfer_account_code_prefix !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Prefix of the transfer accounts</Text>\n            <Text style={styles.infoValue}>{record.transfer_account_code_prefix}</Text>\n          </View>\n        )}\n        {record.attendance_kiosk_delay !== undefined && record.attendance_kiosk_delay !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calculator-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Attendance Kiosk Delay</Text>\n            <Text style={styles.infoValue}>{record.attendance_kiosk_delay}</Text>\n          </View>\n        )}\n        {record.account_journal_payment_credit_account_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Journal Outstanding Payments</Text>\n            <Text style={styles.infoValue}>{'account_journal_payment_credit_account_id' === 'display_name' ? record.account_journal_payment_credit_account_id : (record.account_journal_payment_credit_account_id ? record.account_journal_payment_credit_account_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.phone !== undefined && record.phone !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"call-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Phone</Text>\n            <Text style={styles.infoValue}>{record.phone}</Text>\n          </View>\n        )}\n        {record.company_registry !== undefined && record.company_registry !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Company ID</Text>\n            <Text style={styles.infoValue}>{record.company_registry}</Text>\n          </View>\n        )}\n        {record.child_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"list-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Branches</Text>\n            <Text style={styles.infoValue}>{`record.$child_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        {record.social_facebook !== undefined && record.social_facebook !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Facebook Account</Text>\n            <Text style={styles.infoValue}>{record.social_facebook}</Text>\n          </View>\n        )}\n        {record.rating_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"list-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Ratings</Text>\n            <Text style={styles.infoValue}>{`record.$rating_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        {record.partner_gid !== undefined && record.partner_gid !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calculator-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Company database ID</Text>\n            <Text style={styles.infoValue}>{record.partner_gid}</Text>\n          </View>\n        )}\n        {record.secondary_color !== undefined && record.secondary_color !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Secondary Color</Text>\n            <Text style={styles.infoValue}>{record.secondary_color}</Text>\n          </View>\n        )}\n        {record.paperformat_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Paper format</Text>\n            <Text style={styles.infoValue}>{'paperformat_id' === 'display_name' ? record.paperformat_id : (record.paperformat_id ? record.paperformat_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.appbar_image && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"image-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Apps Menu Footer Image</Text>\n            <Text style={styles.infoValue}>[Binary data]</Text>\n          </View>\n        )}\n        {record.resource_calendar_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"list-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Working Hours</Text>\n            <Text style={styles.infoValue}>{`record.$resource_calendar_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        {record.create_uid && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Created by</Text>\n            <Text style={styles.infoValue}>{'create_uid' === 'display_name' ? record.create_uid : (record.create_uid ? record.create_uid[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.report_header && (\n          <View style={styles.infoBlock}>\n            <View style={styles.infoBlockHeader}>\n              <Ionicons name=\"code-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n              <Text style={styles.infoBlockLabel}>Company Tagline</Text>\n            </View>\n            <Text style={styles.infoBlockText}>[HTML content]</Text>\n          </View>\n        )}\n        {record.period_lock_date && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Journals Entries Lock Date</Text>\n            <Text style={styles.infoValue}>{formatDate(record.period_lock_date)}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Has Message</Text>\n          <Text style={styles.infoValue}>{record.has_message ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.bank_journal_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"list-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Bank Journals</Text>\n            <Text style={styles.infoValue}>{`record.$bank_journal_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Use anglo-saxon accounting</Text>\n          <Text style={styles.infoValue}>{record.anglo_saxon_accounting ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.zip !== undefined && record.zip !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"mail-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Zip</Text>\n            <Text style={styles.infoValue}>{record.zip}</Text>\n          </View>\n        )}\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Download by default</Text>\n          <Text style={styles.infoValue}>{record.invoice_is_download ? 'Yes' : 'No'}</Text>\n        </View>\n        \n        <View style={styles.infoRow}>\n          <Ionicons name=\"checkmark-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n          <Text style={styles.infoLabel}>Use Cash Basis</Text>\n          <Text style={styles.infoValue}>{record.tax_exigibility ? 'Yes' : 'No'}</Text>\n        </View>\n        {record.account_opening_move_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Opening Journal Entry</Text>\n            <Text style={styles.infoValue}>{'account_opening_move_id' === 'display_name' ? record.account_opening_move_id : (record.account_opening_move_id ? record.account_opening_move_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.overtime_start_date && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Extra Hours Starting Date</Text>\n            <Text style={styles.infoValue}>{formatDate(record.overtime_start_date)}</Text>\n          </View>\n        )}\n        {record.hr_presence_control_email_amount !== undefined && record.hr_presence_control_email_amount !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"mail-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}># emails to send</Text>\n            <Text style={styles.infoValue}>{record.hr_presence_control_email_amount}</Text>\n          </View>\n        )}\n        {record.street !== undefined && record.street !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"home-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Street</Text>\n            <Text style={styles.infoValue}>{record.street}</Text>\n          </View>\n        )}\n        {record.expense_accrual_account_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Expense Accrual Account</Text>\n            <Text style={styles.infoValue}>{'expense_accrual_account_id' === 'display_name' ? record.expense_accrual_account_id : (record.expense_accrual_account_id ? record.expense_accrual_account_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.font && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"options-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Font</Text>\n            <Text style={styles.infoValue}>{record.font.replace(/_/g, ' ').charAt(0).toUpperCase() + \n              record.font.replace(/_/g, ' ').slice(1)}</Text>\n          </View>\n        )}\n        {record.tax_lock_date && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Tax Return Lock Date</Text>\n            <Text style={styles.infoValue}>{formatDate(record.tax_lock_date)}</Text>\n          </View>\n        )}\n        {record.internal_project_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Internal Project</Text>\n            <Text style={styles.infoValue}>{'internal_project_id' === 'display_name' ? record.internal_project_id : (record.internal_project_id ? record.internal_project_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.employee_properties_definition !== undefined && record.employee_properties_definition !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Employee Properties</Text>\n            <Text style={styles.infoValue}>{record.employee_properties_definition}</Text>\n          </View>\n        )}\n        {record.incoterm_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Default incoterm</Text>\n            <Text style={styles.infoValue}>{'incoterm_id' === 'display_name' ? record.incoterm_id : (record.incoterm_id ? record.incoterm_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n        {record.message_follower_ids && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"list-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Followers</Text>\n            <Text style={styles.infoValue}>{`record.$message_follower_ids.length + ' items'`}</Text>\n          </View>\n        )}\n        {record.social_github !== undefined && record.social_github !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"information-circle-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>GitHub Account</Text>\n            <Text style={styles.infoValue}>{record.social_github}</Text>\n          </View>\n        )}\n        {record.fiscalyear_lock_date && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"calendar-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>All Users Lock Date</Text>\n            <Text style={styles.infoValue}>{formatDate(record.fiscalyear_lock_date)}</Text>\n          </View>\n        )}\n        {record.name !== undefined && record.name !== false && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"person-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Company Name</Text>\n            <Text style={styles.infoValue}>{record.name}</Text>\n          </View>\n        )}\n        {record.default_cash_difference_expense_account_id && (\n          <View style={styles.infoRow}>\n            <Ionicons name=\"link-outline\" size={20} color=\"#333\" style={styles.infoIcon} />\n            <Text style={styles.infoLabel}>Cash Difference Expense</Text>\n            <Text style={styles.infoValue}>{'default_cash_difference_expense_account_id' === 'display_name' ? record.default_cash_difference_expense_account_id : (record.default_cash_difference_expense_account_id ? record.default_cash_difference_expense_account_id[1] : 'Not specified')}</Text>\n          </View>\n        )}\n      </View>\n    </ScrollView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5',\n  },\n  loadingContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    backgroundColor: '#f5f5f5',\n  },\n  loadingText: {\n    marginTop: 16,\n    fontSize: 16,\n    color: '#333',\n  },\n  header: {\n    alignItems: 'center',\n    backgroundColor: 'white',\n    paddingVertical: 20,\n    borderBottomWidth: 1,\n    borderBottomColor: '#eee',\n  },\n  avatar: {\n    width: 80,\n    height: 80,\n    borderRadius: 40,\n    backgroundColor: '#3498db',\n    justifyContent: 'center',\n    alignItems: 'center',\n    marginBottom: 10,\n  },\n  avatarText: {\n    color: 'white',\n    fontSize: 30,\n    fontWeight: 'bold',\n  },\n  name: {\n    fontSize: 22,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 4,\n  },\n  actions: {\n    flexDirection: 'row',\n    justifyContent: 'space-around',\n    backgroundColor: 'white',\n    paddingVertical: 15,\n    borderBottomWidth: 1,\n    borderBottomColor: '#eee',\n    marginBottom: 15,\n  },\n  actionButton: {\n    alignItems: 'center',\n  },\n  actionText: {\n    marginTop: 5,\n    color: '#333',\n  },\n  section: {\n    backgroundColor: 'white',\n    borderRadius: 8,\n    marginHorizontal: 15,\n    marginBottom: 15,\n    padding: 15,\n    shadowColor: '#000',\n    shadowOffset: { width: 0, height: 1 },\n    shadowOpacity: 0.1,\n    shadowRadius: 2,\n    elevation: 2,\n  },\n  sectionTitle: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 15,\n    borderBottomWidth: 1,\n    borderBottomColor: '#eee',\n    paddingBottom: 10,\n  },\n  infoRow: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginBottom: 12,\n  },\n  infoIcon: {\n    marginRight: 10,\n    width: 20,\n  },\n  infoLabel: {\n    width: 120,\n    fontSize: 16,\n    color: '#333',\n  },\n  infoValue: {\n    flex: 1,\n    fontSize: 16,\n    color: '#666',\n  },\n  linkValue: {\n    flex: 1,\n    fontSize: 16,\n    color: '#3498db',\n  },\n  infoBlock: {\n    marginBottom: 12,\n  },\n  infoBlockHeader: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginBottom: 6,\n  },\n  infoBlockLabel: {\n    fontSize: 16,\n    color: '#333',\n  },\n  infoBlockText: {\n    fontSize: 16,\n    color: '#666',\n    marginLeft: 30,\n    lineHeight: 24,\n  }\n});\n\nexport default ResCompanyDetailsScreen;", "next_steps": {"navigator": "Set up a stack navigator using react-navigation to link the list and details screens.", "list_screen": "Create a list screen with FlatList to display res.company records and navigate to the details screen."}, "sample_code": {"navigator": "import { NavigationContainer } from '@react-navigation/native';\nimport { createStackNavigator } from '@react-navigation/stack';\nimport RescompanyListScreen from './RescompanyListScreen';\nimport RescompanyDetailsScreen from './RescompanyDetailsScreen';\n\nconst Stack = createStackNavigator();\n\nfunction App() {\n  return (\n    <NavigationContainer>\n      <Stack.Navigator>\n        <Stack.Screen name=\"RescompanyList\" component={RescompanyListScreen} />\n        <Stack.Screen name=\"RescompanyDetails\" component={RescompanyDetailsScreen} />\n      </Stack.Navigator>\n    </NavigationContainer>\n  );\n}\nexport default App;", "list_screen": "import React from 'react';\nimport {\n  FlatList,\n  TouchableOpacity,\n  Text,\n  StyleSheet\n} from 'react-native';\n\nconst RescompanyListScreen = ({ navigation }) => {\n  const companys = [\n    {\n  \"account_cash_basis_base_account_id\": false,\n  \"account_default_pos_receivable_account_id\": [\n    40,\n    \"101300 Account Receivable (PoS)\"\n  ],\n  \"account_discount_expense_allocation_id\": false,\n  \"account_discount_income_allocation_id\": false,\n  \"account_enabled_tax_country_ids\": [\n    233\n  ],\n  \"account_fiscal_country_id\": [\n    233,\n    \"United States\"\n  ],\n  \"account_journal_early_pay_discount_gain_account_id\": [\n    37,\n    \"643000 Cash Discount Gain\"\n  ],\n  \"account_journal_early_pay_discount_loss_account_id\": [\n    27,\n    \"443000 Cash Discount Loss\"\n  ],\n  \"account_journal_payment_credit_account_id\": [\n    45,\n    \"101404 Outstanding Payments\"\n  ],\n  \"account_journal_payment_debit_account_id\": [\n    44,\n    \"101403 Outstanding Receipts\"\n  ],\n  \"account_journal_suspense_account_id\": [\n    43,\n    \"101402 Bank Suspense Account\"\n  ],\n  \"account_opening_date\": \"2025-01-01\",\n  \"account_opening_journal_id\": false,\n  \"account_opening_move_id\": false,\n  \"account_purchase_tax_id\": [\n    2,\n    \"15%\"\n  ],\n  \"account_sale_tax_id\": [\n    1,\n    \"15%\"\n  ],\n  \"account_storno\": false,\n  \"account_use_credit_limit\": false,\n  \"active\": true,\n  \"alias_domain_id\": false,\n  \"alias_domain_name\": false,\n  \"all_child_ids\": [],\n  \"anglo_saxon_accounting\": true,\n  \"appbar_image\": \"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\",\n  \"attendance_barcode_source\": \"front\",\n  \"attendance_from_systray\": true,\n  \"attendance_kiosk_delay\": 10,\n  \"attendance_kiosk_key\": \"0d42c393-2a7c-4aac-99a1-dadadd4e9678\",\n  \"attendance_kiosk_mode\": \"barcode_manual\",\n  \"attendance_kiosk_url\": \"http://localhost:8069/hr_attendance/0d42c393-2a7c-4aac-99a1-dadadd4e9678\",\n  \"attendance_kiosk_use_pin\": false,\n  \"automatic_entry_default_journal_id\": false,\n  \"bank_account_code_prefix\": \"1014\",\n  \"bank_ids\": [],\n  \"bank_journal_ids\": [\n    6\n  ],\n  \"bounce_email\": \"\",\n  \"bounce_formatted\": \"\",\n  \"cash_account_code_prefix\": \"1015\",\n  \"catchall_email\": \"\",\n  \"catchall_formatted\": \"\",\n  \"chart_template\": \"generic_coa\",\n  \"child_ids\": [],\n  \"city\": \"\",\n  \"color\": 1,\n  \"company_details\": false,\n  \"company_expense_allowed_payment_method_line_ids\": [\n    3\n  ],\n  \"company_registry\": false,\n  \"country_code\": \"US\",\n  \"country_id\": [\n    233,\n    \"United States\"\n  ],\n  \"create_date\": \"2025-03-06 10:20:53\",\n  \"create_uid\": false,\n  \"currency_exchange_journal_id\": [\n    4,\n    \"Exchange Difference\"\n  ],\n  \"currency_id\": [\n    1,\n    \"USD\"\n  ],\n  \"default_cash_difference_expense_account_id\": [\n    36,\n    \"642000 Cash Difference Loss\"\n  ],\n  \"default_cash_difference_income_account_id\": [\n    26,\n    \"442000 Cash Difference Gain\"\n  ],\n  \"default_from_email\": false,\n  \"display_invoice_amount_total_words\": false,\n  \"display_name\": \"My Company\",\n  \"email\": \"<EMAIL>\",\n  \"email_formatted\": \"\\\"My Company\\\" <<EMAIL>>\",\n  \"email_primary_color\": \"#000000\",\n  \"email_secondary_color\": \"#875A7B\",\n  \"employee_properties_definition\": [],\n  \"expects_chart_of_accounts\": true,\n  \"expense_accrual_account_id\": false,\n  \"expense_currency_exchange_account_id\": [\n    35,\n    \"641000 Foreign Exchange Loss\"\n  ],\n  \"expense_journal_id\": [\n    2,\n    \"Vendor Bills\"\n  ],\n  \"expense_product_id\": [\n    6,\n    \"[EXP_GEN] Others\"\n  ],\n  \"external_report_layout_id\": false,\n  \"fiscal_position_ids\": [],\n  \"fiscalyear_last_day\": 31,\n  \"fiscalyear_last_month\": \"12\",\n  \"fiscalyear_lock_date\": false,\n  \"font\": \"Lato\",\n  \"has_message\": true,\n  \"hr_attendance_display_overtime\": true,\n  \"hr_attendance_overtime\": true,\n  \"hr_presence_control_email_amount\": 0,\n  \"hr_presence_control_ip_list\": false,\n  \"hr_presence_last_compute_date\": \"2025-03-14 01:02:24\",\n  \"iap_enrich_auto_done\": true,\n  \"id\": 1,\n  \"income_currency_exchange_account_id\": [\n    25,\n    \"441000 Foreign Exchange Gain\"\n  ],\n  \"incoterm_id\": false,\n  \"internal_project_id\": [\n    1,\n    \"Internal\"\n  ],\n  \"invoice_is_download\": true,\n  \"invoice_is_email\": true,\n  \"invoice_is_snailmail\": false,\n  \"invoice_is_ubl_cii\": false,\n  \"invoice_terms\": false,\n  \"invoice_terms_html\": false,\n  \"is_company_details_empty\": true,\n  \"layout_background\": \"Blank\",\n  \"layout_background_image\": false,\n  \"leave_timesheet_task_id\": [\n    3,\n    \"Time Off\"\n  ],\n  \"logo\": \"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\",\n  \"logo_web\": \"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\",\n  \"max_tax_lock_date\": \"1-01-01\",\n  \"message_attachment_count\": 0,\n  \"message_follower_ids\": [],\n  \"message_has_error\": false,\n  \"message_has_error_counter\": 0,\n  \"message_has_sms_error\": false,\n  \"message_ids\": [\n    1336\n  ],\n  \"message_is_follower\": false,\n  \"message_needaction\": false,\n  \"message_needaction_counter\": 0,\n  \"message_partner_ids\": [],\n  \"mobile\": \"False\",\n  \"multi_vat_foreign_country_ids\": [],\n  \"name\": \"My Company\",\n  \"nomenclature_id\": [\n    1,\n    \"Default Nomenclature\"\n  ],\n  \"overtime_company_threshold\": 15,\n  \"overtime_employee_threshold\": 15,\n  \"overtime_start_date\": \"2025-03-01\",\n  \"paperformat_id\": [\n    1,\n    \"A4\"\n  ],\n  \"parent_id\": false,\n  \"parent_ids\": [\n    1\n  ],\n  \"parent_path\": \"1/\",\n  \"partner_gid\": 0,\n  \"partner_id\": [\n    1,\n    \"My Company\"\n  ],\n  \"payment_onboarding_payment_method\": false,\n  \"period_lock_date\": false,\n  \"phone\": \"\",\n  \"primary_color\": false,\n  \"project_time_mode_id\": [\n    4,\n    \"Hours\"\n  ],\n  \"qr_code\": false,\n  \"quick_edit_mode\": false,\n  \"rating_ids\": [],\n  \"report_footer\": false,\n  \"report_header\": false,\n  \"resource_calendar_id\": [\n    1,\n    \"Standard 40 hours/week\"\n  ],\n  \"resource_calendar_ids\": [\n    1\n  ],\n  \"revenue_accrual_account_id\": false,\n  \"root_id\": [\n    1,\n    \"My Company\"\n  ],\n  \"secondary_color\": false,\n  \"sequence\": 0,\n  \"snailmail_color\": true,\n  \"snailmail_cover\": false,\n  \"snailmail_duplex\": false,\n  \"social_facebook\": false,\n  \"social_github\": false,\n  \"social_instagram\": false,\n  \"social_linkedin\": false,\n  \"social_tiktok\": false,\n  \"social_twitter\": false,\n  \"social_youtube\": false,\n  \"state_id\": false,\n  \"street\": \"45\",\n  \"street2\": false,\n  \"tax_calculation_rounding_method\": \"round_per_line\",\n  \"tax_cash_basis_journal_id\": [\n    5,\n    \"Cash Basis Taxes\"\n  ],\n  \"tax_exigibility\": false,\n  \"tax_lock_date\": false,\n  \"terms_type\": \"plain\",\n  \"timesheet_encode_uom_id\": [\n    4,\n    \"Hours\"\n  ],\n  \"transfer_account_code_prefix\": \"1017\",\n  \"transfer_account_id\": [\n    46,\n    \"101701 Liquidity Transfer\"\n  ],\n  \"user_ids\": [\n    2,\n    6\n  ],\n  \"uses_default_logo\": true,\n  \"vat\": false,\n  \"website\": false,\n  \"website_id\": [\n    1,\n    \"My Website\"\n  ],\n  \"website_message_ids\": [],\n  \"write_date\": \"2025-03-14 01:02:24\",\n  \"write_uid\": [\n    1,\n    \"OdooBot\"\n  ],\n  \"zip\": \"\",\n  \"account_default_pos_receivable_account_id_expanded\": {\n    \"account_type\": \"asset_receivable\",\n    \"code\": \"101300\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"display_name\": \"101300 Account Receivable (PoS)\",\n    \"id\": 40,\n    \"name\": \"Account Receivable (PoS)\"\n  },\n  \"account_enabled_tax_country_ids_expanded\": [\n    {\n      \"address_format\": \"%(street)s\\n%(street2)s\\n%(city)s %(state_code)s %(zip)s\\n%(country_name)s\",\n      \"address_view_id\": false,\n      \"code\": \"US\",\n      \"country_group_ids\": [],\n      \"create_date\": \"2025-03-06 10:20:54\",\n      \"create_uid\": [\n        1,\n        \"OdooBot\"\n      ],\n      \"currency_id\": [\n        1,\n        \"USD\"\n      ],\n      \"display_name\": \"United States\",\n      \"id\": 233,\n      \"image_url\": \"/base/static/img/country_flags/us.png\",\n      \"is_stripe_supported_country\": true,\n      \"name\": \"United States\",\n      \"name_position\": \"before\",\n      \"phone_code\": 1,\n      \"state_ids\": [\n        68,\n        69,\n        10,\n        9,\n        70,\n        12,\n        60,\n        11,\n        13,\n        14,\n        15,\n        17,\n        16,\n        18,\n        61,\n        19,\n        62,\n        20,\n        24,\n        21,\n        22,\n        23,\n        25,\n        26,\n        27,\n        42,\n        41,\n        28,\n        63,\n        43,\n        44,\n        46,\n        64,\n        45,\n        29,\n        36,\n        37,\n        30,\n        32,\n        33,\n        34,\n        31,\n        35,\n        38,\n        39,\n        40,\n        47,\n        66,\n        65,\n        48,\n        49,\n        50,\n        51,\n        52,\n        53,\n        55,\n        67,\n        54,\n        56,\n        58,\n        57,\n        59\n      ],\n      \"state_required\": true,\n      \"vat_label\": \"\",\n      \"write_date\": \"2025-03-06 10:20:54\",\n      \"write_uid\": [\n        1,\n        \"OdooBot\"\n      ],\n      \"zip_required\": true\n    }\n  ],\n  \"account_fiscal_country_id_expanded\": {\n    \"code\": \"US\",\n    \"display_name\": \"United States\",\n    \"id\": 233,\n    \"name\": \"United States\"\n  },\n  \"account_journal_early_pay_discount_gain_account_id_expanded\": {\n    \"account_type\": \"income\",\n    \"code\": \"643000\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"display_name\": \"643000 Cash Discount Gain\",\n    \"id\": 37,\n    \"name\": \"Cash Discount Gain\"\n  },\n  \"account_journal_early_pay_discount_loss_account_id_expanded\": {\n    \"account_type\": \"expense\",\n    \"code\": \"443000\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"display_name\": \"443000 Cash Discount Loss\",\n    \"id\": 27,\n    \"name\": \"Cash Discount Loss\"\n  },\n  \"account_journal_payment_credit_account_id_expanded\": {\n    \"account_type\": \"asset_current\",\n    \"code\": \"101404\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"display_name\": \"101404 Outstanding Payments\",\n    \"id\": 45,\n    \"name\": \"Outstanding Payments\"\n  },\n  \"account_journal_payment_debit_account_id_expanded\": {\n    \"account_type\": \"asset_current\",\n    \"code\": \"101403\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"display_name\": \"101403 Outstanding Receipts\",\n    \"id\": 44,\n    \"name\": \"Outstanding Receipts\"\n  },\n  \"account_journal_suspense_account_id_expanded\": {\n    \"account_type\": \"asset_current\",\n    \"code\": \"101402\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"display_name\": \"101402 Bank Suspense Account\",\n    \"id\": 43,\n    \"name\": \"Bank Suspense Account\"\n  },\n  \"account_purchase_tax_id_expanded\": {\n    \"amount\": 15.0,\n    \"amount_type\": \"percent\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"country_id\": [\n      233,\n      \"United States\"\n    ],\n    \"display_name\": \"15%\",\n    \"id\": 2,\n    \"name\": \"15%\",\n    \"sequence\": 1,\n    \"tax_group_id\": [\n      1,\n      \"Tax 15%\"\n    ],\n    \"type_tax_use\": \"purchase\"\n  },\n  \"account_sale_tax_id_expanded\": {\n    \"amount\": 15.0,\n    \"amount_type\": \"percent\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"country_id\": [\n      233,\n      \"United States\"\n    ],\n    \"display_name\": \"15%\",\n    \"id\": 1,\n    \"name\": \"15%\",\n    \"sequence\": 1,\n    \"tax_group_id\": [\n      1,\n      \"Tax 15%\"\n    ],\n    \"type_tax_use\": \"sale\"\n  },\n  \"bank_journal_ids_expanded\": [\n    {\n      \"access_token\": false,\n      \"access_url\": \"#\",\n      \"access_warning\": \"\",\n      \"account_control_ids\": [],\n      \"accounting_date\": \"2025-03-14\",\n      \"active\": true,\n      \"activity_calendar_event_id\": false,\n      \"activity_date_deadline\": false,\n      \"activity_exception_decoration\": false,\n      \"activity_exception_icon\": false,\n      \"activity_ids\": [],\n      \"activity_state\": false,\n      \"activity_summary\": false,\n      \"activity_type_icon\": false,\n      \"activity_type_id\": false,\n      \"activity_user_id\": false,\n      \"alias_defaults\": false,\n      \"alias_domain\": false,\n      \"alias_domain_id\": false,\n      \"alias_email\": false,\n      \"alias_id\": false,\n      \"alias_name\": false,\n      \"available_payment_method_ids\": [\n        1,\n        2\n      ],\n      \"bank_acc_number\": false,\n      \"bank_account_id\": false,\n      \"bank_id\": false,\n      \"bank_statements_source\": \"undefined\",\n      \"code\": \"BNK1\",\n      \"color\": 0,\n      \"company_id\": [\n        1,\n        \"My Company\"\n      ],\n      \"company_partner_id\": [\n        1,\n        \"My Company\"\n      ],\n      \"country_code\": \"US\",\n      \"create_date\": \"2025-03-06 10:33:49\",\n      \"create_uid\": [\n        1,\n        \"OdooBot\"\n      ],\n      \"currency_id\": false,\n      \"current_statement_balance\": 0.0,\n      \"default_account_id\": [\n        41,\n        \"101401 Bank\"\n      ],\n      \"default_account_type\": \"asset_cash\",\n      \"display_name\": \"Bank\",\n      \"entries_count\": 0,\n      \"has_message\": false,\n      \"has_sequence_holes\": false,\n      \"has_statement_lines\": false,\n      \"id\": 6,\n      \"inbound_payment_method_line_ids\": [\n        1\n      ],\n      \"invoice_reference_model\": \"odoo\",\n      \"invoice_reference_type\": \"invoice\",\n      \"journal_group_ids\": [],\n      \"json_activity_data\": \"{\\\"activities\\\": []}\",\n      \"kanban_dashboard\": \"{\\\"currency_id\\\": 1, \\\"show_company\\\": false, \\\"number_to_check\\\": 0, \\\"to_check_balance\\\": \\\"$\\\\u00a00.00\\\", \\\"number_to_reconcile\\\": 0, \\\"account_balance\\\": \\\"$\\\\u00a00.00\\\", \\\"has_at_least_one_statement\\\": false, \\\"nb_lines_bank_account_balance\\\": false, \\\"outstanding_pay_account_balance\\\": \\\"$\\\\u00a00.00\\\", \\\"nb_lines_outstanding_pay_account_balance\\\": 0, \\\"last_balance\\\": \\\"$\\\\u00a00.00\\\", \\\"last_statement_id\\\": false, \\\"bank_statements_source\\\": \\\"undefined\\\", \\\"is_sample_data\\\": false, \\\"nb_misc_operations\\\": 0, \\\"misc_class\\\": \\\"\\\", \\\"misc_operations_balance\\\": \\\"$\\\\u00a00.00\\\"}\",\n      \"kanban_dashboard_graph\": \"[{\\\"values\\\": [{\\\"x\\\": \\\"12 Feb\\\", \\\"y\\\": -1.0, \\\"name\\\": \\\"12 February 2025\\\"}, {\\\"x\\\": \\\"17 Feb\\\", \\\"y\\\": 7.0, \\\"name\\\": \\\"17 February 2025\\\"}, {\\\"x\\\": \\\"22 Feb\\\", \\\"y\\\": 6.0, \\\"name\\\": \\\"22 February 2025\\\"}, {\\\"x\\\": \\\"27 Feb\\\", \\\"y\\\": 6.0, \\\"name\\\": \\\"27 February 2025\\\"}, {\\\"x\\\": \\\"4 Mar\\\", \\\"y\\\": 8.0, \\\"name\\\": \\\"4 March 2025\\\"}, {\\\"x\\\": \\\"9 Mar\\\", \\\"y\\\": 6.0, \\\"name\\\": \\\"9 March 2025\\\"}], \\\"title\\\": \\\"\\\", \\\"key\\\": \\\"Sample data\\\", \\\"area\\\": true, \\\"color\\\": \\\"#7c7bad\\\", \\\"is_sample_data\\\": true}]\",\n      \"last_statement_id\": false,\n      \"loss_account_id\": [\n        36,\n        \"642000 Cash Difference Loss\"\n      ],\n      \"message_attachment_count\": 0,\n      \"message_follower_ids\": [],\n      \"message_has_error\": false,\n      \"message_has_error_counter\": 0,\n      \"message_has_sms_error\": false,\n      \"message_ids\": [],\n      \"message_is_follower\": false,\n      \"message_needaction\": false,\n      \"message_needaction_counter\": 0,\n      \"message_partner_ids\": [],\n      \"my_activity_date_deadline\": false,\n      \"name\": \"Bank\",\n      \"outbound_payment_method_line_ids\": [\n        2\n      ],\n      \"payment_sequence\": true,\n      \"profit_account_id\": [\n        26,\n        \"442000 Cash Difference Gain\"\n      ],\n      \"rating_ids\": [],\n      \"refund_sequence\": false,\n      \"restrict_mode_hash_table\": false,\n      \"sale_activity_note\": false,\n      \"sale_activity_type_id\": false,\n      \"sale_activity_user_id\": false,\n      \"secure_sequence_id\": false,\n      \"selected_payment_method_codes\": \",manual,manual,\",\n      \"sequence\": 10,\n      \"sequence_override_regex\": false,\n      \"show_on_dashboard\": true,\n      \"suspense_account_id\": [\n        43,\n        \"101402 Bank Suspense Account\"\n      ],\n      \"type\": \"bank\",\n      \"website_message_ids\": [],\n      \"write_date\": \"2025-03-06 10:33:49\",\n      \"write_uid\": [\n        1,\n        \"OdooBot\"\n      ]\n    }\n  ],\n  \"company_expense_allowed_payment_method_line_ids_expanded\": [\n    {\n      \"available_payment_method_ids\": [\n        1,\n        2\n      ],\n      \"code\": \"manual\",\n      \"company_id\": [\n        1,\n        \"My Company\"\n      ],\n      \"create_date\": \"2025-03-06 10:33:49\",\n      \"create_uid\": [\n        1,\n        \"OdooBot\"\n      ],\n      \"display_name\": \"Manual\",\n      \"id\": 3,\n      \"journal_id\": [\n        7,\n        \"Cash\"\n      ],\n      \"name\": \"Manual\",\n      \"payment_account_id\": false,\n      \"payment_method_id\": [\n        2,\n        \"Manual\"\n      ],\n      \"payment_provider_id\": false,\n      \"payment_provider_state\": false,\n      \"payment_type\": \"outbound\",\n      \"sequence\": 10,\n      \"write_date\": \"2025-03-06 10:33:49\",\n      \"write_uid\": [\n        1,\n        \"OdooBot\"\n      ]\n    }\n  ],\n  \"country_id_expanded\": {\n    \"code\": \"US\",\n    \"display_name\": \"United States\",\n    \"id\": 233,\n    \"name\": \"United States\"\n  },\n  \"currency_exchange_journal_id_expanded\": {\n    \"code\": \"EXCH\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"display_name\": \"Exchange Difference\",\n    \"id\": 4,\n    \"invoice_reference_model\": \"odoo\",\n    \"invoice_reference_type\": \"invoice\",\n    \"name\": \"Exchange Difference\",\n    \"type\": \"general\"\n  },\n  \"currency_id_expanded\": {\n    \"display_name\": \"USD\",\n    \"id\": 1,\n    \"name\": \"USD\",\n    \"symbol\": \"$\"\n  },\n  \"default_cash_difference_expense_account_id_expanded\": {\n    \"account_type\": \"expense\",\n    \"code\": \"642000\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"display_name\": \"642000 Cash Difference Loss\",\n    \"id\": 36,\n    \"name\": \"Cash Difference Loss\"\n  },\n  \"default_cash_difference_income_account_id_expanded\": {\n    \"account_type\": \"income\",\n    \"code\": \"442000\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"display_name\": \"442000 Cash Difference Gain\",\n    \"id\": 26,\n    \"name\": \"Cash Difference Gain\"\n  },\n  \"expense_currency_exchange_account_id_expanded\": {\n    \"account_type\": \"expense\",\n    \"code\": \"641000\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"display_name\": \"641000 Foreign Exchange Loss\",\n    \"id\": 35,\n    \"name\": \"Foreign Exchange Loss\"\n  },\n  \"expense_journal_id_expanded\": {\n    \"code\": \"BILL\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"display_name\": \"Vendor Bills\",\n    \"id\": 2,\n    \"invoice_reference_model\": \"odoo\",\n    \"invoice_reference_type\": \"invoice\",\n    \"name\": \"Vendor Bills\",\n    \"type\": \"purchase\"\n  },\n  \"expense_product_id_expanded\": {\n    \"categ_id\": [\n      3,\n      \"All / Expenses\"\n    ],\n    \"detailed_type\": \"service\",\n    \"display_name\": \"[EXP_GEN] Others\",\n    \"id\": 6,\n    \"name\": \"Others\",\n    \"product_tmpl_id\": [\n      6,\n      \"[EXP_GEN] Others\"\n    ],\n    \"product_variant_ids\": [\n      6\n    ],\n    \"uom_id\": [\n      1,\n      \"Units\"\n    ],\n    \"uom_po_id\": [\n      1,\n      \"Units\"\n    ]\n  },\n  \"income_currency_exchange_account_id_expanded\": {\n    \"account_type\": \"income\",\n    \"code\": \"441000\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"display_name\": \"441000 Foreign Exchange Gain\",\n    \"id\": 25,\n    \"name\": \"Foreign Exchange Gain\"\n  },\n  \"internal_project_id_expanded\": {\n    \"alias_contact\": \"everyone\",\n    \"alias_defaults\": \"{'project_id': 1}\",\n    \"alias_id\": [\n      1,\n      \"Inactive Alias\"\n    ],\n    \"alias_model_id\": [\n      399,\n      \"Task\"\n    ],\n    \"display_name\": \"Internal\",\n    \"id\": 1,\n    \"last_update_status\": \"to_define\",\n    \"name\": \"Internal\",\n    \"privacy_visibility\": \"portal\",\n    \"rating_status\": \"stage\",\n    \"rating_status_period\": \"monthly\"\n  },\n  \"leave_timesheet_task_id_expanded\": {\n    \"display_name\": \"Time Off\",\n    \"id\": 3,\n    \"name\": \"Time Off\",\n    \"state\": \"01_in_progress\"\n  },\n  \"message_ids_expanded\": [\n    {\n      \"attachment_ids\": [],\n      \"author_avatar\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDI5OCwgNjMlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5VPC90ZXh0Pjwvc3ZnPg==\",\n      \"author_guest_id\": false,\n      \"author_id\": [\n        3,\n        \"Updated Partner via API Test **********\"\n      ],\n      \"body\": \"\",\n      \"child_ids\": [],\n      \"create_date\": \"2025-03-13 23:58:28\",\n      \"create_uid\": [\n        2,\n        \"Updated Partner via API Test **********\"\n      ],\n      \"date\": \"2025-03-13 23:58:28\",\n      \"description\": \"\",\n      \"display_name\": false,\n      \"email_add_signature\": false,\n      \"email_from\": \"\\\"Updated Partner via API Test **********\\\" <<EMAIL>>\",\n      \"email_layout_xmlid\": false,\n      \"has_error\": false,\n      \"has_sms_error\": false,\n      \"id\": 1336,\n      \"is_current_user_or_guest_author\": true,\n      \"is_internal\": true,\n      \"letter_ids\": [],\n      \"link_preview_ids\": [],\n      \"mail_activity_type_id\": false,\n      \"mail_ids\": [],\n      \"mail_server_id\": false,\n      \"message_id\": \"<895058085430815.1741910308.971532583236694-openerp-message-notify@4b9448334aa5>\",\n      \"message_type\": \"notification\",\n      \"model\": \"res.company\",\n      \"needaction\": false,\n      \"notification_ids\": [],\n      \"notified_partner_ids\": [],\n      \"parent_id\": false,\n      \"partner_ids\": [],\n      \"pinned_at\": false,\n      \"preview\": \"\",\n      \"rating_ids\": [],\n      \"rating_value\": 0.0,\n      \"reaction_ids\": [],\n      \"record_alias_domain_id\": false,\n      \"record_company_id\": false,\n      \"record_name\": false,\n      \"reply_to\": \"\\\"Updated Partner via API Test **********\\\" <<EMAIL>>\",\n      \"reply_to_force_new\": false,\n      \"res_id\": 1,\n      \"snailmail_error\": false,\n      \"starred\": false,\n      \"starred_partner_ids\": [],\n      \"subject\": false,\n      \"subtype_id\": [\n        2,\n        \"Note\"\n      ],\n      \"tracking_value_ids\": [\n        156\n      ],\n      \"write_date\": \"2025-03-13 23:58:28\",\n      \"write_uid\": [\n        2,\n        \"Updated Partner via API Test **********\"\n      ]\n    }\n  ],\n  \"nomenclature_id_expanded\": {\n    \"display_name\": \"Default Nomenclature\",\n    \"id\": 1,\n    \"name\": \"Default Nomenclature\",\n    \"upc_ean_conv\": \"always\"\n  },\n  \"paperformat_id_expanded\": {\n    \"display_name\": \"A4\",\n    \"dpi\": 90,\n    \"id\": 1,\n    \"name\": \"A4\"\n  },\n  \"parent_ids_expanded\": [\n    {\n      \"account_cash_basis_base_account_id\": false,\n      \"account_default_pos_receivable_account_id\": [\n        40,\n        \"101300 Account Receivable (PoS)\"\n      ],\n      \"account_discount_expense_allocation_id\": false,\n      \"account_discount_income_allocation_id\": false,\n      \"account_enabled_tax_country_ids\": [\n        233\n      ],\n      \"account_fiscal_country_id\": [\n        233,\n        \"United States\"\n      ],\n      \"account_journal_early_pay_discount_gain_account_id\": [\n        37,\n        \"643000 Cash Discount Gain\"\n      ],\n      \"account_journal_early_pay_discount_loss_account_id\": [\n        27,\n        \"443000 Cash Discount Loss\"\n      ],\n      \"account_journal_payment_credit_account_id\": [\n        45,\n        \"101404 Outstanding Payments\"\n      ],\n      \"account_journal_payment_debit_account_id\": [\n        44,\n        \"101403 Outstanding Receipts\"\n      ],\n      \"account_journal_suspense_account_id\": [\n        43,\n        \"101402 Bank Suspense Account\"\n      ],\n      \"account_opening_date\": \"2025-01-01\",\n      \"account_opening_journal_id\": false,\n      \"account_opening_move_id\": false,\n      \"account_purchase_tax_id\": [\n        2,\n        \"15%\"\n      ],\n      \"account_sale_tax_id\": [\n        1,\n        \"15%\"\n      ],\n      \"account_storno\": false,\n      \"account_use_credit_limit\": false,\n      \"active\": true,\n      \"alias_domain_id\": false,\n      \"alias_domain_name\": false,\n      \"all_child_ids\": [],\n      \"anglo_saxon_accounting\": true,\n      \"appbar_image\": \"iVBORw0KGgoAAAANSUhEUgAAAcIAAAB4CAYAAABhPvLiAAAABGdBTUEAALGPC/xhBQAAO4VJREFUeAHtfQm8H0WVbu4SCCQsYQtL8kBUwAcimTCAyygoGBllURE3UAcRkS1kyGPc2FRckE0gQJBn8nRGEVRUYBhGWYLCEJYIQgIGA8TIEggkgRBIyL33fV/drrrVXdXd1eu9N5z/79f/rq6u5dRXp+pUnTpV3bHvvvuOGhH9ttxyy95dd9117ZlnntkHL15N/jqQT8e8efO6n3vuuU47ow022KDv2Wef7b333nvXdnR0NE0Hs+6YNGlS94477tiRpEUwEUws3hQ+scCInIKJYOIi4PoMaT4xAogdPmjvOeOMM1oTgrfddlvnypUrO2zMtBCEUCI9Tf86+vr6Oj72sY91UggmaRFMBJOIAYVP3JYomAgmLgKuz7Dgk27SzQ4fM6HeW2+9tbeFGRiBGbHnnnt2RTOwmCDkTJBC8JprrmmcFtJx1llnMf8u30xQMInP1IVPhE90PydtRyMxcBdMBrDQruGCSbee9bQsBLs32mgjCh+N1wjOBMeMGdMHOtbSsw2BvN9++3Wh/J0+IQgSegQTUz1qsCSYuAMDwUQwiVpJh/QnA/3FcMOkk2uCV199deOzLwCjVJCYCSohaENGYfzKK6/0kBYKwIaFoJqqY220C8LXOxMUTNzOTTARTNiGMcLvkLZj916CSQyN/odhxyddmPVw9uUpS31ebDz8zZkzR6lDV61aFctwyZIlvZMnT+6NjHTqy9iTEmmhOvTll1/uWrFihVkjZVDOSuHXO3PmTA4MPLHr8xJMXCwFE8HERcD1ET4RTFwEXJ8ifNJsbx/RRoJ8M0G+fumll3oi61A+Nm0hqmalyMc7E+SaIAx4elqgY4RgwuqO/wSTOB58EkwEExcB10f4pBomsRmRm1RlHwqeLp8QpDq0TSFIRuEWCZTIKwSp+sPsuA0hKJi4bCWYCCYuAq6P8Ilg4iLg+hTmkyYFodoniFlWB61Dk7Q+9thjfbQOjVSQjc4EKQSpDiUdyS0SVIdyJshtI02rQ4GBYJJkBMHERUQwEUx8CLh+0p/UhIkjoNx0S/noLRLdW221VScMYWL5cCZ40EEH9bSxcZ9C8PDDD6fA984EW1SHCiYuKwkmgomLgOsjfCKYuAi4PqX5JCag3HRL+ahRynXXXdfFLRJ2ClSHcibINUH6YwbW+Ewwy6SZ6tBoJtgoHSwqT9ERTGxuEExiaPQ/CJ+4oAgmgomLgOtTiU9qF4RcE6Q6lFsT7JkgVZDcIgFh2MfN8ihHo8InbyaI/HuibSOEtFFaBBNCHP8JJnE8+CSYCCYuAq6P8En9mNS2RkjBwwszvk4IO0cdyhNjsP9InRiDYjQqeEgH1wSRT6o61No72RgtgonLsIKJYOIi4PoInwgmLgKuT118ElNdutkE+0DL2dExbdo0tVne3ifImeCyZcvUAdpQUzLBxgRPRG3H7Nmzu7hPEDPTmKCnahZhem644YZWDhAQTKIaGbgJnwxgoV2CiUZi4C6YDGChXYKJRmLgXhsmMUExkH4hF01VO9PWv6gOpWEMKGaiTQpBNSP1nXpBYUwh2PbpKIJJjI+ET2JwqAfBRDBxEXB9hE8axqTqGqGx0uHWhOQMjNah3CLR1pog1aG+zzpRCGojHQjkJoUxq0sw8TAtVBg8aN37qSvhE2k7EctI25G24yLg+tTOJ+rrE24+QT6GmOQB2ozd5mZ5ZJd74O19993XijpUd/iCieEh4RMDhXEIJgYK4xBMDBTGIZgYKIyjEUzKqkaVqWrWiTFUh0akNzoD42KpTx3KvG11KB4bpQPpCyYEPf4TTOJ48EkwEUxcBFwf4ZMWMSmjGlUVxI/qQtA4nzAaKmouvV2Dx6a1sT7JfYKCSYxzhU9icKgHwUQwcRFwfYRPWsakkGqUsy/S98UvfpGfMKIQjAnSNtWhpCXkxJim1wQFE5djBRPBxEXA9RE+EUxcBFyfNvikiGpUCR6qQxcsWBDbLE8VZNtCkCfGADLvFok2D9CmMBZMYswrfBKDQz0IJoKJi4DrI3wySJgE7SOkRJ4/f34nN8vz7NC1a9eamSBVkPyGH78n2MY+QdJC4TN69OiYECQdY8eO5Tpgz/Tp0xs/QFswcTlWMBFMXARcH+ETwcRFwPVpk09CVKNK6GkhaB+bRtJ5YgwMY/RHdRs1SCEwaSfGjBkzpo9bJNqyDmXZBROiYH7CJwYK4xBMDBTGIZgYKIxDMDFQGEermOQJQrVoC9WfOkAbQtBQyRkYhWCbH9WlCtK3X9GyDuVMsFFhDAAEE8MFxiGYGCiMQzAxUBiHYGKgMA7BxEBhHK1jkiUIFTG0hIw+pWSo1EKQm+UjzyaFj9o3EhnGON8TpBAEDT1tfkVCMDGsQIfwSQwO9SCYCCYuAq6P8MkQwSRNEOpNi13RDExNUzXNnAnqE2OanoFBHToiTR1KIQjL1V5skWhzs7xgohkBQpD1Q42B8IkBRTAxUBiHYGKgMA7BxEBhHIOGSUzAReRoYtQB2oZEODgTnDBhQu+MGTNa+Z4gslSb5SHwnP2KeiZofUXCJrVut2DiIiqYCCYuAq6P8Ilg4iLg+gwqnyS3T6jDXbkW5/uoLg/Q3nbbbdUG9YZnggQl6MSYhulgdQkmHqblQevCJzFghE9icKgHwUQwcRFwfQadT8z2CQoe/ubMmaPUXPanlEj3kiVL1BYJnKDC9cAm1wRHkBaqQ/kpJWzNiAlrzkq5XWPmzJlUh7qQ1ugjmLhgCiaCiYuA6yN8Ipi4CLg+Q4VPjCQhQb4RPklvc7M8slOjA9xj+wRJh14ThLEKzzFtVBgzP8GEKMR/gkkcDz4JJoKJi4DrI3wydDHhbIuCp8snBCl42haCkyZNogGPVwi2eWKMYOIwrfCJA4m0HRcSwUQw8SDgeg2p/qQLqs5Onhozfvz4Tp86lNah2LrQijqUqlmcWtO1cuVK3jve/OY3v3+LLbZ4aPPNN/86VKKnQSifcf311y/Hpvm7XFxr81EmzUMVE13KNlXEyFMw0cAP3AWTASy0KxUTnPq0MXh2G/w2ufHGGzfFgHcsrs2SF06nWnPnnXe+phMse+fsK9mf6LSk7Qz0sYJJP5908yvq3CfoO0Cbn1Jqa00w2ifozAR1RbV015ZLQxoTW0WMxt60ilgwcZlPMCmICXj2E4jyAzda3AeD4MPg88u4b7EnCsG0/kTajl/bxm1oXHJ6vfYnnbQOtY9Ns9Wh3KQOFmy0oyXTph2g3dnZ2WjeiealRrNaRTxUMWH9tKki5uelBJMYpwifxOBQD7mYoC3rwzfc2DX6ZPUn0nb8QlD6k+c6YxvqqTLgyADqUM4XyZ6NCiI9cgODevcJ4n0rjYcF5XYAjIg6fKfoDCVMQKo+RYdkqx9xxKexYnWp36XduRc0b/RXBBPs5+z6/e9/H7PwTcub/tyGAyEbVL95fILkHEyy8q7yrggmVfLJizvcMMkrTx3vhxsmQ6GPBe7SdgBCrOPiiTH42nvvNddcww6qcSGYd2JMV1dXUEdZpRGx8fDiAdoUyPZMkOkONUysAwRM/UydOnUTkPoErjWh17HHHjsDYb2/opgcc8wxG0IILkRiQfmjA3jhmWee+V/ezBOepCWPT3yYJJKp/FgUk8oZZiQwTDHJKFH1V8MUE9OGqyPgpiCYhGPStcMOO3RzJrhs2TJ1gHYbn1ICeR2zZ8/u4j5BzLZiwpjqC7zvueGGG3pxvQmV+elEcW6q0VgGfXJHx7Rp09QBArax0FDFBOQ6jeeuu+56FerLR4HTJxNYpT4inYl77bXXr3Fo+pJEIEJSCBMYPJyBNA5KpJP6CDXZKZdffvl/pwYYeBHEJyDXwWQgiVpchTGpJVd/IsMSE/DnXmjL/+wv0oAvgL4aPPnwgE+Qa1hiElSy8oEEExe7VEw4C+rliTE0jAETMmqTnYqafWHWyS/cx4QgBQ9pob7aGuG7RanPR+1XpLFQ2ik6wwkTqDqvAzQ/DYUHnVJnb2/v+YnwhTGBSvbN4JtpiXRSHxH2D5dddtn01AD9L4RPXIAEE8HERcD1ET4pgUkHRvMj9QHaiN+kEDQnxsybN69bzwSxReJj6By/79KufNbH/6aJdy/heVXCTz1ipnExZhpnT5kyZRyE+wO+MKF+oGkehMVBV1xxhTev0HTywiEPpfqzMdFxODCAypbfWOTZrrl1AxXlFgg3H9eWOo28O8p5cCRE2YB4gLb3U1fcT+rjEwjCGxHvA3n58D3y4ne83ob8OHtN/dWJSWomYS9KYRKWdLFQwx0TqOJPwMDr4rxSow0fhjYcZDU63DHJw6LMe8HERS0Ek+42vydI61B07knDmFEgfZxLfqrPRnjDy/mhwMq/p6eHR8cVSdOX1jh03L+EcDkYwrDyviYng34PZTHrwUSdooMgPfzQMO65QpDJgc6lUYfzcz6H/IDZuSjjf9F4RluHYpASi5p2qALy+jA6tyAhyASR1xmgMVMIIlitmMQKUuzBCEFqDEIxKZZFcGjBxIVKMBFMXARcnyA+0etzQR2tm0eYDyWyTx3K2BA2jeYdRqE/FOhmJz+L9PtDlPfNwsRWESOHQvhgNH01ML22AGU7IezxWgja8UgHhSBVxJG/oYUGMhCCF9jhs9yg6e79998/qYqNRWkKk1gmYQ+x7QB2lCxM7HB1uQUTF0nBRDBxEXB9ivBJZ9OCiMTQ6o+znqRFJtcF0ZmaztUtypDw+RRmPsEdfgjFeZhg9tEbfWg4JDknDKxtj0O9LnNepHucNnHixM2Sr6mWpTqUhyp4+OSrCL99Mk7K8xrE/zw2OWuB6gRrGhMnw3QPJQT58WWU3RkA5WCSnmqJN4KJC5pgIpi4CLg+Rfmk0N4zN7tsHxKTd8ID1gSeRyoP+lJC57kR0tgh8e5pPC9N+KlHhH3G51/VD+lOwVrYCtx/kZbWyJEjX5w+ffqitPfaH2nkYlL1hIdLL730GczYpiLPWTrfnDuF4Gm4jNFLmjqU6dBAhmFRFj7m/lCPZ2Om+lBawDYwScvb9icdfEb5aMzlPW2praUEwcSumX63YCKYuAi4PmX4pDFBSGL2868JqvUvWodGs55foyi8nN+XvvSlA7He95+JF+dgnenChF/jjyjP6ciEl/f32muvjYDweQEvr1x//fVPv/jii1cnAxbAJEzCJDOwnoHR/0OH/gnkGbSGhwHJMdhKMwNbWv7CWU9Oh38R0qUhU+4PQvABhP1OWsA2MUmjIfJXAxSUXZ22ZIelOjQAEztKJbdg4sInmAgmLgKuT1k+0WuEbooVfEgMZ4JUh2rrUCant0jA2dppBhWKUSYqZ1anrl69+haetGInMBiYQEV6DGiglW3Ib+R66633Hapls7aN0EAGZQkSrhCCaxH2KAhlr7HRYGDiA4J08MQYCkGeLGSHIc/mYWKHr+oWTFwEBRPBxEXA9anCJ7XPCEkM1wRBZmyfIMkeM2ZMH0fWtIREJ1lp1oPZ1y5I4yoXjhEjMDsb6fNv0e8dt9xyyynI7xzm2RYmyfJBRboYs8JTkf9lyXcpzx/EdpZ9sSZ4M8lOhilqIIP434cQnJtMh8+DhYmHFqUO1UIwuY7Nk4UwMFDrpCTbE782L8HEhVIwEUxcBFyfqnxStyBUBzTTyMCeCZJsqpeizfI+wwu3ZDk+UOVtAEObt+UEG7TXqJiPInMKwtYw8RUWa3MzMIv7OOjZ1/c+6YfBxXkQhBNpIJN8h+ciBjKPYIZ5licNeg0qJhZN2jpUHaoAIWhecSZIIZijIjbha3AIJi6Igolg4iLg+lTmk7oEodpzFRnGdPB7gjatFIJ41upQXwdrB19X3G/jKGWwMeHMGzO5owHqn3FtGADu7jgH9PMI90M7bBEDGeTJ+j7Ks046lPjEWIf6DlqnEKTFbIRBkzwrmNiM1u8WTAQTFwHXpzY+qUUQosMfkaYOpRDkGsutt95aWR3q4jB0fYDJ+kMEkw5sln8MgoyGPueGIIaZ9jdPPfXUq8455xx7fTHYQAZ5XIQ8/yeZV118ghnuVqBxHLQCWyJNXqvhfg5ros9hJvfkueee+3Iy78SzakDYO9kVaS9iAzctBHn4PAcSibi1PtaFSRGiMDAaCawmwBBte8TbBmVcA/x45uxDOHJw5bqESUVeaYRPoG1Zb+nSpdutWbNmO+C+LXh5Oepj0ahRo/52wQUXDKglrEodDD7R2fswXLx48QtYRnl++fLlT0OTYk7fGq5tpw5BmGkdCjB7ygpBMAi/ZhA75gSM42UUXWkF7jxb9WUw2MYF4hQKmnZsGhIpjUkhAqCCZAPiZnmoKS9961vf+jHE3zsgjXFgcKpBv8Kw6DgPRTqhBjKPIezXPHmU5pPjjjtuDDrtDyLN9yHt/cEXb2D6uJts6Oa1Fj8I/Xvw4maEvQlrlH80gfodBpPkiTFUh06YMKGXp+wwKKyWP8L8EvGdR3Ri3+Z6rPPC44FO5WykafZsglYK4Y5NN92Ul4qB98+tWLHiLDx4+QRpfLQMXSeccMIbsH4+FXGPAp6jNXl4HoFnPv4KmHwaA1f9Shm42Zg0PTBgxlnW5njtxUQTXCOvBPNJCCY0nrv55psPAJ2fefrpp9meNiDNmoeJPyy2uXVnMd79rLu7exZ46uGoXKrt7LTTTvuARz6j+YTvorz7Nttssz7wBePfDv79WRSv1C0EQ/C8Shv5rsV1L8pxKzzY3m7nixBMVALl/0r3J74sqwhCxShkWnQgNIyJjar1mmC0RaLUqBoVSqONrXyEF/VDxSwDg12L+6+x5+++d7/73UugtuQ38UY99dRT2yG9/XAdiveTEa4KLoa0tHXSKpiYxPMdyhISQlBvB+ijjEDZ56B8IVsfTkbDmgEGfxZZhW5XQdJ9R6MxmBEi4pbmk+OPP35z0HwSOokTke7Y/CIrIxzW3duj6+sQ4vejTr+7zTbbXMN1T1qHWpiYJLXmAt9J5CyQ/lQpvxP3Y02gFAcwugKvggQhynEkrgnJpKI8lTcGe49hPf20ND5BfoXpQl2egBkI67G/B0sSgGfQ5bRhanNsTDzRavVC2TrL9Cd18woGsVx30m3HlNHHJ+ZligMDqn3wmbIf4fVbUoIY74g3TgXfn0qhBoFz7Pz58x8lJmgHu+D5Cyaw5UA8/cQ6LCUIK2C4D/h3H+T7FdCs2hvaGtvbwEhVU1f9Xro/yco6ZiqeFTD5jsBT9QfG8J4YwwYUNeRk1FafUUGv4PoO6H0DOujPQ7hed8kllzxFIUhCUFmvwn8hritxfQid0G4If23dRHK20RYmKKta/6IlJNVcuix4/gs6mm/r55z7KKTzXYQpYiADCK/gyND8yvIJGtQUzF4WIf7puIKEoMk07tgD8a/CYGce0pyUxEQHhX8fv8UJfmCPYnoV/b7te51tBwL9G6j3i1GGVCHoK99gYIK2V7g/aYJXYOG+p912ND5FMOEsENifC+zvQPxcIajz0Hfw7bshEOdiJjg1ac2sw9R1bwJDDAAm1UWfTgeYNCJ3ys58tBGIs0VCj5iqno7CgmOK/o8YBX1Tg2DfAUiISnMR4hwC4UdDkaDODZ8I+gvCfgQj6M+BgWfAvR6uSr86MQklRH9eCsI3FuXRRx89Z+edd6ZqZmLshecBYT4O79c8rxwvdGCLoX49NfGiMJ+g8YxFnf8IeR+aSKvq4y5I8w58beXLCxYsuMxOLOsUHTtcm27gGcSveTShI/4UwpyWFy75frAwQZszAzfSlNV2muQVaE5uHzt27JfRfgyvFMGEQhCzwB+jCJ8C3yXhLfI8CoHPwXrcFsDm0SIRQ8I2iSHa8Z0QsNPQ/3IQVsevcH8SmmnhGSEqlZ9uogD1CkFukcCaIGdblWqfBUBeW+CiqtJ3Uf2V9XsYs7u9ighBOzFsO5iFzugAXKtt/6JuNuQ6McnJn+rQLq4J+r6xyIaMulkN9cpRKJdaB8tJj6+D9mQC6y/CSvRFnV4ZPkGnvQUaz11Io24hqMnioOZ8dCrq7FjWTZHOTSdS9k5MysYtEw/5bYN4hTqhtjHJKldW22mTV8pggvXAWSgbByG1/NBeT0W7PbqWxKJE2sAQPHgRhOElVeku058UybPQjJDEUB1KlUFyi0Sbqr+8AoJplmH0dCiEGadDMYGMSnk3/A5DWXbDfRNcS9CJ34HwV0GttxDP5gchejvCk/l+YjwLOFrGRG8HUPWTnAlSpYN60+tf96NcVHt+vUBxUoMC7x9jJn2jDlCGT6ZOnbrBqlWrrkPcnXQ6Td1B77EQhk/OnTv3exYmMT6pO2+NSd3pZqWHPC/Ce2OYkxVWv0vwSaOY6Dx996y20zavbLLJJovBJ98D35DUXEwgYI4G9kf4ylXRb++K8U30NjEEFscDk7+jf2WfU/in206Tcid4RkhiuCdOW0LaOmuOmPDcY6lDc5mlMBoFIkConfjDH/6QagRDB9SsE9D505JwNq4T8W4/XP+A60CE/xaY/BG8vxBrRDFVKIThv+PdfyBc4V+LmKgFZKpD0YE4M3XOenhsGrcDoBAKExiPUOU8r3ChEhGAzTMbbrghD/hWv7J8AiH4Y8TlgnsrP9D9DcycD7ExaSpjG5Om8khJ940p/l5vYNKX5BNvwIY98/qTtnkFA+Vvbb755tRSmP4kDQL2M8DxvLT3Q8W/bQxR7m9juemjRctvtx0M7mPrx3l8UiSvIEFIYmgdioSdTpbEtKj6CynbXIw8foqAhmmhB98Zi873oBzvTUsA77pxTYFp800nnnhizKoSjP11XIVVpHWpiNNojvzVTFCrQ5MDFK36o/GFjQkE/ho8fx6XMhqK0ipzOx57n3jYuLI6LMMnqJ8Dgf1hZTKvEIfD+wunTZsWcshA6WxQrtS2UzrR5iL2Jfmkuaz8KUPo9GX1J4PFK6jHH4BXzJYTP/UjuH3nuwgbYr+QlkTj/kMdQw1AVtupW+7kCkISw5kgMo4doE1iSQxu+sQYTf9g38/jyFYTgQ5/FNadfovncdov647y7huZmZtgULE+gYfUTzCZgAkH6Ej41P/I7QD8dl7aYdFa9WdjoqnAgGEO3DSpL/u7BjPmXzFyWT5B/XSjfs4vSMA8lOcKxJmC+/txcaR5Ou4/x10J5ZD0QPN2L7744r+FhC0TJguTMulVjQN8uMfzYlyfx3UINCH7we9g3LlmfCmuBbhM26maX5n4oCvV2nyo8wr2aW6LMnOvbvAPeL+I63qU+wzcD0Zb+Bzcl+C6OziRAgFLYjgfWVwJPuEe1A+AzsbbG/JpVe5krhGSGK4JAgTvTBBT1aF2YgwtHG+w+QIzvBPwXHTd6RhM46dDANrf0Ps10vm0nXaAu7FOhXXD/LnXibpz1EVM6vKEh5DDokePHn0a1CQHI703B5THBEFjeB4XsVVCsCyfoH6ORBK7mIQzHMhvNeg8DfvaeB5qco/Sr4gJ9kKNw6icQvKgjKTMK6Q5DSPkC7DGucx41uAgLWmY1JB84SRAz7eWLVv2TaqX7MhabQ48Z8G/MX6188xyY0aYeppPXbwCLNT2qCOOOGI78P+loKcWXsF2ny8hrSDjMmIA3rsJtPwL+HmJXnKiP34c0I3AOjYHeZfDSaOnWn5FMYTwO+uhhx66EJODtVSbg0/II7zUABiq4K3R3mbg+eAQAkPaW1bb4eSrCbmTJQiVSidrJlj2xJgQwEqGuQeznBV2XIB6lP0c4kacTlyfQ9hpOjyef6fdQ+CuOlnfFgkaGVAIhh4WzSOdIoOg21CumDDNKicYegoGCtxsX5VPDs/Kx3q3HO53on45OvX9FCZz5sx5Hhazh6MT4Wwxd3Ee9boBrg8hwZ/4Ei3pl4pJyfSqRHsNs4zjFi5c+O9IxAjBonxShYAa49bBK3bbWQraauMV8FEofRSCp6D9XLAflpwwQOlODlDY4eN0pxvGjx+/G06coWDcvyYcQ2lcjs/J7bdkyZKHIQRT+xN+BBx0HQJjmFNwPzePxoD2ltp2tAayCbljGoZVABpedGBzsWN4wcZDYqjDxz6Z1JGblVarTjDXYjtDmgfj+S22X6gbab3HCsvzOl/E80uW32A51YkxWgjaRLBuaKDDkRvo56ugET6tYxH+UjutLDfCXo9G/NOqfIK12I2Rxnuz8tLvMFM4DXTO18+Ju4MJ9kv+AGHuTITzPoKGD3tfFPfMbTvFk6wWAyP6syMhaBIqyycmgWIOhQno8PU1wSnVxCsOn5CAOnglUosGaZ7Qfn6mhWDSuC3Zx3L9HR/6/hTIjG8IDkZuIGARDBHrLAjg+aH9Cdrm+SjXHQO5pbtS2ltu22lS7jjMCSJTd+7r7wlGp14EdbLpcOS+CZ6dWClxdGL/qqgUTFyNCSo6mb6dVxtuMssImLgrdWgyQ5q+o5Prs9QXySCpz9gM/9XUl4kXwOErGhPkF7PkYtBQPsFI8/0Ivl4ieecR+f3pve9972XOi36PVEyQ/skIkmsMhLJMxqApWKWVQgdVxLltJy1ug/5XJ9OuwifJtPKeNSaowzLt2SRfA6+k8gkzweznJNxK8wrUovbA2dCddACGZ3CO6IlUm4e2HezPfQ7xjkumVfQ5FEOkez9UqFcU4RPQR3lwPK5SGGo+CcWkaNnzwicFoZqWJvTVKg0QqAxj8FHdtVGh89Ku8l4xLXTPtFQt8htjB8YG1Jft54LulVF4gwkqK5Z+wfSqBleYaOvQpCpFW4eW3Q4A83Bd3lw60ZBfpkqnBj55Y25mCADc/0MfiZcIn4nJgw8+eD/CP5CI43vcEJ5m4OMLEOBn+CRZN3bbCUinziBz//rXvz5uJ1iVT+y0AtwGE9RhJUGIvKrwSiafEJNZs2bNRR5VeOV/B+BBleiP0G5WFG07mHH9AnGfCMkjI0wohjyS8DUurxTpT0Aj8SuDoeGTrLbTpNwxgpCM6lOHElQ2ZD0txWPjM0HMaNSHFpGXoY905P1QBlptmd+4ceP+DvCCO3gTsd/xiI3Jq6++yvXUrRJh2nqMbZGwM2XdaIOHyL/p+hkBdYmjNmfeJfgkVPg8Ypc5codi4ovrJAfV69aOZwGPIdJ2khQbw7G2+cRuO8nOLUlk4HNZXgnlE5JRmldQ3i1DyoHB/Z+S6lDGC2k7yOO+kDwywgRhiDXlh7m8EqVTtD8phGEWn4RgklHWQq+UoCExaVN16qzByKkmzYVyCwhMIcjtALSEDAgeCwKht7vtgbTW4Pk/bb9QNzC51sYElcIRX9EZamh2meGyMKH6Alipw6KbHDHZBEJoOOrQknwSJHwws3/Yzp/uApg4cZNp8RnYBdHii4v1L6+aqyQmvizK+j2tI7bJJ1n9iaanxD2ofpK8UoBP+sADpXkFcUMF4QP2Xl/iEMonyKOqIAzCEPnMB27Eo6gQZDsKxjCLT0IxKcFH3ijcRN7YQabeHFM8SQdfQQXWDRDUdgD7u1sp0WLeSGM7WEDuiSn6vfoF+uyz0VHxSLXg2SUq87G//e1vV9nWXGhgQSbWOt867j5M7HS1mgv00rsw09ppFXFjxBgbpHDkxsGSdbJQUHIo3wYhAZEfDZXUrwQmK3TcrDt4JIgWXxqgb4PkrCcNE9Cfuybqy6OMH+hSZW+TT1g/3HcMep0tV2XKoOMU5ZUSfMKsqvBK0CCZgyZdJt7T+MQOo90oU6U2Hooh+jpaaJfNKxjDND4pgonGpuxd80kn13qQiMO0JCbrhIeyGafEU42H61/sUJIjppQ4ad6xM/6wP+zPEBSnpwVO+iPsaix8f5YGH7pzw0I6vDs+ngzb8HMqJqybNju3rHJW4RNguiQrbf0Oa5I7Re4ymOys08m6+2iBX1BngHBcYzS/HEzGmoANO0DX8jb5hJ1KWn8SimUaJIhfhFfK8AmzLs0riLs0jXbbHxage+jnHD7RwcwdGEwyD8UdFMDPhkSz2ltI8GSYIAwxSKMV7JCRO86JMZySsoJAZCsnxrDx8HQUqG6c01EwmwvqiBI1cSw2VW9v+8FU+Ww8n4krL73l0OEfis7jbi0Emc7uu+9+JG5BFczwVX9ZmGiVQdEtElVpSsaviU+eTqbre0ajeUsFTN7iSzPph5G6zyI4dH1ZCcI8TGCZynCtzQhRpmVt8QnrhyN89B2xE6gsTJIHICSrIO85mFfS+pOAtlOaV1D+oO0NCDfRwqRQH4u4pQQh64aY4O7jcQd3tjfHM9wjKC76WXSxA4c7lMUknKx4SI2JljsxpmVQbfpOa6FoFJcnPOI5lHjSxFScCaqcUcD1MaO7iAW1ScFG7LMw5X8HyvRfuNba7+BeAb/LIXh3w5Fbt9gVtPXWW2+JtM5IhG/8MQ0T1s9Q+IBsTXwS1LkB/3cQ8KKYRILnbYGV5aPlhZC44JtdGS4PE/BfUCcRkmdIGIzsV3OtB2GbbsPKxgD5OCN8jUnUl4SQnRbGVz9OWPJKUT5hIlV5BeV7yiHG4wFe+ewWW2yxCWjsK9LH4qSrDyO5N3iSDPIiJqBxSUhg3d5CwtphimC4YsWKGC2aT4pgYuddxm3zSWzdTE/VuUUCCTfeeNBIOzGKHMlv59lCkKMDqnTANEmBFVreg8E4ZyYDQ016F9YPD0T6XNjeB9dkMAdVFVvCVPik559/PjZK2XjjjUfiugrvY9aoeG7qRwu3TExwkMFrCMPRddP1k1pGdOjqYOSqfIJ0ZqdmEn/xWdTnpDQ+ScMEdft1NOrN40l5n+5PnkjEUIgfJAiRx14hbQcztEO9uTfkifbTihDk90nTtgNweSXik0qlLMIre+yxx0Rff5LGJySsKq8gidsDC7gNvtZyXpG2AwGzBXjs8sD07WCx/gSzsD/YLzPcn8XRaUaFmxEu9qoAhg/A/sKs+4e0nVhG1R5imGg+MYKQxCB9NVXHvelOVpk00zrU911DHhFGS8gq5QXjnAYGOs2XxoUXXrgcHd8cXL+D2vTPqBTmF/vGIr5OvTE+VXQN4qvZiC+dmv0ax6QuesHwtfAJjmd6GGn9JYCuLtTnRXY4Dpay+ATnh+6MOKfYcdLcoEGdPZl8D8EVJAghcN4FejbK+nIDBi80TDssmUeJ55imo0T8uqJwb55SuSXbDjOouz8pwisQmhfqQubxCcPVwSvsR5BUqHr0CBj1naBpzLqffPLJm4I/fwKsi27dcvoTzIDY1hZk5Re964LQnM76DQirghTBEBF+q9Otm090uil3BxMdTglCEoOpUG9Lx6apza08Igz5OoYxunOLNnJqOsvcWYnfAMNdw4NhfQmgotVJIHinVDp6dICGvSdom433k33xGvALxgSNoulBSm7xcAqNVpvnhs0LAIy9QsgT7+04P/S322+//XZ8Z/NJEhM0Sn5j8mYEC1qPS6MBHerzHjocL8TffNSoUVcm6dAB0a66sEH5p3jeRfuVvCs+QX5mAFsyncrRQIO37TDhpvqTtHryFMbwShafMF5dvMK6x/XfHlq8XigLv9x+3ZQpU8Z5A8AT7w/AgfgPIuwH0sKk+Kf2J2gXv0mJk/R+B7QwN+JYtvHJF8nnohjC+FDR0BSfJOmLnlMx4ftuEoN7TxMHmUYE2DdFjHU6innHkRv1xKBDqUPTOhUTIdABJjoMo5sDMTvkKHEmZoELo6jKwg3lV+uktAyFGmdv5HsCro8GJl9HsNYxqYPoutIYOXLk5VjTPRnpjcpLE/VyAKzu7ttpp53OwvrXrfj01CMYML3KeFQfQXDtioX+z+A6Ki8t/R5p3gJ1+YP62b7jXZAlYBTnUNBwMej7Bo/Eoh9pwmxxn5tvvvloPB4ShSt7M3wyceJEkBY8WC+bX1a8WNuxAzbZn5ThFahtTwev3I4Z+SO4GuMVYNABvrsE9R38hRr0TR/CQR0PQ+DdCvc9qFN+M3Uz3PfCfR9c72S6Nr4hbsTjV2m6uZSACY6Jwj4WvyvgcSKu3PaGsJPRLz4EPlYYsr1VxRBp3rZo0aJ5TfKJKfCAw7QdHyaUO93U4bd1digtl1BBnSRmgMaBEeTee+9d20zDTh/u0bi+xguVOg/3e3Fx8f0ljJDGYb/idmC+d+E5dXSGd038lGppkDCxy1O4sdmRq7inT5++CCPP81EPXw1MZxOEOx+DmxE4D7EX9fk46m4jNLCt0BEFJtEfDPF60XFNTYnUgYb/GGZyy/F+05QwSe8TcJ7jcaDpKaS9BjTtiHIlw5R5TuWTMolViKM6FG6RQKfKTjbGN+zcmuxPSvLKDxrmFYPJk08++Sd8LeL3qPvgL0WAR8aiPj7CC25VNfpesp7UqVxpfeyECRMee+aZZ4q2t9owRF38n6b5JIFbatshHdSEUu50RkIwEbfeR1Ss0s3SSofrCcnU4W9bQiZf1/28KxL8LK4v4zobHeFJYFzOAFsVgkMJEzY8GDs49QJMWvmhDr6DjIKsAm2CQDdVhG/Evej6iU7mSu4z1Q/2nZjwB974o+2f545oGo/7jnlhQ94jncy2E5JGXWGIiX3akp0uZxvsVJruT4YaryQxwWDsLPBMWSM/G9JSbvJLXh87WBhiUDgTg9cH2+ATgpfXdmy5041Ka2XNSX82CCDEKrjNDb+xjIfAwxDBRO3/gtFSNwyEBgUVGEKsxDrDR9BQbgHzbtASEVRDpc4G9akXwOR60POhlmjyZpPGJ97AzXkqPkHyzhYJPbIuerJQGVKHGK84mKBzvReq+zNQNu5dbv3Hzh99bGwNOdnHDhKG9+L7iqfADqOnDT7RwKe1nSQmMcB05BrvnJZ2aX21nS4bT5IY+z3dGLn8Lek3jJ7TaK+ESZ3lZ6Oh6TvS7AKTNs0LWaR3LF269B6Mpo/AwKwWXWJWZni3EGt5H8R68apkOBsTdihQI/0UND2ZDFf2GWktC4ybyidtDV41nUlMtD/vWs2FtX3qpZseVCtM7r777vvAK58ZTF7JwgRW6OeCtt/YOA2GO6uP5VYy0PTJljB8DCrRQ6A2f6lNPikid5rs/JRKB51J6lQdU3iuCZJHvA3oPe95zyN4/+JgMFENec7xpFEZE0+apbzYkKnmoholuYWlVILlIxlMsMn2eswK2cGFnuhSOFek/SdYvR6gDVrsBHyY4ICF1+D/TTtcWTfyZuczPSQ+jBLUebs+NVdI/LrC+DDRabelDo3yM3xCTPB5qUHjlRBMsPXqcND9E41V1Tt45/8WTYOqv6w+FkZiv0G6FIaNtTfQfD+E4D+DlmebVptH+MT4JIlZGiZNCUK1gMxpabSoHsuHM0Ee/RRtkfAKQRYA6imOMr+SLMwweKZ12ukJOmvBJJFmqUc2ZKr+9CZovW2kVGLVIjmY4Gvhv0TDeTuS9a7dVckODf5SCMG3X3LJJY8n08nCBCP8mQh/WTJOkWfkvZidDuK8FhIvq+0gnTZmzWqNJY1PONtIqLlS23FIeXPCOHzC8JpXgMcDOfELv0aaXl7J4hMbEwxk1kDj8BlkfDrSqrpm+DVox74fUgidV2gfi/2PV8OqdlITGAKrGWg7/wThs9BSh7bOJxq3LExiAkpHqHhXEllPS+1O1p6qc3SAfHJBQUWxA/ourmImgRULUTY6GOp5XJ9AI3jESqNWTKx0CzvZkGn1h4jOWk/hxKpFSMUEs7AFaEDvROP/V2BZh1pyNtLZHyPg4zETXJ0kOwsT8iwtIffff/8TkcaFuAoLIcS5A/sM/xG8/EQy77RnWv2ltZ20OHX6h2DSlpqLs+O0/uT+++//C2Zf+7TBK2UwQT/wTQgaGuj9skT9LALvsC/5doG4y/SSU2gfC2vcBTVjeDtU1wdiueUktJ1VQ4FP8jDh+lCtP26RgPSn6Tk3y5u0tRoFU3VuPKV/rhBkIIRluK9gr81VcE8GM5KpRvJd3o+MizBp1pClvreVkedLoG8u3l+LDi92ynvdmGTQkPmKeHCEj87dOWMWW0gKd/CZmeW8zMMENL4CHC9EJzgdlmZHgvYjkSRnikGb5BGOVlm/Q51cAgH4P3B7f1mYUAgikj4YmXw4FVs9foE7O6Z3IW7mQBJ5z0e4H6CTmcUZAtylfsm2g+0ZpdIJjVQAk9AkS4fL4xP2J8CWM64LmuSVKphQ0IC+w9CHvQ088WGo//lJt4m4fH0TNQZ/hGD/FfZN/lAP3CBYRsM/9wc6l2WpQ9MSAHbUYlXCEHnfDG3O5Y8//vicRNtJy7Y2/xA+AfbMzyt3fBVRijgyCiNy5IaK8FouUR0KwEmIl5hSGXsikRauf2nVnx2EFYR1y16OUgBM43Qwb8FkoAaq8AkEwIboIP4JHck+SHEc6o9nxvIcxjVwU/A9B/ciuG+FEOXntzLrtwqf8EQQbDY+GLS8BfnwYPatcGcbWAj3X3GfCwF8Wx4NCKdUkLwLnxCF/h/rhq6ymNTJK1X4JCqOc5s6depm2Ew/HvxDPub2n1VwPwUjroch/By7CAzAjsT7HzsJJTyQ1tHgux/BO5P3E9G8j4EY/h17Z29/4oknzKEUw7GPrUsQKjWXNlW1UeVolscc4VDrtf39RPUKstP3uDvwdQZ1fFvSjFiPUto6Sg5Cv0MwidWQ8EkMDvUgmAgmLgIJH8wmvwuB/G8Jb+cRA8WDMAjklp8mf+tcH5up2glEkibNnb4On4KHC+otfRONC+qqgpIGOhTGpIVrPW0JQcHE4R7hEweSEYLJOoyJntW6RSzmE6XzvpBYEIQ8Naup3zrbx1adESprriz1BfXVedahddQamSVLHUqz2WhWWlllkEOvYOICJJgIJi4Crs86xSdQZ56NfmkXaMJOwiytrNFXB2aDX0Q6IVbLC2FY8yYX1np81uU+toqxjGHa5EGmhF1b6bSlDqUlJGZ9jhGIVofi+1/cs9iaEBRMTOMTPjFQGIdgYqAwjnUKE8iuPbGmR1UmPyF2AJ6/BqOp6VguKWKU1oF1uglI43sGpQwH+rc7Ml5XfaWszdfVPrasajRm+m4jTMGj92tE/o0KH45SuCaYVIcyb9Ki1aF4bJQOpC+YEPT4TzCJ48EnwWQdxwSfLlof/dIsFJPblPjjofAX4QD3uyDY/qHfK/sfArPzC1/4wpcg3O5H3I2zQ/e/RbhG1gaR7jrfx5ZRjaqGjC0SXjN8CsGhoA7luiDXJyPrUHJKk4JQMHFbqmAimLgIuD7rHJ9A2PEQ+S+7Re33gXB7AMLlWnw27BZsi3gKvk+PHj26D9aXO2L7wZvw7k0I80ncJ6Wl4fFfhL2ub4wOIfG8LudFIZi25LQu9bGFBCFBIZyY5ncvXrzY+ahum+pQ0sI9cSDH2RjOmSC3SPA0A7xvUgAa03fBhJzR/xM+0UgM3AWTASy0a13EBOuCe0EleifKqGeDurhN36difZDfXK3t93rqY4uoRpXgoWHMggULUk+9wEiGFdG48Ek7HUWrQ9s6zYDCWDCJtT3hkxgc6kEweR1gQpUohOAsFLVtIbgIp9dc6UJc3odC8PXUxwZVGEGZP39+JywvO3liDKbvZibJ6TEOS+6dPHlyL4Aj8o0LQQofqBJiM0HSgU/mMO8enOSgT68pzwk5MQUTFyDBRDBxEXB91lU+2WOPPXji0KFuiRv1WQMheCA+rfREXbmwfl5vfWyI1agSeloI2ucfEnhulsc+wd42T4xBtjEhSDrGjBnTxy0SbVmHMk/BhCiYn/CJgcI4BBMDhXGsk5hgXXBvlPAUU8r2HP8KIXhPXdlRCHJNEOm9rvrYPEGoFrKh+uuKDgE2eHMG1vaJMWn7FbU6FJvl6z4/1JTXcggmFhiRUzARTFwEXJ91mU8WYlnoZxAkR7jFbsSHZ5JOwbpgyP7CUALU4ea+IzLX9T42SxAqpqV1qO8AbQpBWodGCDepDlX7iyLDGOfbeawg0GAfjBxa6WXCCSYuaoKJYOIi4Pqs03wCgbQURT4SRnM/xv1yCMQdXQjq8YHAfQbXYdikX9e+wdd9H5smCPXm1q5odKDUGboatRDkiTGokCaFIK0yR6RN1SkEowO0G6cDZRdMNAMM3AWTASy0SzDRSAzcXzeY4MDr3+FA7d1WrVr1L+i7Pg0I3jEAQ2UXt1pciG9qzvAdzF02delj/Z8B0UzbTXWoDS7VoRMmTOhFZfOzJ/oTSXaQut1D5nBXMgtVs4KJqWLhEwOFcQgmBgrjeF1jgu0UO8CS9FOYMLwPiOyGfoRfmijyW4S4f8R109Zbb/1z2GKU/pxXSqbSxwKY5IxQHQKMDr8z2eHr2de2227L2RcxbXImqBoPzXchfLloG5uRan01PzwJWpqkg+UUTIhC/CeYxPHgk2AimDgIQH35BDxpTcprBLZYbInZ4u44HJuq002iayPc10dfthz3ZbhegPs5zPzuw8zv73hu4id9rIWqmfFhpALsOzrmzJmj1KGorJjwWbJkidoi0aZ16Msvv9yFrRmxvY6clXK7xsyZM7VAtopTr1MwcfEUTAQTFwHXR/jEj8ndd9/9yu23374Y/dgDOJTknhdeeOEPuH6P6yZ80+8WTELuOO+88+bC+v0RhHW+S+imWs6H9cMlJ+lj+/Ezwo7A+FR/DNbmiTHITo2scXfMd/WstI0TY1huwYQoxH+CSRwPPgkmgomLgOsjfDJ0MeFsi4KnyycEKXjaFoKTJk2iutYrBHmAdlsnxggmDtMKnziQSNtxIRFMBBMPAq7XkOpPunjKOU+NGT9+fKdPHcotEti6wHW4RtfiOFriD6fWdK1cuZJ3M1ttUx2Kciozb8EkxrmCSQwO9SCYCCYuAq6P8MkwwKQDM7CR0T5BI3hIN2eC/LJ8W2uC0T5B70ywrQO0UWxj4SaYGO4VTAwUxiGYGCiMQzAxUBiHYGKgMI4hiQlNZ0cZEuGgOpRHlfFr7vTHJK3xmSCtQ5Gv96O6VIe2ZR0Kod9x3XXXqVN0BBOFgBrNCiY2N/RrDAQTwSSGgPsgbWcYYRIThFRB8ht+EEp93CyPcjQuBLNmgsi/B8emaevQRmnhmiC3aQCD2Jc1BBPBxG7Pwic2Gv1uwUQwcRFwfYYyn8S2JvDEGMwQe9sSgnknxkRCkAKwMSHItUlePECbs1LfoeKCySsxtbnwifAJuzlpO25nL5gMT0y6dthhh27OepYtW9ZLdWgbn1ICVB2zZ8/u4h4WzMJiwpiqWbzvueGGG1o5No0GOtOmTVMnxtjGQoKJYGI1abKJ8IkFCNuwYBIHRDBx8KDHsOATzoJ6qQ6lYQwoJuGNzb4ICkdMmGHpE2OMEKTgIS1cE7RmgqSnqZ/ar5i21iOYxI/XEz7xrx0LnwifRB2U9CduTz1sMFFWo9wi0aY6dN68ed2+mSCNdHCiAo10mhTGrC5juRQdKm4EMl/SYlYwic/UBZMdO5I8K5gIJuwv8JP+pB8H+39YYdJNdWgLM0HFLFnWoQjQw4/q4t6aEOR5qujc7Mpr/RQdwSQGPx86BBPBxEHA9RA+EUxcBFyfID7RM6FGhU+aOpQ02+pQPDZKB9JXJs1Zp+hQRUy68GuUFsGkH2T7XzCx0eh3CyaCiYuA6yN8Ug2TmDWgm1R1H1YQrUN96lC9NYHHprUwK1VCkB8ahvB19iwOFTWXYOKqzQUTwUT3RNKfaCQG7oLJABbaVRST5GeYdDq13ElM1j5BqCV7eYA2hGDjsy8WCF+PppEOhWBsAEAh2JaKWDBxWUswEUxcBFwf4RPBxEXA9SnDJ1o16qZW0YfEcK0HyXi3SLR5gDaFMdWhCxYsiG2Wp1q2bSEomMQZS/gkjgefBBPBxEXA9RE+qQ8T8z1CN8nyPqwgCp/Ro0fHhCBVXGPHjuXsr2f69Ok8Zbt8JgExSQcPz+ZmeZ4d6jvIe/Lkyb0QTkyt8VmpYBKvNOGTOB6KCaXtOKAInziQqMGS9CdxXKrwSe2qURKTdmLMmDFj+qItEq1slidMWgj6ToyBYUxvW4eKCyYu0womgkkcAfdJ+hPBxEXA9anKJ3ULQvVxX9/ePMs6lDPBRmdfgElbh6oDtCEEDXKclfKIsLbWBEkL1bKCiakCOgSTGBzqQTARTFwEXB/hkwYwqUsQqs2TnKqDxg5+T9CmlUIQzz1tfkWC1qHRp5QMKVoIcrN85NmkQBZMDPLGIZgYKIxDMDFQGIdgYqAwDsHEQGEctWFSiyDEtHREmpqLQpDWodgi0Yo6lLRgBtYVzcBiApkzQQpBnqLT9KxUMDHMahyCiYHCOAQTA4VxCCYGCuMQTAwUxlEnJnUIQmUdCoHn7M3TM8GWhaA6QNs+MYYzwQkTJvTOmDGjlW8soqYEE8OuxiGYGCiMQzAxUBiHYGKgMA7BxEBhHLViUmX7BKel3gO0SSqFYNsHaKedGMODkbfddlu1ab/hmaBgYvjUOAQTA4VxCCYGCuMQTAwUxiGYGCiMoxFM/j+QoT52LLsGvQAAAABJRU5ErkJggg==\",\n      \"attendance_barcode_source\": \"front\",\n      \"attendance_from_systray\": true,\n      \"attendance_kiosk_delay\": 10,\n      \"attendance_kiosk_key\": \"0d42c393-2a7c-4aac-99a1-dadadd4e9678\",\n      \"attendance_kiosk_mode\": \"barcode_manual\",\n      \"attendance_kiosk_url\": \"http://localhost:8069/hr_attendance/0d42c393-2a7c-4aac-99a1-dadadd4e9678\",\n      \"attendance_kiosk_use_pin\": false,\n      \"automatic_entry_default_journal_id\": false,\n      \"bank_account_code_prefix\": \"1014\",\n      \"bank_ids\": [],\n      \"bank_journal_ids\": [\n        6\n      ],\n      \"bounce_email\": \"\",\n      \"bounce_formatted\": \"\",\n      \"cash_account_code_prefix\": \"1015\",\n      \"catchall_email\": \"\",\n      \"catchall_formatted\": \"\",\n      \"chart_template\": \"generic_coa\",\n      \"child_ids\": [],\n      \"city\": \"\",\n      \"color\": 1,\n      \"company_details\": false,\n      \"company_expense_allowed_payment_method_line_ids\": [\n        3\n      ],\n      \"company_registry\": false,\n      \"country_code\": \"US\",\n      \"country_id\": [\n        233,\n        \"United States\"\n      ],\n      \"create_date\": \"2025-03-06 10:20:53\",\n      \"create_uid\": false,\n      \"currency_exchange_journal_id\": [\n        4,\n        \"Exchange Difference\"\n      ],\n      \"currency_id\": [\n        1,\n        \"USD\"\n      ],\n      \"default_cash_difference_expense_account_id\": [\n        36,\n        \"642000 Cash Difference Loss\"\n      ],\n      \"default_cash_difference_income_account_id\": [\n        26,\n        \"442000 Cash Difference Gain\"\n      ],\n      \"default_from_email\": false,\n      \"display_invoice_amount_total_words\": false,\n      \"display_name\": \"My Company\",\n      \"email\": \"<EMAIL>\",\n      \"email_formatted\": \"\\\"My Company\\\" <<EMAIL>>\",\n      \"email_primary_color\": \"#000000\",\n      \"email_secondary_color\": \"#875A7B\",\n      \"employee_properties_definition\": [],\n      \"expects_chart_of_accounts\": true,\n      \"expense_accrual_account_id\": false,\n      \"expense_currency_exchange_account_id\": [\n        35,\n        \"641000 Foreign Exchange Loss\"\n      ],\n      \"expense_journal_id\": [\n        2,\n        \"Vendor Bills\"\n      ],\n      \"expense_product_id\": [\n        6,\n        \"[EXP_GEN] Others\"\n      ],\n      \"external_report_layout_id\": false,\n      \"fiscal_position_ids\": [],\n      \"fiscalyear_last_day\": 31,\n      \"fiscalyear_last_month\": \"12\",\n      \"fiscalyear_lock_date\": false,\n      \"font\": \"Lato\",\n      \"has_message\": true,\n      \"hr_attendance_display_overtime\": true,\n      \"hr_attendance_overtime\": true,\n      \"hr_presence_control_email_amount\": 0,\n      \"hr_presence_control_ip_list\": false,\n      \"hr_presence_last_compute_date\": \"2025-03-14 01:02:24\",\n      \"iap_enrich_auto_done\": true,\n      \"id\": 1,\n      \"income_currency_exchange_account_id\": [\n        25,\n        \"441000 Foreign Exchange Gain\"\n      ],\n      \"incoterm_id\": false,\n      \"internal_project_id\": [\n        1,\n        \"Internal\"\n      ],\n      \"invoice_is_download\": true,\n      \"invoice_is_email\": true,\n      \"invoice_is_snailmail\": false,\n      \"invoice_is_ubl_cii\": false,\n      \"invoice_terms\": false,\n      \"invoice_terms_html\": false,\n      \"is_company_details_empty\": true,\n      \"layout_background\": \"Blank\",\n      \"layout_background_image\": false,\n      \"leave_timesheet_task_id\": [\n        3,\n        \"Time Off\"\n      ],\n      \"logo\": \"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\",\n      \"logo_web\": \"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\",\n      \"max_tax_lock_date\": \"1-01-01\",\n      \"message_attachment_count\": 0,\n      \"message_follower_ids\": [],\n      \"message_has_error\": false,\n      \"message_has_error_counter\": 0,\n      \"message_has_sms_error\": false,\n      \"message_ids\": [\n        1336\n      ],\n      \"message_is_follower\": false,\n      \"message_needaction\": false,\n      \"message_needaction_counter\": 0,\n      \"message_partner_ids\": [],\n      \"mobile\": \"False\",\n      \"multi_vat_foreign_country_ids\": [],\n      \"name\": \"My Company\",\n      \"nomenclature_id\": [\n        1,\n        \"Default Nomenclature\"\n      ],\n      \"overtime_company_threshold\": 15,\n      \"overtime_employee_threshold\": 15,\n      \"overtime_start_date\": \"2025-03-01\",\n      \"paperformat_id\": [\n        1,\n        \"A4\"\n      ],\n      \"parent_id\": false,\n      \"parent_ids\": [\n        1\n      ],\n      \"parent_path\": \"1/\",\n      \"partner_gid\": 0,\n      \"partner_id\": [\n        1,\n        \"My Company\"\n      ],\n      \"payment_onboarding_payment_method\": false,\n      \"period_lock_date\": false,\n      \"phone\": \"\",\n      \"primary_color\": false,\n      \"project_time_mode_id\": [\n        4,\n        \"Hours\"\n      ],\n      \"qr_code\": false,\n      \"quick_edit_mode\": false,\n      \"rating_ids\": [],\n      \"report_footer\": false,\n      \"report_header\": false,\n      \"resource_calendar_id\": [\n        1,\n        \"Standard 40 hours/week\"\n      ],\n      \"resource_calendar_ids\": [\n        1\n      ],\n      \"revenue_accrual_account_id\": false,\n      \"root_id\": [\n        1,\n        \"My Company\"\n      ],\n      \"secondary_color\": false,\n      \"sequence\": 0,\n      \"snailmail_color\": true,\n      \"snailmail_cover\": false,\n      \"snailmail_duplex\": false,\n      \"social_facebook\": false,\n      \"social_github\": false,\n      \"social_instagram\": false,\n      \"social_linkedin\": false,\n      \"social_tiktok\": false,\n      \"social_twitter\": false,\n      \"social_youtube\": false,\n      \"state_id\": false,\n      \"street\": \"45\",\n      \"street2\": false,\n      \"tax_calculation_rounding_method\": \"round_per_line\",\n      \"tax_cash_basis_journal_id\": [\n        5,\n        \"Cash Basis Taxes\"\n      ],\n      \"tax_exigibility\": false,\n      \"tax_lock_date\": false,\n      \"terms_type\": \"plain\",\n      \"timesheet_encode_uom_id\": [\n        4,\n        \"Hours\"\n      ],\n      \"transfer_account_code_prefix\": \"1017\",\n      \"transfer_account_id\": [\n        46,\n        \"101701 Liquidity Transfer\"\n      ],\n      \"user_ids\": [\n        2,\n        6\n      ],\n      \"uses_default_logo\": true,\n      \"vat\": false,\n      \"website\": false,\n      \"website_id\": [\n        1,\n        \"My Website\"\n      ],\n      \"website_message_ids\": [],\n      \"write_date\": \"2025-03-14 01:02:24\",\n      \"write_uid\": [\n        1,\n        \"OdooBot\"\n      ],\n      \"zip\": \"\"\n    }\n  ],\n  \"partner_id_expanded\": {\n    \"display_name\": \"My Company\",\n    \"id\": 1,\n    \"name\": \"My Company\",\n    \"property_account_payable_id\": [\n      14,\n      \"211000 Account Payable\"\n    ],\n    \"property_account_receivable_id\": [\n      6,\n      \"121000 Account Receivable\"\n    ]\n  },\n  \"project_time_mode_id_expanded\": {\n    \"category_id\": [\n      3,\n      \"Working Time\"\n    ],\n    \"display_name\": \"Hours\",\n    \"factor\": 8.0,\n    \"factor_inv\": 0.125,\n    \"id\": 4,\n    \"name\": \"Hours\",\n    \"rounding\": 0.01,\n    \"uom_type\": \"smaller\"\n  },\n  \"resource_calendar_id_expanded\": {\n    \"display_name\": \"Standard 40 hours/week\",\n    \"id\": 1,\n    \"name\": \"Standard 40 hours/week\",\n    \"tz\": \"Asia/Bangkok\"\n  },\n  \"resource_calendar_ids_expanded\": [\n    {\n      \"active\": true,\n      \"associated_leaves_count\": 0,\n      \"attendance_ids\": [\n        1,\n        2,\n        3,\n        4,\n        5,\n        6,\n        7,\n        8,\n        9,\n        10,\n        11,\n        12,\n        13,\n        14,\n        15\n      ],\n      \"company_id\": [\n        1,\n        \"My Company\"\n      ],\n      \"create_date\": \"2025-03-06 10:21:21\",\n      \"create_uid\": [\n        1,\n        \"OdooBot\"\n      ],\n      \"display_name\": \"Standard 40 hours/week\",\n      \"global_leave_ids\": [],\n      \"hours_per_day\": 8.0,\n      \"id\": 1,\n      \"leave_ids\": [],\n      \"name\": \"Standard 40 hours/week\",\n      \"two_weeks_calendar\": false,\n      \"two_weeks_explanation\": \"The current week (from 2025-03-10 to 2025-03-16) correspond to the  second one.\",\n      \"tz\": \"Asia/Bangkok\",\n      \"tz_offset\": \"+0700\",\n      \"write_date\": \"2025-03-06 10:21:21\",\n      \"write_uid\": [\n        1,\n        \"OdooBot\"\n      ]\n    }\n  ],\n  \"root_id_expanded\": {\n    \"account_opening_date\": \"2025-01-01\",\n    \"currency_id\": [\n      1,\n      \"USD\"\n    ],\n    \"display_name\": \"My Company\",\n    \"fiscalyear_last_day\": 31,\n    \"fiscalyear_last_month\": \"12\",\n    \"id\": 1,\n    \"layout_background\": \"Blank\",\n    \"name\": \"My Company\",\n    \"partner_id\": [\n      1,\n      \"My Company\"\n    ]\n  },\n  \"tax_cash_basis_journal_id_expanded\": {\n    \"code\": \"CABA\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"display_name\": \"Cash Basis Taxes\",\n    \"id\": 5,\n    \"invoice_reference_model\": \"odoo\",\n    \"invoice_reference_type\": \"invoice\",\n    \"name\": \"Cash Basis Taxes\",\n    \"type\": \"general\"\n  },\n  \"timesheet_encode_uom_id_expanded\": {\n    \"category_id\": [\n      3,\n      \"Working Time\"\n    ],\n    \"display_name\": \"Hours\",\n    \"factor\": 8.0,\n    \"factor_inv\": 0.125,\n    \"id\": 4,\n    \"name\": \"Hours\",\n    \"rounding\": 0.01,\n    \"uom_type\": \"smaller\"\n  },\n  \"transfer_account_id_expanded\": {\n    \"account_type\": \"asset_current\",\n    \"code\": \"101701\",\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"display_name\": \"101701 Liquidity Transfer\",\n    \"id\": 46,\n    \"name\": \"Liquidity Transfer\"\n  },\n  \"user_ids_expanded\": [\n    {\n      \"accesses_count\": 602,\n      \"action_id\": false,\n      \"active\": true,\n      \"active_lang_count\": 1,\n      \"active_partner\": true,\n      \"activity_calendar_event_id\": false,\n      \"activity_date_deadline\": false,\n      \"activity_exception_decoration\": false,\n      \"activity_exception_icon\": false,\n      \"activity_ids\": [],\n      \"activity_state\": false,\n      \"activity_summary\": false,\n      \"activity_type_icon\": false,\n      \"activity_type_id\": false,\n      \"activity_user_id\": false,\n      \"additional_info\": false,\n      \"additional_note\": false,\n      \"address_id\": [\n        1,\n        \"My Company\"\n      ],\n      \"allocation_count\": 0.0,\n      \"allocation_display\": \"0\",\n      \"allocation_remaining_display\": \"0\",\n      \"api_key_ids\": [],\n      \"attendance_manager_id\": false,\n      \"attendance_state\": \"checked_in\",\n      \"avatar_1024\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDI5OCwgNjMlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5VPC90ZXh0Pjwvc3ZnPg==\",\n      \"avatar_128\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDI5OCwgNjMlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5VPC90ZXh0Pjwvc3ZnPg==\",\n      \"avatar_1920\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDI5OCwgNjMlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5VPC90ZXh0Pjwvc3ZnPg==\",\n      \"avatar_256\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDI5OCwgNjMlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5VPC90ZXh0Pjwvc3ZnPg==\",\n      \"avatar_512\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDI5OCwgNjMlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5VPC90ZXh0Pjwvc3ZnPg==\",\n      \"bank_account_count\": 0,\n      \"bank_ids\": [],\n      \"barcode\": false,\n      \"birthday\": false,\n      \"calendar_last_notif_ack\": \"2025-03-06 11:01:40\",\n      \"can_edit\": true,\n      \"can_publish\": true,\n      \"category_id\": [\n        1\n      ],\n      \"category_ids\": [],\n      \"certificate\": \"bachelor\",\n      \"channel_ids\": [\n        1,\n        2,\n        4,\n        5\n      ],\n      \"child_ids\": [],\n      \"children\": 0,\n      \"city\": \"Khlong Tan\",\n      \"coach_id\": [\n        5,\n        \"mark\"\n      ],\n      \"color\": 0,\n      \"comment\": \"<p>abc123</p>\",\n      \"commercial_company_name\": false,\n      \"commercial_partner_id\": [\n        3,\n        \"Updated Partner via API Test **********\"\n      ],\n      \"companies_count\": 1,\n      \"company_id\": [\n        1,\n        \"My Company\"\n      ],\n      \"company_ids\": [\n        1\n      ],\n      \"company_name\": false,\n      \"company_registry\": false,\n      \"company_type\": \"person\",\n      \"complete_name\": \"Updated Partner via API Test **********\",\n      \"contact_address\": \"55 Soi Sukhumvit 26\\n\\nKhlong Tan\\nBangkok 10110\\nThailand\",\n      \"contact_address_inline\": \"55 Soi Sukhumvit 26, Khlong Tan, Bangkok 10110, Thailand\",\n      \"contract_ids\": [],\n      \"country_code\": \"TH\",\n      \"country_id\": [\n        217,\n        \"Thailand\"\n      ],\n      \"country_of_birth\": false,\n      \"create_date\": \"2025-03-06 10:20:54\",\n      \"create_employee\": false,\n      \"create_employee_id\": false,\n      \"create_uid\": [\n        1,\n        \"OdooBot\"\n      ],\n      \"credit\": 0.0,\n      \"credit_limit\": 0.0,\n      \"credit_to_invoice\": 0.0,\n      \"currency_id\": [\n        1,\n        \"USD\"\n      ],\n      \"current_leave_state\": false,\n      \"customer_rank\": 0,\n      \"date\": false,\n      \"days_sales_outstanding\": 0.0,\n      \"debit\": 0.0,\n      \"debit_limit\": 0.0,\n      \"department_id\": [\n        1,\n        \"Administration\"\n      ],\n      \"display_extra_hours\": true,\n      \"display_name\": \"Updated Partner via API Test **********\",\n      \"duplicated_bank_account_partners_count\": 0,\n      \"email\": \"<EMAIL>\",\n      \"email_formatted\": \"\\\"Updated Partner via API Test **********\\\" <<EMAIL>>\",\n      \"email_normalized\": \"<EMAIL>\",\n      \"emergency_contact\": \"\",\n      \"emergency_phone\": \"\",\n      \"employee\": false,\n      \"employee_bank_account_id\": false,\n      \"employee_cars_count\": 1,\n      \"employee_count\": 1,\n      \"employee_country_id\": false,\n      \"employee_id\": [\n        1,\n        \"Administrator\"\n      ],\n      \"employee_ids\": [\n        1\n      ],\n      \"employee_parent_id\": [\n        5,\n        \"mark\"\n      ],\n      \"employee_resource_calendar_id\": [\n        1,\n        \"Standard 40 hours/week\"\n      ],\n      \"employee_skill_ids\": [],\n      \"employee_type\": \"employee\",\n      \"employees_count\": 1,\n      \"expense_manager_id\": [\n        6,\n        \"mark\"\n      ],\n      \"fiscal_country_codes\": \"US\",\n      \"function\": \"CEO CFO\",\n      \"gender\": \"male\",\n      \"groups_count\": 41,\n      \"groups_id\": [\n        2,\n        8,\n        25,\n        34,\n        17,\n        61,\n        52,\n        28,\n        19,\n        31,\n        51,\n        42,\n        44,\n        3,\n        9,\n        125,\n        124,\n        56,\n        1,\n        101,\n        100,\n        13,\n        16,\n        60,\n        30,\n        33,\n        29,\n        55,\n        4,\n        50,\n        7,\n        59,\n        24,\n        22,\n        21,\n        23,\n        18,\n        32,\n        27,\n        26,\n        49\n      ],\n      \"has_message\": true,\n      \"has_unreconciled_entries\": false,\n      \"hours_last_month\": 90.94,\n      \"hours_last_month_display\": \"90.94\",\n      \"hr_icon_display\": \"presence_present\",\n      \"hr_presence_state\": \"present\",\n      \"id\": 2,\n      \"identification_id\": \"\",\n      \"im_status\": \"offline\",\n      \"image_1024\": false,\n      \"image_128\": false,\n      \"image_1920\": false,\n      \"image_256\": false,\n      \"image_512\": false,\n      \"industry_id\": false,\n      \"invoice_ids\": [],\n      \"invoice_warn\": \"no-message\",\n      \"invoice_warn_msg\": false,\n      \"is_absent\": false,\n      \"is_blacklisted\": false,\n      \"is_company\": false,\n      \"is_public\": false,\n      \"is_published\": false,\n      \"is_system\": true,\n      \"job_title\": \"Field Manager\",\n      \"journal_item_count\": 0,\n      \"km_home_work\": 0,\n      \"lang\": \"en_US\",\n      \"last_activity\": false,\n      \"last_activity_time\": false,\n      \"last_check_in\": \"2025-03-11 06:03:53\",\n      \"last_check_out\": false,\n      \"last_time_entries_checked\": false,\n      \"leave_date_to\": false,\n      \"leave_manager_id\": [\n        6,\n        \"mark\"\n      ],\n      \"log_ids\": [\n        22\n      ],\n      \"login\": \"mark\",\n      \"login_date\": \"2025-03-11 05:17:31\",\n      \"marital\": \"single\",\n      \"meeting_count\": 4,\n      \"meeting_ids\": [\n        6,\n        12,\n        7,\n        1\n      ],\n      \"message_attachment_count\": 0,\n      \"message_bounce\": 0,\n      \"message_follower_ids\": [],\n      \"message_has_error\": false,\n      \"message_has_error_counter\": 0,\n      \"message_has_sms_error\": false,\n      \"message_ids\": [\n        1324\n      ],\n      \"message_is_follower\": false,\n      \"message_needaction\": false,\n      \"message_needaction_counter\": 0,\n      \"message_partner_ids\": [],\n      \"mobile\": \"0402851235\",\n      \"mobile_blacklisted\": false,\n      \"mobile_phone\": \"0402851235\",\n      \"my_activity_date_deadline\": false,\n      \"name\": \"Updated Partner via API Test **********\",\n      \"new_password\": \"\",\n      \"notification_type\": \"email\",\n      \"oauth1_session_ids\": [],\n      \"oauth2_session_ids\": [],\n      \"odoobot_failed\": false,\n      \"odoobot_state\": \"onboarding_emoji\",\n      \"parent_id\": false,\n      \"parent_name\": false,\n      \"partner_gid\": 0,\n      \"partner_id\": [\n        3,\n        \"Updated Partner via API Test **********\"\n      ],\n      \"partner_latitude\": 0.0,\n      \"partner_longitude\": 0.0,\n      \"partner_share\": false,\n      \"passport_id\": \"\",\n      \"password\": \"\",\n      \"payment_token_count\": 0,\n      \"payment_token_ids\": [],\n      \"peppol_eas\": false,\n      \"peppol_endpoint\": false,\n      \"permit_no\": false,\n      \"phone\": \"******-272-6209\",\n      \"phone_blacklisted\": false,\n      \"phone_mobile_search\": false,\n      \"phone_sanitized\": \"+***********\",\n      \"phone_sanitized_blacklisted\": false,\n      \"pin\": \"111\",\n      \"pinned_apps\": [],\n      \"place_of_birth\": \"\",\n      \"plan_to_change_bike\": false,\n      \"plan_to_change_car\": false,\n      \"private_city\": \"Khlong Tan\",\n      \"private_country_id\": [\n        217,\n        \"Thailand\"\n      ],\n      \"private_email\": \"\",\n      \"private_lang\": false,\n      \"private_phone\": \"\",\n      \"private_state_id\": false,\n      \"private_street\": \"55 Soi Sukhumvit 26\",\n      \"private_street2\": \"\",\n      \"private_zip\": \"\",\n      \"project_ids\": [],\n      \"property_account_payable_id\": [\n        14,\n        \"211000 Account Payable\"\n      ],\n      \"property_account_position_id\": false,\n      \"property_account_receivable_id\": [\n        6,\n        \"121000 Account Receivable\"\n      ],\n      \"property_payment_term_id\": false,\n      \"property_product_pricelist\": false,\n      \"property_supplier_payment_term_id\": false,\n      \"rating_ids\": [],\n      \"ref\": false,\n      \"ref_company_ids\": [],\n      \"request_overtime\": true,\n      \"res_users_settings_id\": [\n        1,\n        \"Updated Partner via API Test **********\"\n      ],\n      \"res_users_settings_ids\": [\n        1\n      ],\n      \"resource_calendar_id\": [\n        1,\n        \"Standard 40 hours/week\"\n      ],\n      \"resource_ids\": [\n        1\n      ],\n      \"resume_line_ids\": [],\n      \"rules_count\": 143,\n      \"same_company_registry_partner_id\": false,\n      \"same_vat_partner_id\": false,\n      \"self\": [\n        3,\n        \"Updated Partner via API Test **********\"\n      ],\n      \"share\": false,\n      \"show_credit_limit\": false,\n      \"show_leaves\": true,\n      \"sidebar_type\": \"large\",\n      \"signature\": \"<p style=\\\"margin-bottom: 0px;\\\"><span data-o-mail-quote=\\\"1\\\">-- <br data-o-mail-quote=\\\"1\\\">\\nAdministrator</span></p>\",\n      \"signup_expiration\": false,\n      \"signup_token\": false,\n      \"signup_type\": false,\n      \"signup_url\": \"http://localhost:8069/web/login?db=loneworker&login=mark\",\n      \"signup_valid\": false,\n      \"spouse_birthdate\": false,\n      \"spouse_complete_name\": false,\n      \"ssnid\": false,\n      \"starred_message_ids\": [],\n      \"state\": \"active\",\n      \"state_id\": [\n        1498,\n        \"Bangkok (TH)\"\n      ],\n      \"street\": \"55 Soi Sukhumvit 26\",\n      \"street2\": \"\",\n      \"study_field\": \"\",\n      \"study_school\": \"\",\n      \"supplier_rank\": 0,\n      \"task_count\": 0,\n      \"task_ids\": [],\n      \"title\": [\n        3,\n        \"Mister\"\n      ],\n      \"total_invoiced\": 0.0,\n      \"total_overtime\": 6.74,\n      \"totp_enabled\": false,\n      \"totp_trusted_device_ids\": [],\n      \"trust\": \"normal\",\n      \"type\": \"contact\",\n      \"tz\": \"Asia/Bangkok\",\n      \"tz_offset\": \"+0700\",\n      \"ubl_cii_format\": false,\n      \"use_partner_credit_limit\": false,\n      \"user_group_warning\": false,\n      \"user_id\": false,\n      \"user_ids\": [\n        2\n      ],\n      \"vat\": \"\",\n      \"visa_expire\": false,\n      \"visa_no\": false,\n      \"visitor_ids\": [\n        1\n      ],\n      \"website\": \"http://markshaw.com\",\n      \"website_id\": false,\n      \"website_message_ids\": [],\n      \"website_published\": false,\n      \"website_url\": \"#\",\n      \"work_contact_id\": [\n        3,\n        \"Updated Partner via API Test **********\"\n      ],\n      \"work_email\": \"<EMAIL>\",\n      \"work_location_id\": false,\n      \"work_phone\": \"\",\n      \"write_date\": \"2025-03-06 11:01:41\",\n      \"write_uid\": [\n        1,\n        \"OdooBot\"\n      ],\n      \"zip\": \"10110\"\n    },\n    {\n      \"accesses_count\": 420,\n      \"action_id\": false,\n      \"active\": true,\n      \"active_lang_count\": 1,\n      \"active_partner\": true,\n      \"activity_calendar_event_id\": false,\n      \"activity_date_deadline\": false,\n      \"activity_exception_decoration\": false,\n      \"activity_exception_icon\": false,\n      \"activity_ids\": [],\n      \"activity_state\": false,\n      \"activity_summary\": false,\n      \"activity_type_icon\": false,\n      \"activity_type_id\": false,\n      \"activity_user_id\": false,\n      \"additional_info\": false,\n      \"additional_note\": false,\n      \"address_id\": [\n        1,\n        \"My Company\"\n      ],\n      \"allocation_count\": 0.0,\n      \"allocation_display\": \"0\",\n      \"allocation_remaining_display\": \"0\",\n      \"api_key_ids\": [],\n      \"attendance_manager_id\": false,\n      \"attendance_state\": \"checked_in\",\n      \"avatar_1024\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==\",\n      \"avatar_128\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==\",\n      \"avatar_1920\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==\",\n      \"avatar_256\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==\",\n      \"avatar_512\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==\",\n      \"bank_account_count\": 0,\n      \"bank_ids\": [],\n      \"barcode\": false,\n      \"birthday\": false,\n      \"calendar_last_notif_ack\": \"2025-03-07 06:11:58\",\n      \"can_edit\": true,\n      \"can_publish\": true,\n      \"category_id\": [],\n      \"category_ids\": [],\n      \"certificate\": \"other\",\n      \"channel_ids\": [\n        1,\n        3,\n        4,\n        5\n      ],\n      \"child_ids\": [],\n      \"children\": 0,\n      \"city\": false,\n      \"coach_id\": [\n        1,\n        \"Administrator\"\n      ],\n      \"color\": 0,\n      \"comment\": false,\n      \"commercial_company_name\": false,\n      \"commercial_partner_id\": [\n        10,\n        \"mark\"\n      ],\n      \"companies_count\": 1,\n      \"company_id\": [\n        1,\n        \"My Company\"\n      ],\n      \"company_ids\": [\n        1\n      ],\n      \"company_name\": false,\n      \"company_registry\": false,\n      \"company_type\": \"person\",\n      \"complete_name\": \"mark\",\n      \"contact_address\": \"\\n\\n  \\n\",\n      \"contact_address_inline\": \"\",\n      \"contract_ids\": [],\n      \"country_code\": false,\n      \"country_id\": false,\n      \"country_of_birth\": false,\n      \"create_date\": \"2025-03-07 06:11:58\",\n      \"create_employee\": false,\n      \"create_employee_id\": false,\n      \"create_uid\": [\n        2,\n        \"Updated Partner via API Test **********\"\n      ],\n      \"credit\": 0.0,\n      \"credit_limit\": 0.0,\n      \"credit_to_invoice\": 0.0,\n      \"currency_id\": [\n        1,\n        \"USD\"\n      ],\n      \"current_leave_state\": false,\n      \"customer_rank\": 0,\n      \"date\": false,\n      \"days_sales_outstanding\": 0.0,\n      \"debit\": 0.0,\n      \"debit_limit\": 0.0,\n      \"department_id\": false,\n      \"display_extra_hours\": true,\n      \"display_name\": \"mark\",\n      \"duplicated_bank_account_partners_count\": 0,\n      \"email\": \"<EMAIL>\",\n      \"email_formatted\": \"\\\"mark\\\" <<EMAIL>>\",\n      \"email_normalized\": \"<EMAIL>\",\n      \"emergency_contact\": false,\n      \"emergency_phone\": false,\n      \"employee\": false,\n      \"employee_bank_account_id\": false,\n      \"employee_cars_count\": 0,\n      \"employee_count\": 1,\n      \"employee_country_id\": false,\n      \"employee_id\": [\n        5,\n        \"mark\"\n      ],\n      \"employee_ids\": [\n        5\n      ],\n      \"employee_parent_id\": [\n        1,\n        \"Administrator\"\n      ],\n      \"employee_resource_calendar_id\": [\n        1,\n        \"Standard 40 hours/week\"\n      ],\n      \"employee_skill_ids\": [],\n      \"employee_type\": \"employee\",\n      \"employees_count\": 1,\n      \"expense_manager_id\": [\n        2,\n        \"Updated Partner via API Test **********\"\n      ],\n      \"fiscal_country_codes\": \"US\",\n      \"function\": false,\n      \"gender\": false,\n      \"groups_count\": 33,\n      \"groups_id\": [\n        8,\n        17,\n        31,\n        28,\n        34,\n        52,\n        61,\n        19,\n        51,\n        42,\n        44,\n        3,\n        9,\n        56,\n        1,\n        100,\n        13,\n        16,\n        60,\n        30,\n        33,\n        55,\n        50,\n        7,\n        59,\n        24,\n        22,\n        21,\n        23,\n        18,\n        32,\n        27,\n        26\n      ],\n      \"has_message\": true,\n      \"has_unreconciled_entries\": false,\n      \"hours_last_month\": 0.0,\n      \"hours_last_month_display\": \"0\",\n      \"hr_icon_display\": \"presence_present\",\n      \"hr_presence_state\": \"present\",\n      \"id\": 6,\n      \"identification_id\": false,\n      \"im_status\": \"offline\",\n      \"image_1024\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==\",\n      \"image_128\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==\",\n      \"image_1920\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==\",\n      \"image_256\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==\",\n      \"image_512\": \"PD94bWwgdmVyc2lvbj0nMS4wJyBlbmNvZGluZz0nVVRGLTgnID8+PHN2ZyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayc+PHJlY3QgZmlsbD0naHNsKDIxNiwgNjIlLCA0NSUpJyBoZWlnaHQ9JzE4MCcgd2lkdGg9JzE4MCcvPjx0ZXh0IGZpbGw9JyNmZmZmZmYnIGZvbnQtc2l6ZT0nOTYnIHRleHQtYW5jaG9yPSdtaWRkbGUnIHg9JzkwJyB5PScxMjUnIGZvbnQtZmFtaWx5PSdzYW5zLXNlcmlmJz5NPC90ZXh0Pjwvc3ZnPg==\",\n      \"industry_id\": false,\n      \"invoice_ids\": [],\n      \"invoice_warn\": \"no-message\",\n      \"invoice_warn_msg\": false,\n      \"is_absent\": false,\n      \"is_blacklisted\": false,\n      \"is_company\": false,\n      \"is_public\": false,\n      \"is_published\": false,\n      \"is_system\": true,\n      \"job_title\": false,\n      \"journal_item_count\": 0,\n      \"km_home_work\": 0,\n      \"lang\": \"en_US\",\n      \"last_activity\": false,\n      \"last_activity_time\": false,\n      \"last_check_in\": \"2025-03-11 03:37:09\",\n      \"last_check_out\": false,\n      \"last_time_entries_checked\": false,\n      \"leave_date_to\": false,\n      \"leave_manager_id\": [\n        2,\n        \"Updated Partner via API Test **********\"\n      ],\n      \"log_ids\": [\n        23\n      ],\n      \"login\": \"<EMAIL>\",\n      \"login_date\": \"2025-03-11 05:37:06\",\n      \"marital\": \"single\",\n      \"meeting_count\": 5,\n      \"meeting_ids\": [\n        11,\n        5,\n        4,\n        3,\n        2\n      ],\n      \"message_attachment_count\": 0,\n      \"message_bounce\": 0,\n      \"message_follower_ids\": [\n        43\n      ],\n      \"message_has_error\": false,\n      \"message_has_error_counter\": 0,\n      \"message_has_sms_error\": false,\n      \"message_ids\": [\n        122\n      ],\n      \"message_is_follower\": true,\n      \"message_needaction\": false,\n      \"message_needaction_counter\": 0,\n      \"message_partner_ids\": [\n        3\n      ],\n      \"mobile\": false,\n      \"mobile_blacklisted\": false,\n      \"mobile_phone\": false,\n      \"my_activity_date_deadline\": false,\n      \"name\": \"mark\",\n      \"new_password\": \"\",\n      \"notification_type\": \"email\",\n      \"oauth1_session_ids\": [],\n      \"oauth2_session_ids\": [],\n      \"odoobot_failed\": false,\n      \"odoobot_state\": \"onboarding_emoji\",\n      \"parent_id\": false,\n      \"parent_name\": false,\n      \"partner_gid\": 0,\n      \"partner_id\": [\n        10,\n        \"mark\"\n      ],\n      \"partner_latitude\": 0.0,\n      \"partner_longitude\": 0.0,\n      \"partner_share\": false,\n      \"passport_id\": false,\n      \"password\": \"\",\n      \"payment_token_count\": 0,\n      \"payment_token_ids\": [],\n      \"peppol_eas\": false,\n      \"peppol_endpoint\": false,\n      \"permit_no\": false,\n      \"phone\": false,\n      \"phone_blacklisted\": false,\n      \"phone_mobile_search\": false,\n      \"phone_sanitized\": false,\n      \"phone_sanitized_blacklisted\": false,\n      \"pin\": false,\n      \"pinned_apps\": [],\n      \"place_of_birth\": false,\n      \"plan_to_change_bike\": false,\n      \"plan_to_change_car\": false,\n      \"private_city\": false,\n      \"private_country_id\": false,\n      \"private_email\": false,\n      \"private_lang\": false,\n      \"private_phone\": false,\n      \"private_state_id\": false,\n      \"private_street\": false,\n      \"private_street2\": false,\n      \"private_zip\": false,\n      \"project_ids\": [],\n      \"property_account_payable_id\": [\n        14,\n        \"211000 Account Payable\"\n      ],\n      \"property_account_position_id\": false,\n      \"property_account_receivable_id\": [\n        6,\n        \"121000 Account Receivable\"\n      ],\n      \"property_payment_term_id\": false,\n      \"property_product_pricelist\": false,\n      \"property_supplier_payment_term_id\": false,\n      \"rating_ids\": [],\n      \"ref\": false,\n      \"ref_company_ids\": [],\n      \"request_overtime\": false,\n      \"res_users_settings_id\": [\n        2,\n        \"mark\"\n      ],\n      \"res_users_settings_ids\": [\n        2\n      ],\n      \"resource_calendar_id\": [\n        1,\n        \"Standard 40 hours/week\"\n      ],\n      \"resource_ids\": [\n        16\n      ],\n      \"resume_line_ids\": [\n        4\n      ],\n      \"rules_count\": 124,\n      \"same_company_registry_partner_id\": false,\n      \"same_vat_partner_id\": false,\n      \"self\": [\n        10,\n        \"mark\"\n      ],\n      \"share\": false,\n      \"show_credit_limit\": false,\n      \"show_leaves\": true,\n      \"sidebar_type\": \"large\",\n      \"signature\": \"<p data-o-mail-quote=\\\"1\\\">--<br data-o-mail-quote=\\\"1\\\">mark</p>\",\n      \"signup_expiration\": false,\n      \"signup_token\": false,\n      \"signup_type\": false,\n      \"signup_url\": \"http://localhost:8069/web/login?db=loneworker&login=mark%40markshaw.com\",\n      \"signup_valid\": false,\n      \"spouse_birthdate\": false,\n      \"spouse_complete_name\": false,\n      \"ssnid\": false,\n      \"starred_message_ids\": [],\n      \"state\": \"active\",\n      \"state_id\": false,\n      \"street\": false,\n      \"street2\": false,\n      \"study_field\": false,\n      \"study_school\": false,\n      \"supplier_rank\": 0,\n      \"task_count\": 0,\n      \"task_ids\": [],\n      \"title\": false,\n      \"total_invoiced\": 0.0,\n      \"total_overtime\": 0.0,\n      \"totp_enabled\": false,\n      \"totp_trusted_device_ids\": [],\n      \"trust\": \"normal\",\n      \"type\": \"contact\",\n      \"tz\": \"Asia/Bangkok\",\n      \"tz_offset\": \"+0700\",\n      \"ubl_cii_format\": false,\n      \"use_partner_credit_limit\": false,\n      \"user_group_warning\": false,\n      \"user_id\": false,\n      \"user_ids\": [\n        6\n      ],\n      \"vat\": false,\n      \"visa_expire\": false,\n      \"visa_no\": false,\n      \"visitor_ids\": [\n        9\n      ],\n      \"website\": false,\n      \"website_id\": false,\n      \"website_message_ids\": [],\n      \"website_published\": false,\n      \"website_url\": \"#\",\n      \"work_contact_id\": [\n        10,\n        \"mark\"\n      ],\n      \"work_email\": \"<EMAIL>\",\n      \"work_location_id\": false,\n      \"work_phone\": false,\n      \"write_date\": \"2025-03-07 09:56:38\",\n      \"write_uid\": [\n        6,\n        \"mark\"\n      ],\n      \"zip\": false\n    }\n  ],\n  \"website_id_expanded\": {\n    \"company_id\": [\n      1,\n      \"My Company\"\n    ],\n    \"default_lang_id\": [\n      1,\n      \"English (US)\"\n    ],\n    \"display_name\": \"My Website\",\n    \"id\": 1,\n    \"language_ids\": [\n      1\n    ],\n    \"name\": \"My Website\",\n    \"user_id\": [\n      4,\n      \"Public user\"\n    ]\n  }\n},\n    // Add more records as needed\n  ];\n  return (\n    <FlatList\n      data={companys}\n      renderItem={({ item }) => (\n        <TouchableOpacity \n          style={styles.item}\n          onPress={() => navigation.navigate('RescompanyDetails', { record: item })}>\n          <Text style={styles.itemText}>{item.name || item.display_name || `Record #${item.id}`}</Text>\n        </TouchableOpacity>\n      )}\n      keyExtractor={(item) => item.id.toString()}\n    />\n  );\n};\n\nconst styles = StyleSheet.create({\n  item: {\n    padding: 15,\n    borderBottomWidth: 1,\n    borderBottomColor: '#eee',\n  },\n  itemText: {\n    fontSize: 16,\n  },\n});\n\nexport default RescompanyListScreen;"}, "notes": ["Check for False in account_journal_early_pay_discount_gain_account_id to avoid 'bool object is not subscriptable' errors.", "Check for False in expense_product_id to avoid 'bool object is not subscriptable' errors.", "Check for False in currency_exchange_journal_id to avoid 'bool object is not subscriptable' errors.", "Check for False in expense_currency_exchange_account_id to avoid 'bool object is not subscriptable' errors.", "Check for False in website_id to avoid 'bool object is not subscriptable' errors.", "Limit the number of records fetched for bank_ids to avoid overloading the UI.", "Limit the number of records fetched for account_enabled_tax_country_ids to avoid overloading the UI.", "Check for False in account_discount_expense_allocation_id to avoid 'bool object is not subscriptable' errors.", "Check for False in expense_journal_id to avoid 'bool object is not subscriptable' errors.", "Check for False in default_cash_difference_income_account_id to avoid 'bool object is not subscriptable' errors.", "Limit the number of records fetched for all_child_ids to avoid overloading the UI.", "Check for False in income_currency_exchange_account_id to avoid 'bool object is not subscriptable' errors.", "Optimize logo (binary) to prevent performance issues on mobile.", "Check for False in leave_timesheet_task_id to avoid 'bool object is not subscriptable' errors.", "Check for False in account_sale_tax_id to avoid 'bool object is not subscriptable' errors.", "Check for False in project_time_mode_id to avoid 'bool object is not subscriptable' errors.", "Check for False in account_journal_payment_debit_account_id to avoid 'bool object is not subscriptable' errors.", "Check for False in country_id to avoid 'bool object is not subscriptable' errors.", "Check for False in account_discount_income_allocation_id to avoid 'bool object is not subscriptable' errors.", "Check for False in resource_calendar_id to avoid 'bool object is not subscriptable' errors.", "Check for False in account_purchase_tax_id to avoid 'bool object is not subscriptable' errors.", "Check for False in alias_domain_id to avoid 'bool object is not subscriptable' errors.", "Limit the number of records fetched for website_message_ids to avoid overloading the UI.", "Check for False in account_cash_basis_base_account_id to avoid 'bool object is not subscriptable' errors.", "Check for False in write_uid to avoid 'bool object is not subscriptable' errors.", "Check for False in account_journal_suspense_account_id to avoid 'bool object is not subscriptable' errors.", "Optimize layout_background_image (binary) to prevent performance issues on mobile.", "Check for False in timesheet_encode_uom_id to avoid 'bool object is not subscriptable' errors.", "Limit the number of records fetched for parent_ids to avoid overloading the UI.", "Limit the number of records fetched for company_expense_allowed_payment_method_line_ids to avoid overloading the UI.", "Check for False in tax_cash_basis_journal_id to avoid 'bool object is not subscriptable' errors.", "Check for False in account_fiscal_country_id to avoid 'bool object is not subscriptable' errors.", "Check for False in account_default_pos_receivable_account_id to avoid 'bool object is not subscriptable' errors.", "Check for False in nomenclature_id to avoid 'bool object is not subscriptable' errors.", "Check for False in root_id to avoid 'bool object is not subscriptable' errors.", "Check for False in transfer_account_id to avoid 'bool object is not subscriptable' errors.", "Check for False in currency_id to avoid 'bool object is not subscriptable' errors.", "Limit the number of records fetched for fiscal_position_ids to avoid overloading the UI.", "Limit the number of records fetched for multi_vat_foreign_country_ids to avoid overloading the UI.", "Optimize logo_web (binary) to prevent performance issues on mobile.", "Limit the number of records fetched for message_ids to avoid overloading the UI.", "Check for False in revenue_accrual_account_id to avoid 'bool object is not subscriptable' errors.", "Limit the number of records fetched for user_ids to avoid overloading the UI.", "Check for False in partner_id to avoid 'bool object is not subscriptable' errors.", "Check for False in account_opening_journal_id to avoid 'bool object is not subscriptable' errors.", "Limit the number of records fetched for message_partner_ids to avoid overloading the UI.", "Check for False in state_id to avoid 'bool object is not subscriptable' errors.", "Check for False in automatic_entry_default_journal_id to avoid 'bool object is not subscriptable' errors.", "Check for False in external_report_layout_id to avoid 'bool object is not subscriptable' errors.", "Check for False in account_journal_early_pay_discount_loss_account_id to avoid 'bool object is not subscriptable' errors.", "Check for False in parent_id to avoid 'bool object is not subscriptable' errors.", "Check for False in account_journal_payment_credit_account_id to avoid 'bool object is not subscriptable' errors.", "Limit the number of records fetched for child_ids to avoid overloading the UI.", "Limit the number of records fetched for rating_ids to avoid overloading the UI.", "Check for False in paperformat_id to avoid 'bool object is not subscriptable' errors.", "Optimize appbar_image (binary) to prevent performance issues on mobile.", "Limit the number of records fetched for resource_calendar_ids to avoid overloading the UI.", "Check for False in create_uid to avoid 'bool object is not subscriptable' errors.", "Limit the number of records fetched for bank_journal_ids to avoid overloading the UI.", "Check for False in account_opening_move_id to avoid 'bool object is not subscriptable' errors.", "Check for False in expense_accrual_account_id to avoid 'bool object is not subscriptable' errors.", "Check for False in internal_project_id to avoid 'bool object is not subscriptable' errors.", "Check for False in incoterm_id to avoid 'bool object is not subscriptable' errors.", "Limit the number of records fetched for message_follower_ids to avoid overloading the UI.", "Check for False in default_cash_difference_expense_account_id to avoid 'bool object is not subscriptable' errors."]}