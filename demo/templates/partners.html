{% extends "layout.html" %}

{% block title %}Active Partners{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-md-6">
        <form class="d-flex" id="searchForm">
            <input class="form-control me-2" type="search" placeholder="Search partners..." id="searchInput">
            <button class="btn btn-outline-primary" type="submit">Search</button>
        </form>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ url_for('archived_partners') }}" class="btn btn-secondary me-2">
            <i class="bi bi-archive"></i> Archived Partners
        </a>
        <a href="{{ url_for('create_partner') }}" class="btn btn-primary">
            <i class="bi bi-person-plus"></i> Add Partner
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between">
            <h5 class="mb-0">Partner List</h5>
            <span>Total: {{ total_partners }}</span>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover table-striped mb-0">
                <thead class="table-light">
                    <tr>
                        <th scope="col">Image</th>
                        <th scope="col">Name</th>
                        <th scope="col">Email</th>
                        <th scope="col">Phone</th>
                        <th scope="col">Mobile</th>
                        <th scope="col">Type</th>
                        <th scope="col">Actions</th>
                    </tr>
                </thead>
                <tbody id="partnerTableBody">
                    {% if partners %}
                        {% for partner in partners %}
                        <tr>
                            <td>
                                {% if partner.image_128 %}
                                    <img src="data:image/png;base64,{{ partner.image_128 }}" alt="{{ partner.name }}" class="partner-image">
                                {% else %}
                                    <div class="partner-image bg-secondary d-flex align-items-center justify-content-center text-white">
                                        <i class="bi bi-person"></i>
                                    </div>
                                {% endif %}
                            </td>
                            <td>{{ partner.name }}</td>
                            <td>{{ partner.email or '-' }}</td>
                            <td>{{ partner.phone or '-' }}</td>
                            <td>{{ partner.mobile or '-' }}</td>
                            <td>
                                {% if partner.is_company %}
                                    <span class="badge bg-primary">Company</span>
                                {% else %}
                                    <span class="badge bg-secondary">Individual</span>
                                {% endif %}
                                
                                {% if partner.parent_id %}
                                    <span class="badge bg-info">Contact of {{ partner.parent_id[1] }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('view_partner', partner_id=partner.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ url_for('edit_partner', partner_id=partner.id) }}" class="btn btn-sm btn-outline-secondary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ partner.id }}">
                                        <i class="bi bi-archive"></i>
                                    </button>
                                </div>
                                
                                <!-- Archive Modal -->
                                <div class="modal fade" id="deleteModal{{ partner.id }}" tabindex="-1" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Confirm Archive</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                Are you sure you want to archive <strong>{{ partner.name }}</strong>?
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <form action="{{ url_for('delete_partner', partner_id=partner.id) }}" method="post">
                                                    <button type="submit" class="btn btn-danger">Archive</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="7" class="text-center py-3">No partners found</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <nav>
            <ul class="pagination justify-content-center mb-0">
                {% if page > 1 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('partners', page=page-1) }}">Previous</a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#">Previous</a>
                    </li>
                {% endif %}
                
                {% for p in range(1, total_pages + 1) %}
                    <li class="page-item {% if p == page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('partners', page=p) }}">{{ p }}</a>
                    </li>
                {% endfor %}
                
                {% if page < total_pages %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('partners', page=page+1) }}">Next</a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#">Next</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        $('#searchForm').on('submit', function(e) {
            e.preventDefault();
            const searchTerm = $('#searchInput').val().trim();
            
            if (searchTerm) {
                $.ajax({
                    url: "{{ url_for('search_partners') }}",
                    type: "GET",
                    data: {
                        term: searchTerm
                    },
                    success: function(data) {
                        let html = '';
                        
                        if (data.length > 0) {
                            data.forEach(function(partner) {
                                html += `
                                <tr>
                                    <td>
                                        ${partner.image_128 ? 
                                            `<img src="data:image/png;base64,${partner.image_128}" alt="${partner.name}" class="partner-image">` : 
                                            `<div class="partner-image bg-secondary d-flex align-items-center justify-content-center text-white">
                                                <i class="bi bi-person"></i>
                                            </div>`
                                        }
                                    </td>
                                    <td>${partner.name}</td>
                                    <td>${partner.email || '-'}</td>
                                    <td>${partner.phone || '-'}</td>
                                    <td>${partner.mobile || '-'}</td>
                                    <td>
                                        ${partner.is_company ? 
                                            `<span class="badge bg-primary">Company</span>` : 
                                            `<span class="badge bg-secondary">Individual</span>`
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="/partners/${partner.id}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="/partners/${partner.id}/edit" class="btn btn-sm btn-outline-secondary">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal${partner.id}">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                `;
                            });
                        } else {
                            html = `<tr><td colspan="7" class="text-center py-3">No partners found for "${searchTerm}"</td></tr>`;
                        }
                        
                        $('#partnerTableBody').html(html);
                    }
                });
            }
        });
    });
</script>
{% endblock %}