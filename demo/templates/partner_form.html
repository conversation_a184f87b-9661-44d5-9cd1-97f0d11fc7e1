{% extends "layout.html" %}

{% block title %}
    {% if partner %}Edit Partner{% else %}Add Partner{% endif %}
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">{% if partner %}Edit Partner: {{ partner.name }}{% else %}Add New Partner{% endif %}</h5>
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6">
                    <h6>General Information</h6>
                    <hr>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Name *</label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ partner.name if partner else '' }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_company" name="is_company" {% if partner and partner.is_company %}checked{% endif %}>
                            <label class="form-check-label" for="is_company">
                                Is a Company
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ partner.email if partner else '' }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="phone" name="phone" value="{{ partner.phone if partner else '' }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="mobile" class="form-label">Mobile</label>
                        <input type="tel" class="form-control" id="mobile" name="mobile" value="{{ partner.mobile if partner else '' }}">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h6>Address Information</h6>
                    <hr>
                    
                    <div class="mb-3">
                        <label for="street" class="form-label">Street</label>
                        <input type="text" class="form-control" id="street" name="street" value="{{ partner.street if partner else '' }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="street2" class="form-label">Street 2</label>
                        <input type="text" class="form-control" id="street2" name="street2" value="{{ partner.street2 if partner else '' }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="city" class="form-label">City</label>
                        <input type="text" class="form-control" id="city" name="city" value="{{ partner.city if partner else '' }}">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="zip" class="form-label">ZIP</label>
                            <input type="text" class="form-control" id="zip" name="zip" value="{{ partner.zip if partner else '' }}">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between mt-4">
                <a href="{{ url_for('partners') }}" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">
                    {% if partner %}Update{% else %}Create{% endif %} Partner
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}