{% extends "layout.html" %}

{% block title %}Partner Details{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ partner.name }}</h5>
            <div>
                <a href="{{ url_for('edit_partner', partner_id=partner.id) }}" class="btn btn-primary">
                    <i class="bi bi-pencil"></i> Edit
                </a>
                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                    <i class="bi bi-trash"></i> Delete
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3 text-center">
                {% if partner.image_1920 %}
                    <img src="data:image/png;base64,{{ partner.image_1920 }}" alt="{{ partner.name }}" class="partner-detail-image mb-3">
                {% else %}
                    <div class="partner-detail-image bg-secondary d-flex align-items-center justify-content-center text-white mx-auto mb-3">
                        <i class="bi bi-person" style="font-size: 4rem;"></i>
                    </div>
                {% endif %}
                
                <div class="mb-3">
                    {% if partner.is_company %}
                        <span class="badge bg-primary">Company</span>
                    {% else %}
                        <span class="badge bg-secondary">Individual</span>
                    {% endif %}
                    
                    {% if partner.parent_id %}
                        <div class="mt-2">
                            <span class="badge bg-info">Contact of {{ partner.parent_id[1] }}</span>
                        </div>
                    {% endif %}
                </div>
                
                {% if partner.category_id %}
                    <div class="mb-3">
                        <h6>Tags</h6>
                        {% for category in partner.category_id %}
                            <span class="badge bg-secondary">{{ category[1] }}</span>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <div class="row g-3">
                    <div class="col-md-6">
                        <h6>Contact Information</h6>
                        <hr>
                        <div class="mb-2">
                            <strong><i class="bi bi-envelope"></i> Email:</strong>
                            {% if partner.email %}
                                <a href="mailto:{{ partner.email }}">{{ partner.email }}</a>
                            {% else %}
                                <span class="text-muted">Not provided</span>
                            {% endif %}
                        </div>
                        <div class="mb-2">
                            <strong><i class="bi bi-telephone"></i> Phone:</strong>
                            {% if partner.phone %}
                                <a href="tel:{{ partner.phone }}">{{ partner.phone }}</a>
                            {% else %}
                                <span class="text-muted">Not provided</span>
                            {% endif %}
                        </div>
                        <div class="mb-2">
                            <strong><i class="bi bi-phone"></i> Mobile:</strong>
                            {% if partner.mobile %}
                                <a href="tel:{{ partner.mobile }}">{{ partner.mobile }}</a>
                            {% else %}
                                <span class="text-muted">Not provided</span>
                            {% endif %}
                        </div>
                        <div class="mb-2">
                            <strong><i class="bi bi-globe"></i> Website:</strong>
                            {% if partner.website %}
                                <a href="{{ partner.website }}" target="_blank">{{ partner.website }}</a>
                            {% else %}
                                <span class="text-muted">Not provided</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>Address Information</h6>
                        <hr>
                        <address>
                            {% if partner.street %}{{ partner.street }}<br>{% endif %}
                            {% if partner.street2 %}{{ partner.street2 }}<br>{% endif %}
                            {% if partner.city %}{{ partner.city }}{% endif %}
                            {% if partner.state_id %}, {{ partner.state_id[1] }}{% endif %}
                            {% if partner.zip %} {{ partner.zip }}{% endif %}<br>
                            {% if partner.country_id %}{{ partner.country_id[1] }}{% endif %}
                        </address>
                    </div>
                </div>
                
                {% if partner.comment %}
                <div class="mt-4">
                    <h6>Notes</h6>
                    <hr>
                    <p>{{ partner.comment }}</p>
                </div>
                {% endif %}
                
                {% if partner.child_ids %}
                <div class="mt-4">
                    <h6>Contacts</h6>
                    <hr>
                    <div class="list-group">
                        {% for contact in partner.child_ids %}
                        <a href="{{ url_for('view_partner', partner_id=contact[0]) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            {{ contact[1] }}
                            <span class="badge bg-primary rounded-pill">View</span>
                        </a>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete <strong>{{ partner.name }}</strong>?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ url_for('delete_partner', partner_id=partner.id) }}" method="post">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}